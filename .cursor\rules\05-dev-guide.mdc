---
description:
globs:
alwaysApply: false
---
# 开发指南

## 环境配置
- 基于 Java 8 开发
- 使用 Maven 管理依赖
- 修改 /_appconfig/cicd.yml 设置启动参数

## 多环境配置
- 推荐将区分环境的配置托管到 Toad
- 参考 [Pilot 多环境配置](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/)

## 多数据源配置
- 参考 [Pilot 多数据源配置](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-multiple-dataSource)

## 开发规范
1. 代码结构遵循模块化设计
2. 接口设计遵循 RESTful 规范
3. 异常处理统一使用自定义异常
4. 日志记录使用 SLF4J + Logback

## 部署与运维
- 使用 Pilot 进行应用部署
- 访问 [Pilot OPS](https://coops.qunhequnhe.com/pilot#/ops) 查看应用中间件版本

## 常见问题
参考 [Pilot 常见问题](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-qa)
