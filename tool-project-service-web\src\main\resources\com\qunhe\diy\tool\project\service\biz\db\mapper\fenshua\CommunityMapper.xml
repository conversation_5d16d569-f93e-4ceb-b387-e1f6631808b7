<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ CommunityMapper.xml
  ~ Copyright 2023 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.1//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.CommunityMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `commid`,
        `name`,
        `areaid`,
        `avgprice`,
        `desc`,
        `albumid`,
        `pics`,
        `openingtime`,
        `checkintime`,
        `refurl`,
        `metadata`,
        `unitnum`,
        `lastmodified`,
        `created`,
        `decoration`,
        `propertytype`,
        `floorarearate`,
        `address`,
        `userid`,
        `hiden`,
        `deleted`,
        `urltoken`,
        `checked`,
        `zip_code`,
        `house_number`
    </sql>


    <select id="getCommFullInfos"
            resultType="com.qunhe.diy.tool.project.service.common.param.CommunityInfo" >
        SELECT
            c.commid as commId,
            c.name as commName,
            c.areaid as areaId,
            c.albumid as albumId,
            c.hiden as hidenVal,
            sda.parentareaid as sysparentareaid,
            sda.fullname as sysfullname
        FROM community AS c
                 LEFT JOIN sysdictarea AS sda
                           ON sda.areaid = c.areaid
        WHERE c.commid >= 0 AND c.name = #{name}
          AND (sda.areaid = #{areaId} OR sda.parentareaid = #{areaId})
          AND c.deleted = 0
        ORDER BY commId
            LIMIT 1;
    </select>

    <select id="getDefaultCommunityByAreaId" parameterType="Long"
            resultType="com.qunhe.instdeco.plan.data.Community">
        SELECT
        <include refid="Base_Column_List"/>
        FROM community
        WHERE areaid = #{areaId}
          AND hiden = 1
          AND deleted = 0
            LIMIT 1
    </select>

    <select id="getDefaultCommunity" parameterType="Long"
            resultType="com.qunhe.instdeco.plan.data.Community">
        SELECT
        <include refid="Base_Column_List"/>
        FROM community
        WHERE commid = #{commid}
          AND deleted = 0
    </select>

    <select id="getAreaByCommId" parameterType="Long"
            resultType="com.qunhe.instdeco.plan.data.SysDictArea">
        SELECT a.*
        FROM community AS c
                 LEFT JOIN sysdictarea AS a ON c.areaid = a.areaid
        WHERE c.commid = #{commId}
    </select>

    <select id="getCommunitiesByNameAndAreaId" parameterType="Map"
            resultType="com.qunhe.instdeco.plan.data.Community">
        SELECT
        <include refid="Base_Column_List"/>
        FROM community AS cm
        WHERE cm.`name` = #{name}
        <if test="areaId != null">
            AND `areaid` = #{areaId}
        </if>
    </select>

    <insert id="insertCommunity" parameterType="com.qunhe.instdeco.plan.data.Community"
            useGeneratedKeys="true" keyProperty="commId">
        INSERT INTO community (
        <trim suffixOverrides=",">
            <if test="name != null">`name`,</if>
            <if test="areaId != null">`areaid`,</if>
            <if test="checked != null">checked,</if>
        </trim>
        ) VALUES (
        <trim suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="checked != null">#{checked},</if>
        </trim>
        )
    </insert>

    <select id="getNameById" parameterType="long" resultType="String">
        SELECT cm.name
        FROM community AS cm
        WHERE cm.commid = #{id}
    </select>


</mapper>