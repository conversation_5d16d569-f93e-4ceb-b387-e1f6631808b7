/*
 * MultiLevelRevisionAdvice.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.advice;

import com.qunhe.diy.tool.project.service.biz.annotation.MultiLevelRevision;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.ToolLevelService;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectService;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/27
 */
@Aspect
@Component
@RequiredArgsConstructor
public class MultiLevelRevisionAdvice {

    private static final QHLogger LOG = QHLogger.getLogger(MultiLevelRevision.class);

    private final ToolProjectService toolProjectService;
    private final ToolLevelService toolLevelService;

    @AfterReturning("@annotation(multiLevelRevision)")
    public void afterAspect(JoinPoint joinPoint, MultiLevelRevision multiLevelRevision) {
        final Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();

        // 解析SpringEL
        LocalVariableTableParameterNameDiscoverer discoverer =
                new LocalVariableTableParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);
        SpelExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        Long planId = null;
        if (StringUtils.isNotBlank(multiLevelRevision.planId())) {
            planId = parser.parseExpression(multiLevelRevision.planId())
                    .getValue(context, Long.class);
        }
        Long designId = null;
        if (StringUtils.isNotBlank(multiLevelRevision.designId())) {
            designId = parser.parseExpression(multiLevelRevision.designId())
                    .getValue(context, Long.class);
        }
        String levelId = null;
        if (StringUtils.isNotBlank(multiLevelRevision.levelId())) {
            levelId = parser.parseExpression(multiLevelRevision.levelId())
                    .getValue(context, String.class);
        }

        try {
            ProjectDesign projectDesign;
            if (designId != null) {
                projectDesign = toolProjectService.getProjectDesignByDesignId(designId);
            } else if (planId != null) {
                projectDesign = toolProjectService.getProjectDesignByPlanId(planId);
            } else if (levelId != null) {
                projectDesign = toolProjectService.getProjectDesignByLevelId(levelId);
            } else {
                projectDesign = null;
            }
            if (projectDesign == null) {
                LOG.message("saveMultiLevelRevision - projectDesign is null")
                        .with("planId", planId)
                        .with("designId", designId)
                        .with("levelId", levelId)
                        .warn();
                return;
            }
            if (toolProjectService.needDesignRevision(projectDesign)) {
                // 生成多层历史版本
                final Long userId = UserDb.getUserIdBySession();
                toolLevelService.saveMultiLevelRevision(projectDesign.getDesignId(), userId);
            }
        } catch (Exception e) {
            //异常处理
            LOG.message("MultiLevelRevisionAdvice occurs exception", e)
                    .error();
        }
    }

}
