/*
 * SaaSConfigService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qunhe.assembly.oplog.utils.JsonUtils;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.instdeco.libra.client.AbTestClient;
import com.qunhe.instdeco.libra.service.share.dto.AbTestExperimentConfigDto;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.account.common.data.Relation;
import com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient;
import com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.saas.config.client.data.ConfigItemContent;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade.COOHOM_USER_TAG;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SaaSConfigService {

    private static final QHLogger LOG = QHLogger.getLogger(SaaSConfigService.class);

    public static final Long PROJECT_COUNT_POINT = 10716L;

    public static final String COMMON_CONFIG_FAVOR_ICON = "commonConfig:favorIcon";

    public static final String ALL_TOOL_CONFIG_LANGUAGE_TYPE = "allToolConfig:languageType";

    private static final String RECOVERY_DIY_PERMISSION_NEW_B = "recoveryFourPermission_new_B";
    private static final String RECOVERY_DIY_PERMISSION_B = "recoveryFourPermission_B";

    public static final Map<String, Map<String, BusinessConfigElement>> BUSINESS_CONFIG =
            new HashMap<>(2);

    private final AccessPointClient accessPointClient;

    private final SaasConfigFacade saasConfigFacade;

    private final AbTestClient abTestClient;

    private final com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient
            commercialAccessPointClient;

    private final UserInfoFacade userInfoFacade;

    private final BusinessAccountDb businessAccountDb;

    static {
        final Map<String, BusinessConfigElement> model1 = new HashMap<>(2);
        final BusinessConfigElement elementIcon = new BusinessConfigElement();
        elementIcon.setShow(true);
        elementIcon.setPicUrl("//www.kujiale.com/static/images/favicon.ico");
        model1.put(COMMON_CONFIG_FAVOR_ICON, elementIcon);

        final Map<String, BusinessConfigElement> model2 = new HashMap<>(2);
        final BusinessConfigElement elementLanguage = new BusinessConfigElement();
        elementLanguage.setShow(true);
        elementLanguage.setStatus(0);
        model2.put(ALL_TOOL_CONFIG_LANGUAGE_TYPE, elementLanguage);

        BUSINESS_CONFIG.put(COMMON_CONFIG_FAVOR_ICON, model1);
        BUSINESS_CONFIG.put(ALL_TOOL_CONFIG_LANGUAGE_TYPE, model2);
    }




    @SentinelResource(value = "checkProjectCountPointAccess",
            fallback = "checkProjectCountPointAccessHandler")
    @SneakyThrows(AccessAuthenticatorException.class)
    public Boolean checkProjectCountPointAccess(final Long userId) {
        final CheckAccessibleRequestBody requestBody = new CheckAccessibleRequestBody();
        requestBody.setUserId(userId);
        requestBody.setAccessPoints(Collections.singletonList(PROJECT_COUNT_POINT));
        final List<Long> result = accessPointClient.filterAccessPoint(requestBody);
        // 存在该权限点 那么需要方案数量的校验
        if (!CollectionUtils.isEmpty(result) && result.contains(PROJECT_COUNT_POINT)) {
            LOG.message("AccessPoint check true")
                    .with("userId", userId).with("point", PROJECT_COUNT_POINT).info();
            return true;
        }
        return false;
    }

    public Boolean checkProjectCountPointAccessHandler(final Long userId, final Throwable e) {
        LOG.message("AccessPoint check Block", e).with("userId", userId).error();
        return false;
    }

    @SentinelResource(value = "ServiceCallFacade#getBusinessConfigMap",
            fallback = "getBusinessConfigMapFallBack")
    public BusinessConfigMap getBusinessConfigMap(final Long userId,
            final String itemCode) {
        BusinessConfigMap businessConfigMap = new BusinessConfigMap();

        ConfigItemContent configItemContent = saasConfigFacade.getUserConfig(userId, itemCode);
        if (configItemContent == null || configItemContent.getItemValue() == null) {
            return businessConfigMap;
        }
        businessConfigMap.put(itemCode, JsonUtils.parseString(configItemContent.getItemValue(),
                BusinessConfigElement.class));
        return businessConfigMap;
    }

    public BusinessConfigMap getBusinessConfigMapFallBack(final Long userId,
            final String itemCode) {
        LOG.message("getBusinessConfigMapFallBack")
                .with("userId", userId)
                .with("itemCode", itemCode)
                .warn();
        return convert2BusinessConfig(itemCode);
    }

    private BusinessConfigMap convert2BusinessConfig(final String itemCode) {
        final BusinessConfigMap businessConfigMap = new BusinessConfigMap();
        businessConfigMap.setModel(BUSINESS_CONFIG.get(itemCode));
        return businessConfigMap;
    }

    @SentinelResource(value = "checkBusinessAccessPoint",
            fallback = "checkBusinessAccessPointFallback")
    public Boolean checkBusinessAccessPoint(final Long userId, final Long rootAccountId,
            final Long accountId, final List<Long> accessPoints)
            throws AccessAuthenticatorException {
        final CheckAccessibleRequestBody checkAccessibleRequestBody =
                new CheckAccessibleRequestBody();
        checkAccessibleRequestBody.setAccessPoints(accessPoints);
        checkAccessibleRequestBody.setUserId(userId);
        checkAccessibleRequestBody.setRelation(Relation.AND);
        return commercialAccessPointClient.checkAccessible(checkAccessibleRequestBody);
    }

    public Boolean checkBusinessAccessPointFallback(final Long userId, final Long rootAccountId,
            final Long accountId, final List<Long> accessPoints, Throwable e) {
        LOG.message("checkBusinessAccessPointFallback to false ", e)
                .with("userId", userId)
                .with("rootAccountId", rootAccountId)
                .with("accountId", accountId)
                .with("accessPoints", accessPoints)
                .error();
        return false;
    }

    public Map<String, Boolean> getAbTestResultFallback(final String appKey, final String cookieId,
            final Long userId, final String ip, final BlockException e) {
        LOG.message("getAbTestResultFallback", e)
                .with("appKey", appKey)
                .with("cookieId", cookieId)
                .with("userId", userId)
                .with("ip", ip)
                .warn();
        return Collections.emptyMap();
    }

    @SentinelResource(value = "ServiceCallFacade#getAbTestResult",
            blockHandler = "getAbTestResultFallback")
    public Map<String, String> getAbTestResult(final String appKey, final String cookieId,
            final Long userId, final String ip) {
        final Map<String, AbTestExperimentConfigDto> res =
                abTestClient.getAbTestExperimentConfig(appKey, cookieId, userId, ip);
        if (res == null) {
            return Collections.emptyMap();
        } else {
            return Maps.transformValues(res, v -> {
                if (v != null && v.getOptimized() != null) {
                    return v.getOptimized().toString();
                } else {
                    return "null";
                }
            });
        }
    }

    public boolean checkRecycleDiyAuthWithAbConfig(Long userId, Map<String, String> abTestConfig) {
        final Long rootAccountId = businessAccountDb.getRootAccountIdByUserId(userId);
        if (rootAccountId != null) {
            //coohom商家或者coohom-apic
            if (userInfoFacade.isCoohomUser(userId)) {
                return false;
            } else {
                return checkBusinessRecycleFourBucketAuthWithConfig(userId, abTestConfig);
            }
        } else {
            // 不是coohom则回收
            return org.apache.commons.collections4.CollectionUtils.isEmpty(
                    userInfoFacade.matchUserTags(userId, Sets.newHashSet(COOHOM_USER_TAG)));
        }
    }

    /**
     * recoveryFourPermission_new_B：（商家）回收新建用户的4.0权限，所有环境的分桶已全量，为了兼容商家账号配置和缩小影响范围，暂不下线
     * recoveryFourPermission_B：（商家）回收4.0权限，但内网和beta开放0%，从兼容性来看暂不下线；
     * @param userId
     * @param abTestConfig
     * @return
     */
    private boolean checkBusinessRecycleFourBucketAuthWithConfig(final Long userId,
            final Map<String, String> abTestConfig) {
        String bucketV1 = abTestConfig.get(RECOVERY_DIY_PERMISSION_NEW_B);
        final String bucketV2 = abTestConfig.get(RECOVERY_DIY_PERMISSION_B);
        final boolean result = Boolean.parseBoolean(bucketV1) || Boolean.parseBoolean(bucketV2);
        LOG.message("checkBusinessRecycleFourBucketAuthWithConfig result")
                .with("userId", userId)
                .with("result", result)
                .with("bucketV1", bucketV1)
                .with("bucketV2", bucketV2)
                .info();
        return result;
    }


}
