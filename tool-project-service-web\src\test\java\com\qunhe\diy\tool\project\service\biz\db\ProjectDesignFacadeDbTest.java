/*
 * ProjectDesignFacadeDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import java.util.Arrays;
import java.util.List;

import com.qunhe.log.QHLogger;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.client.project.DynamicProjectClient;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.projectmanagement.client.data.QueryByDesignIdParam;

/**
 * <AUTHOR>
 */
public class ProjectDesignFacadeDbTest {
    @Mock
    QHLogger LOG;
    @Mock
    List<String> COLS;
    @Mock
    DynamicProjectClient dynamicProjectClient;
    @Mock
    ProjectClient projectClient;
    @InjectMocks
    ProjectDesignFacadeDb projectDesignFacadeDb;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateProject() throws Exception {
        try {
            ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                    .designId(35338021L)
                    .userId(1111251217L)
                    .modelDataId("MJRBX7NMDTAY6AABAAAAACQ8")
                    .planId(35338021L)
                    .vrc("V0150R0105")
                    .recommend(false)
                    .reworked(false)
                    .designSaved(false)
                    .build();
            ProjectSequence result = projectDesignFacadeDb.createProject(toolProjectSaveParam);
            Assert.assertEquals("create fail", null, result);
        } catch (Exception e) {
            // skip
            LOG.message("testCreateProject方案创建失败", e);
        }
    }

    @Test
    public void testGetProject() throws Exception {
        QueryByDesignIdParam queryByDesignIdParam = QueryByDesignIdParam.builder()
                .designId(35338021L)
                .build();
        ProjectDesign result = projectDesignFacadeDb.getProject(queryByDesignIdParam);
        Assert.assertEquals("get fail", null, result);
    }

    @Test
    public void testGetProject2() {
        ProjectDesign result = projectDesignFacadeDb.getProject(35338021L);
        Assert.assertEquals(null, null, result);
    }

    @Test
    public void testGetProjectByDesignId() {
        ProjectDesign result = projectDesignFacadeDb.getProjectByDesignId(35338021L);
        Assert.assertEquals(null, null, result);
    }

    @Test
    public void testRecoverProject() {
        Integer result = projectDesignFacadeDb.recoverProject(35338021L);
        Assert.assertEquals(null, Integer.valueOf(0), result);
    }

    @Test
    public void testDeleteProject() {
        int result = projectDesignFacadeDb.deleteProject(35338021L);
        Assert.assertEquals(null, 0, result);
    }

    @Test
    public void testGetPlanId() throws Exception {
        Long result = projectDesignFacadeDb.getPlanId(35338021L);
        Assert.assertEquals(null, Long.valueOf(0), result);
    }

    @Test
    public void testRecoverDeleteProject() {
        int result = projectDesignFacadeDb.recoverDeleteProject(35338021L);
        Assert.assertEquals(null, 0, result);
    }

    @Test
    public void testRecycleDesign() {
        int result = projectDesignFacadeDb.recycleDesign(1111251217L, 35338021L);
        Assert.assertEquals(null, 0, result);
    }

    @Test
    public void testSwitchDesignUser() throws Exception {
        boolean result = projectDesignFacadeDb.switchDesignUser(981501710L, 1111251217L, 35338021L);
        Assert.assertEquals(null, false, result);
    }

    @Test
    public void testGetDesignsByUserId() throws Exception {
        List<Long> result = projectDesignFacadeDb.getDesignsByUserId(1111251217L);
        Assert.assertEquals(null, Arrays.asList(), result);
    }

    @Test
    public void testBatchDeleteProject() {
        final int size = projectDesignFacadeDb.batchDeleteProject(Arrays.asList(35338021L));
        Assert.assertEquals("size equals 0", 0, size);
    }

    @Test
    public void testBatchRollbackRemoveProject() {
        final int size = projectDesignFacadeDb.batchRollbackRemoveProject(Arrays.asList(35338021L));
        Assert.assertEquals("size equals 0", 0, size);
    }

    @Test
    public void getCoohomVrcList() {
        Mockito.when(projectClient.getCoohomVrcList()).thenReturn(Arrays.asList("vrc"));
        List<String> coohomVrcList = projectDesignFacadeDb.getCoohomVrcList();
        Assert.assertEquals("size equals 1", 1, coohomVrcList.size());
    }
}