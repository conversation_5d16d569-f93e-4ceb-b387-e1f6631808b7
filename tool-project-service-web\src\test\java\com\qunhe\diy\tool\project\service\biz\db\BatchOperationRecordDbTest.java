/*
 * BatchOperationRecordDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.BatchOperationRecordMapper;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class BatchOperationRecordDbTest {

    @Mock
    private BatchOperationRecordMapper mockBatchOperationRecordMapper;

    private BatchOperationRecordDb batchOperationRecordDbUnderTest;

    @Before
    public void setUp() throws Exception {
        batchOperationRecordDbUnderTest = new BatchOperationRecordDb(mockBatchOperationRecordMapper);
    }

    @Test
    public void testInsertRecord() {
        // Setup
        final BatchOperationData batchOperationData = BatchOperationData.builder().build();
        when(mockBatchOperationRecordMapper.insertBatchOperationRecord(
                BatchOperationData.builder().build())).thenReturn(0);

        // Run the test
        final int result = batchOperationRecordDbUnderTest.insertRecord(batchOperationData);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testUpdateCompleteCountAndStatus() {
        // Setup
        when(mockBatchOperationRecordMapper.updateCompleteCountAndStatus(0, 0, "recordId")).thenReturn(0);

        // Run the test
        final int result = batchOperationRecordDbUnderTest.updateCompleteCountAndStatus(0, 0, "recordId");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testUpdateBatchOperateFinishInfos() {
        // Setup
        when(mockBatchOperationRecordMapper.updateBatchOperateFinishInfos(0, "link", 0, "recordId")).thenReturn(0);

        // Run the test
        final int result = batchOperationRecordDbUnderTest.updateBatchOperateFinishInfos(0, "link", 0, "recordId");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testUpdateOperationRecord() {
        // Setup
        final BatchOperationData batchOperationData = BatchOperationData.builder().build();
        when(mockBatchOperationRecordMapper.updateOperationRecord(BatchOperationData.builder().build())).thenReturn(0);

        // Run the test
        final int result = batchOperationRecordDbUnderTest.updateOperationRecord(batchOperationData);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGetBatchOperationRecordByRecordId() {
        // Setup
        final BatchOperationData expectedResult = BatchOperationData.builder().build();
        when(mockBatchOperationRecordMapper.getBatchOperationRecordByRecordId("recordId"))
                .thenReturn(BatchOperationData.builder().build());

        // Run the test
        final BatchOperationData result = batchOperationRecordDbUnderTest.getBatchOperationRecordByRecordId("recordId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetMultiOperateLinksByUserId() {
        // Setup
        when(mockBatchOperationRecordMapper.getMultiOperateLinksByUserId(0L, 0)).thenReturn(Collections.singletonList("value"));

        // Run the test
        final List<String> result = batchOperationRecordDbUnderTest.getMultiOperateLinksByUserId(0L, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.singletonList("value"));
    }

    @Test
    public void testGetMultiOperateLinksByUserId_BatchOperationRecordMapperReturnsNoItems() {
        // Setup
        when(mockBatchOperationRecordMapper.getMultiOperateLinksByUserId(0L, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = batchOperationRecordDbUnderTest.getMultiOperateLinksByUserId(0L, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
