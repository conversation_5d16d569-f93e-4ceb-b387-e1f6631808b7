/*
 * CommunityController.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.CommunityService;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectBackendController;
import com.qunhe.diy.tool.project.service.web.utils.RequestUtils;
import com.qunhe.instdeco.plan.annotations.LoginApiInterceptor;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import com.qunhe.log.QHLogger;
import com.qunhe.web.standard.data.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class CommunityController {
    private final static QHLogger LOG = QHLogger.getLogger(
            ToolProjectBackendController.class);

    private final CommunityService communityService;

    @Autowired
    public CommunityController(final CommunityService communityService) {
        this.communityService = communityService;
    }

    @GetMapping(value = ApiConstant.COMMUNITY_GET_OR_CREATE_API)
    public Result<Community> getOrCreateProjectCommunity(
            @RequestParam(value = "province", required = false) final String province,
            @RequestParam(value = "city", required = false) final String city,
            @RequestParam(value = "communityName", required = false) final String communityName) {
        try {
            final Community community = communityService.getOrCreateProjectCommunity(province, city,
                    communityName);
            if (community == null) {
                return Result.error("community is null");
            }
            return Result.ok(community);
        } catch (Exception e) {
            // 包装返回错误信息
            LOG.message("get or create community failed", e)
                    .with("province", province)
                    .with("city", city)
                    .with("communityName", communityName)
                    .error();
            return Result.error("get or create community failed");
        }
    }

    @GetMapping(value = ApiConstant.COMMUNITY_NAME_BY_COMM_ID)
    public String getCommNameByCommId(@RequestParam(value = "commid") final Long commId) {
        return communityService.getCommNameById(commId);
    }

    @GetMapping(value = ApiConstant.SYSDICTAREA_BY_COMM_ID)
    public SysDictArea getSysDictArea(@RequestParam(value = "commid") final Long commId) {
        return communityService.getSysDictArea(commId);
    }

    /**
     * 已废弃，动态省市地区的解析能力通过方案创建时刷写commid来实现，不再依赖上游是否传递areaId来区分场景。
     * 功能描述：areaId为空，返回用户真实ip地址areaId（方案创建场景）；areaId不为空，返回方案历史areaId（编辑历史方案）
     */
    @Deprecated
    @LoginApiInterceptor
    @GetMapping(value = ApiConstant.COMMUNITY_DEFAULT_LOCATION)
    public Result<LocationParam> calculateIpLocation(
            @RequestParam(value = "designid") final Long designId,
            @RequestParam(value = "areaid", required = false) final String areaId,
            final HttpServletRequest request) {
        final Long userId = UserDb.getUserIdBySession();
        final boolean isCoohom = RequestUtils.isCoohom(request);
        return Result.ok(communityService.getLocationParam(isCoohom, designId, userId, areaId));
    }

}
