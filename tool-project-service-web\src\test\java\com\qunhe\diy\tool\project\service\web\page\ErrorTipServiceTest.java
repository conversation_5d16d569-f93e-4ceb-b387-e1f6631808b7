/*
 * ErrorTipServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.assembly.oplog.utils.JsonUtils;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipData;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ErrorTipServiceTest {

    @InjectMocks
    private ErrorTipService errorTipService;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private ToadProperties toadProperties;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Before
    public void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    public void testSave_Success() {
        // Setup
        ErrorTipData errorTipData = new ErrorTipData();
        errorTipData.setErrorTipType(ErrorTipType.DEFAULT);
        errorTipData.setTitleArgs(new Object[]{"title arg"});
        errorTipData.setDescArgs(new Object[]{"desc arg"});
        
        when(toadProperties.getSessionErrorTipExpireMinutes()).thenReturn(5L);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            String expectedJson = "{\"errorTipType\":\"DEFAULT\"}";
            mockedJsonUtils.when(() -> JsonUtils.toString(errorTipData))
                    .thenReturn(expectedJson);
            
            // Execute
            String result = errorTipService.save(errorTipData);
            
            // Verify
            assertNotNull("Result should not be null", result);
            assertTrue("Result should start with prefix", 
                    result.startsWith(ErrorTipService.ERROR_TIP_ID_PREFIX));
            
            verify(valueOperations, times(1)).set(eq(result), eq(expectedJson), eq(5L), eq(TimeUnit.MINUTES));
            verify(toadProperties, times(1)).getSessionErrorTipExpireMinutes();
        }
    }

    @Test
    public void testSave_WithNullData() {
        // Setup
        when(toadProperties.getSessionErrorTipExpireMinutes()).thenReturn(2L);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            String expectedJson = "null";
            mockedJsonUtils.when(() -> JsonUtils.toString(null))
                    .thenReturn(expectedJson);
            
            // Execute
            String result = errorTipService.save(null);
            
            // Verify
            assertNotNull("Result should not be null", result);
            assertTrue("Result should start with prefix", 
                    result.startsWith(ErrorTipService.ERROR_TIP_ID_PREFIX));
            
            verify(valueOperations, times(1)).set(eq(result), eq(expectedJson), eq(2L), eq(TimeUnit.MINUTES));
        }
    }

    @Test
    public void testGet_Success() {
        // Setup
        String key = "errortip_id_12345";
        String jsonData = "{\"errorTipType\":\"DEFAULT\"}";
        ErrorTipData expectedData = new ErrorTipData();
        expectedData.setErrorTipType(ErrorTipType.DEFAULT);
        
        when(valueOperations.get(key)).thenReturn(jsonData);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.parseString(jsonData, ErrorTipData.class))
                    .thenReturn(expectedData);
            
            // Execute
            ErrorTipData result = errorTipService.get(key);
            
            // Verify
            assertNotNull("Result should not be null", result);
            assertEquals("Error tip type should match", ErrorTipType.DEFAULT, result.getErrorTipType());
            
            verify(valueOperations, times(1)).get(key);
        }
    }

    @Test
    public void testGet_NotFound() {
        // Setup
        String key = "errortip_id_nonexistent";
        when(valueOperations.get(key)).thenReturn(null);
        
        // Execute
        ErrorTipData result = errorTipService.get(key);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default error tip data", ErrorTipData.DEFAULT, result);
        
        verify(valueOperations, times(1)).get(key);
    }

    @Test
    public void testGet_EmptyString() {
        // Setup
        String key = "errortip_id_empty";
        when(valueOperations.get(key)).thenReturn("");
        
        // Execute
        ErrorTipData result = errorTipService.get(key);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default error tip data", ErrorTipData.DEFAULT, result);
        
        verify(valueOperations, times(1)).get(key);
    }

    @Test
    public void testGet_JsonParseException() {
        // Setup
        String key = "errortip_id_invalid";
        String invalidJson = "invalid json";
        
        when(valueOperations.get(key)).thenReturn(invalidJson);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.parseString(invalidJson, ErrorTipData.class))
                    .thenThrow(new RuntimeException("JSON parse error"));
            
            // Execute
            ErrorTipData result = errorTipService.get(key);
            
            // Verify - should handle exception gracefully and return default
            assertNotNull("Result should not be null", result);
            assertEquals("Should return default error tip data on parse error", ErrorTipData.DEFAULT, result);
            
            verify(valueOperations, times(1)).get(key);
        }
    }

    @Test
    public void testSave_WithFopSessionConflictType() {
        // Setup
        ErrorTipData errorTipData = new ErrorTipData();
        errorTipData.setErrorTipType(ErrorTipType.FOP_SESSION_CONFLICT);
        errorTipData.setDescArgs(new Object[]{"TestUser"});
        
        when(toadProperties.getSessionErrorTipExpireMinutes()).thenReturn(10L);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            String expectedJson = "{\"errorTipType\":\"FOP_SESSION_CONFLICT\",\"descArgs\":[\"TestUser\"]}";
            mockedJsonUtils.when(() -> JsonUtils.toString(errorTipData))
                    .thenReturn(expectedJson);
            
            // Execute
            String result = errorTipService.save(errorTipData);
            
            // Verify
            assertNotNull("Result should not be null", result);
            assertTrue("Result should start with prefix", 
                    result.startsWith(ErrorTipService.ERROR_TIP_ID_PREFIX));
            
            verify(valueOperations, times(1)).set(eq(result), eq(expectedJson), eq(10L), eq(TimeUnit.MINUTES));
        }
    }

    @Test
    public void testKeyGeneration_IsUnique() {
        // Setup
        ErrorTipData errorTipData = new ErrorTipData();
        when(toadProperties.getSessionErrorTipExpireMinutes()).thenReturn(5L);
        
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toString(any()))
                    .thenReturn("{}");
            
            // Execute
            String result1 = errorTipService.save(errorTipData);
            String result2 = errorTipService.save(errorTipData);
            
            // Verify
            assertNotNull("First result should not be null", result1);
            assertNotNull("Second result should not be null", result2);
            assertNotEquals("Keys should be unique", result1, result2);
            
            assertTrue("First key should start with prefix", 
                    result1.startsWith(ErrorTipService.ERROR_TIP_ID_PREFIX));
            assertTrue("Second key should start with prefix", 
                    result2.startsWith(ErrorTipService.ERROR_TIP_ID_PREFIX));
        }
    }
}
