/*
 * FphFloorplanService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.house.property.floorplan.home.client.FphFloorplanClient;
import com.qunhe.house.property.floorplan.home.common.model.AreaItemDTO;
import com.qunhe.log.QHLogger;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class FphFloorplanService {
    private static final QHLogger LOG = QHLogger.getLogger(FphFloorplanService.class);

    private static final String FHP_IP_TOKEN = "toolDesignSaveIPLocationToken";

    private final FphFloorplanClient fphFloorplanClient;

    public FphFloorplanService(final FphFloorplanClient fphFloorplanClient) {
        this.fphFloorplanClient = fphFloorplanClient;
    }

    @SentinelResource(value = "getIpLocation", blockHandler = "getIpLocationFallBack")
    @SuppressWarnings("PMD.AvoidCatchExceptionInSentinelResource")
    public AreaItemDTO getIpLocation(final String ip) {
        try {
            final Optional<AreaItemDTO> areaItemDTOs = fphFloorplanClient.getIpLocation(ip, FHP_IP_TOKEN);
            if (areaItemDTOs.isPresent()) {
                return areaItemDTOs.get();
            }
        } catch (Exception e) {
            //
            LOG.message("fphFloorplanClient get ipLocation error", e)
                    .error();
        }
        return AreaItemDTO.buildDefault();
    }

    public AreaItemDTO getIpLocationFallBack(final String ip, final BlockException e) {
        LOG.message("getIpLocationFallBack", e)
                .with("currentIp", ip)
                .error();
        return AreaItemDTO.buildDefault();
    }
}
