/*
 * LoggerInterceptor.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.interceptor;

import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.rpc.common.Constants;
import org.slf4j.MDC;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * Created on 2018.10.31.
 */
public class LoggerInterceptor extends HandlerInterceptorAdapter {
    private static final String USER_ID = "user_id";
    private static final String TRACE_ID = "trace_id";

    @Override
    public boolean preHandle(final HttpServletRequest request, final HttpServletResponse response,
            final Object handler) {

        String traceId = request.getHeader(Constants.HEADER_HUNTER_TRACE_ID);
        if (traceId == null) {
            traceId = SpanContextHolder.getTraceId();
        }
        if (traceId != null) {
            MDC.put(TRACE_ID, traceId);
        }
        return true;
    }

    @Override
    public void afterCompletion(final HttpServletRequest request,
            final HttpServletResponse response,
            final Object handler, final Exception ex) {
        MDC.clear();
    }
}
