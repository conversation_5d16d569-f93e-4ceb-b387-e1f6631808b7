/*
 * VrcAppIdDataDb.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.tool.project.service.biz.cache.VrcAppIdCacheData;
import com.qunhe.diy.tool.project.service.biz.constants.CacheNames;
import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.VrcAppIdDataMapper;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.log.QHLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@Service
public class VrcAppIdDataDb {
    private final static QHLogger LOG = QHLogger.getLogger(VrcAppIdDataDb.class);

    @Autowired
    private VrcAppIdDataMapper vrcAppIdDataMapper;


    public String getVrcByVtypeAndAppId(final String vType, final Integer appId,
            final String stage) {
        if (StringUtils.isEmpty(vType) || Objects.isNull(appId) || StringUtils.isEmpty(stage)) {
            LOG.message("empty vtype or appid or stage")
                    .error();
            return null;
        }
        return vrcAppIdDataMapper.getVrcByVtypeAndAppId(vType, appId, stage);
    }


    @CacheEvict(value = CacheNames.ALL_VRC_APP_ID_LIST, key = "#vrcAppIdData.stage")
    public boolean saveVrcAppIdData(final VrcAppIdData vrcAppIdData) {
        if (Objects.isNull(vrcAppIdData)) {
            LOG.message("empty vrcAppIdData when saveVrcAppIdData")
                    .error();
            return false;
        }
        return vrcAppIdDataMapper.saveVrcAppIdData(vrcAppIdData);
    }

    @Cacheable(value = CacheNames.ALL_VRC_APP_ID_LIST, key = "#stage")
    public VrcAppIdCacheData getAllVrcAppIdList(String stage) {
        return new VrcAppIdCacheData(vrcAppIdDataMapper.getAllVrcAppIdList(stage));
    }

    public VrcAppIdData getByVTypeAndAppId(String vType, Integer appId, final String stage) {
        return vrcAppIdDataMapper.getByVTypeAndAppId(vType, appId, stage);
    }

    @CacheEvict(value = CacheNames.ALL_VRC_APP_ID_LIST, key = "#vrcAppIdData.stage")
    public boolean updateVrcAppIdData(final VrcAppIdData vrcAppIdData) {
        return vrcAppIdDataMapper.updateVrcAppIdData(vrcAppIdData) > 0;
    }

    @CacheEvict(value = CacheNames.ALL_VRC_APP_ID_LIST, key = "#stage")
    public boolean deleteVrcAppIdData(String vType, Integer appId, String stage) {
        return vrcAppIdDataMapper.deleteVrcAppIdData(vType, appId, stage);
    }

    @CacheEvict(value = CacheNames.ALL_VRC_APP_ID_LIST, key = "#stage")
    public void clearCache(String stage) {
        LOG.message("clear vrc list cache for stage: " + stage).info();
    }

}
