/*
 * DesignBatchGetRequest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data.restapi;

import com.qunhe.diybe.module.restapi.common.data.request.RestApiRequest;
import com.qunhe.diybe.module.restapi.common.error.Code;
import com.qunhe.diybe.module.restapi.common.error.ErrorDetails;
import com.qunhe.diybe.module.restapi.common.exception.RestApiProcessException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RestApiDesignBatchGetRequest extends RestApiRequest {

    private List<String> ids;

    private String idType;


    /**
     * 用于本身DTO的数据验证
     */
    @Override
    public void validate() throws RestApiProcessException {
        if (idType == null || CollectionUtils.isEmpty(ids)) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT,
                    ErrorDetails.build().withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
                            .withMessage("idType and ids cannot be null"));
        }
    }
}
