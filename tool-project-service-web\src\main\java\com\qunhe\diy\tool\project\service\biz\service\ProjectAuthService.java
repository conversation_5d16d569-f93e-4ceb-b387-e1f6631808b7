/*
 * ProjectAuthService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.ProjectAuthOperationException;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.hunter.constant.TraceKeys;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.instdeco.plan.site.constants.SiteConstants;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.data.TransferErrorCode;
import com.qunhe.project.platform.project.auth.data.UserAuthTransferResult;
import com.qunhe.project.platform.project.auth.enums.AuthBizType;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.project.platform.project.auth.param.AuthCheckParam;
import com.qunhe.project.platform.project.auth.param.UserAuthBatchTransferParam;
import com.qunhe.project.platform.project.auth.param.UserAuthTransferParam;
import com.qunhe.project.platform.project.auth.param.UserAuthUpdateParam;
import com.qunhe.rpc.proxy.RpcWrapperException;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProjectAuthService {
    private static final QHLogger LOG = QHLogger.getLogger(ProjectAuthService.class);
    /**
     * //审核员id，内外网同一账号
     */
    private static final Long DESIGN_ADMIN_ID = 138238L;

    private final ProjectAuthClient projectAuthClient;

    private final LongCrypt longCrypt;


    /**
     * 校验用户是否有编辑权限，包括方案协作场景
     *
     * @param userId
     * @param designId
     * @return
     */
    public boolean checkAuth(final Long userId, final Long designId) {
        if (Objects.equals(SiteConstants.SpecialUserIds.KUJIALE_VIRTUAL_SAMPLE_ROOM, userId) ||
                Objects.equals(DESIGN_ADMIN_ID, userId)) {
            return true;
        }
        final AuthCheckParam authCheckParam = AuthCheckParam.builder()
                .userId(userId)
                .designId(designId)
                .authCheckType(AuthCheckType.WRITE)
                .build();
        final Result<Boolean> checkResult = projectAuthClient.checkAuth(authCheckParam);
        if (!checkResult.getD()) {
            LOG.message("auth check fail").warn();
            return false;
        }
        return true;
    }

    @SentinelResource(value = "checkAuth", fallback = "checkAuthHandler")
    public Boolean checkAuth(final Long designId, final Long userId, final Boolean coohomAuth,
            final Boolean dcsOrderAuth, final Long dcsOrderId, YunDesign yunDesign,
            AuthCheckType checkLevel) {
        final AuthCheckParam authCheckParam = AuthCheckParam.builder()
                .designId(designId)
                .userId(userId)
                .orderDesignId(dcsOrderId)
                .authCheckType(checkLevel)
                .coohomAuth(coohomAuth)
                .dcsOrderAuth(dcsOrderAuth)
                .build();
        final Result<Boolean> res = projectAuthClient.checkAuth(authCheckParam);
        if (res.success()) {
            return res.getD();
        } else {
            LOG.message("check auth failed, use fallback")
                    .with("designId", designId)
                    .with("userId", userId)
                    .warn();
        }
        return yunDesign != null && Objects.equals(yunDesign.getUserId(), userId);
    }

    public Boolean checkAuthHandler(final Long designId, final Long userId,
            final Boolean coohomAuth,
            final Boolean dcsOrderAuth, final Long dcsOrderId, YunDesign yunDesign,
            AuthCheckType checkLevel,
            final Throwable e) {
        LOG.message("checkAuth - BlockBack", e)
                .with("designId", designId)
                .with("userId", userId)
                .warn();
        return yunDesign != null && Objects.equals(yunDesign.getUserId(), userId);
    }

    @SentinelResource(value = "saveUserAuth")
    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public void saveUserAuth(Long userId, Long designId) {
        UserAuthUpdateParam userAuthUpdateParam = UserAuthUpdateParam.builder()
                .userId(userId)
                .designId(designId)
                .readAuth(true)
                .writeAuth(true)
                .commentAuth(true)
                .deleteAuth(true)
                .authBizType(AuthBizType.NONE)
                .build();
        Result<Boolean> result = projectAuthClient.updateUserAuth(userAuthUpdateParam);
        if (result == null || !result.success()) {
            LOG.message("save user auth fail")
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("save user auth fail");
        }
        LOG.message("save user auth success")
                .with("userId", userId)
                .with("designId", designId)
                .info();
    }

    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public List<UserAuthTransferResult> batchRecycleUserAuth(Long userId, List<Long> designIds) {
        if (CollectionUtils.isEmpty(designIds)) {
            return Lists.newArrayList();
        }
        UserAuthBatchTransferParam batchTransferParam = new UserAuthBatchTransferParam();
        batchTransferParam.setList(
                designIds.stream().map(designId -> UserAuthTransferParam.builder()
                        .designId(designId)
                        .srcUserId(userId)
                        .targetUserId(1L)
                        .build()).collect(Collectors.toList()));
        Result<List<UserAuthTransferResult>> result = projectAuthClient.batchTransferUserAuth(
                batchTransferParam);
        if (result == null || !result.success() || CollectionUtils.isEmpty(result.getD())) {
            LOG.message("batch recycle user auth fail")
                    .with("userId", userId)
                    .with("designIds", designIds)
                    .error();
            throw new ProjectAuthOperationException("batch recycle user auth fail");
        }
        return result.getD();
    }

    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public void recycleUserAuth(Long userId, Long designId) {
        UserAuthBatchTransferParam batchTransferParam = new UserAuthBatchTransferParam();
        batchTransferParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(designId)
                .srcUserId(userId)
                .targetUserId(1L)
                .build()));
        Result<List<UserAuthTransferResult>> result = projectAuthClient.batchTransferUserAuth(
                batchTransferParam);
        if (result == null || !result.success()) {
            LOG.message("recycle user auth fail")
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("recycle user auth fail");
        }
        if (CollectionUtils.isNotEmpty(result.getD()) && result.getD().get(0).getErrorCode() ==
                TransferErrorCode.EXCEPTION) {
            LOG.message("recycle user auth exception")
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("recycle user auth exception");
        }
    }

    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public List<UserAuthTransferResult> batchRecoverUserAuth(Long userId, List<Long> designIds) {
        if (CollectionUtils.isEmpty(designIds)) {
            return Lists.newArrayList();
        }
        UserAuthBatchTransferParam batchTransferParam = new UserAuthBatchTransferParam();
        batchTransferParam.setList(
                designIds.stream().map(designId -> UserAuthTransferParam.builder()
                        .designId(designId)
                        .srcUserId(1L)
                        .targetUserId(userId)
                        .build()).collect(Collectors.toList()));
        Result<List<UserAuthTransferResult>> result = projectAuthClient.batchTransferUserAuth(
                batchTransferParam);
        if (result == null || !result.success() || CollectionUtils.isEmpty(result.getD())) {
            LOG.message("batch recover user auth fail")
                    .with("userId", userId)
                    .with("designIds", designIds)
                    .error();
            throw new ProjectAuthOperationException("batch recover user auth fail");
        }
        return result.getD();
    }

    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public void recoverUserAuth(Long userId, Long designId) {
        UserAuthBatchTransferParam batchTransferParam = new UserAuthBatchTransferParam();
        batchTransferParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(designId)
                .srcUserId(1L)
                .targetUserId(userId)
                .build()));
        Result<List<UserAuthTransferResult>> result = projectAuthClient.batchTransferUserAuth(
                batchTransferParam);
        if (result == null || !result.success()) {
            LOG.message("recover user auth fail")
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("recover user auth fail");
        }
        if (CollectionUtils.isNotEmpty(result.getD()) && result.getD().get(0).getErrorCode() ==
                TransferErrorCode.EXCEPTION) {
            LOG.message("recover user auth exception")
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("recover user auth exception");
        }
    }

    @Retryable(include = { RpcWrapperException.class, ProjectAuthOperationException.class })
    public void transferUserAuth(Long srcUserId, Long targetUserId, Long designId) {
        UserAuthBatchTransferParam batchTransferParam = new UserAuthBatchTransferParam();
        batchTransferParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(designId)
                .srcUserId(srcUserId)
                .targetUserId(targetUserId)
                .build()));
        Result<List<UserAuthTransferResult>> result = projectAuthClient.batchTransferUserAuth(
                batchTransferParam);
        if (result == null || !result.success()) {
            LOG.message("transfer user auth fail")
                    .with("srcUserId", srcUserId)
                    .with("targetUserId", targetUserId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("transfer user auth fail");
        }
        if (CollectionUtils.isNotEmpty(result.getD()) && result.getD().get(0).getErrorCode() ==
                TransferErrorCode.EXCEPTION) {
            LOG.message("transfer user auth exception")
                    .with("srcUserId", srcUserId)
                    .with("targetUserId", targetUserId)
                    .with("designId", designId)
                    .error();
            throw new ProjectAuthOperationException("transfer user auth exception");
        }
    }


    /**
     * 校验用户是否有删除权限
     */
    @SentinelResource(value = "hasDeletedAccess", fallback = "hasDeletedAccessFallback")
    public boolean hasDeletedAccess(final String obsPlanId,
            final String obsDesignId, Long planId, Long designId) {
        final Long userIdBySession = UserDb.getUserIdBySession();
        if (userIdBySession == null) {
            return false;
        }
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = longCrypt.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = longCrypt.decrypt(obsDesignId);
        }
        if (designId == null && planId == null) {
            return false;
        }
        final AuthCheckParam authCheckParam = AuthCheckParam.builder()
                .userId(userIdBySession)
                .designId(designId)
                .planId(planId)
                .authCheckType(AuthCheckType.DELETE)
                .build();
        final Result<Boolean> deleteResult = projectAuthClient.checkAuth(authCheckParam);
        if (!deleteResult.getD()) {
            LOG.message("Delete Access check fail")
                    .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                    .with("traceId", SpanContextHolder.getTraceId())
                    .withPoJo(authCheckParam)
                    .warn();
            return false;
        } else {
            return true;
        }
    }

    public boolean hasDeletedAccessFallback(final String obsPlanId,
            final String obsDesignId, Long planId, Long designId, Throwable e) {
        LOG.message("Delete Access check fall back to true.", e)
                .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                .with("traceId", SpanContextHolder.getTraceId())
                .with("obsPlanId", obsPlanId)
                .with("obsDesignId", obsDesignId)
                .with("planId", planId)
                .with("designId", designId)
                .error();
        return true;
    }
}
