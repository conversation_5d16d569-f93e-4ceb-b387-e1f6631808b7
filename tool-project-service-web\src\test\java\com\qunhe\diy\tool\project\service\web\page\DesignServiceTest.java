/*
 * DesignServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.db.DesignMetaDataDb;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.kam.client.layoutdesigncommon.LayoutMultiDesignClient;
import com.qunhe.kam.client.layoutdesigncommon.exception.LayoutDesignCommonException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for DesignService
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DesignServiceTest {

    @Mock
    private LayoutMultiDesignClient layoutMultiDesignClient;

    @Mock
    private HomeDesignFacadeDb homeDesignFacadeDb;

    @Mock
    private DesignMetaDataDb designMetaDataDb;

    @InjectMocks
    private DesignService designService;

    @Before
    public void setUp() {
        // Setup common test data
    }

    @Test
    public void testGetMainDesignId_Success() throws Exception {
        // Setup
        Long designId = 12345L;
        Long userId = 67890L;
        Long expectedMainDesignId = 11111L;

        when(layoutMultiDesignClient.getMainDesignId(designId, userId))
                .thenReturn(expectedMainDesignId);

        // Execute
        Long result = designService.getMainDesignId(designId, userId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Main design ID should match", expectedMainDesignId, result);
        verify(layoutMultiDesignClient, times(1)).getMainDesignId(designId, userId);
    }

    @Test(expected = LayoutDesignCommonException.class)
    public void testGetMainDesignId_Exception() throws Exception {
        // Setup
        Long designId = 12345L;
        Long userId = 67890L;

        when(layoutMultiDesignClient.getMainDesignId(designId, userId))
                .thenThrow(new LayoutDesignCommonException("Test exception"));

        // Execute
        designService.getMainDesignId(designId, userId);

        // Verify - exception should be thrown
    }

    @Test
    public void testGetMainDesignIdFallBack() {
        // Setup
        Long designId = 12345L;
        Long userId = 67890L;
        Throwable throwable = new RuntimeException("Fallback test");

        // Execute
        Long result = designService.getMainDesignIdFallBack(designId, userId, throwable);

        // Verify
        assertNull("Fallback should return null", result);
    }



    @Test
    public void testGetLastOpenedLevelId_Success() {
        // Setup
        Long planId = 12345L;
        String expectedLevelId = "test-level-id";

        when(designMetaDataDb.getLastOpenedLevelId(planId))
                .thenReturn(expectedLevelId);

        // Execute
        String result = designMetaDataDb.getLastOpenedLevelId(planId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Level ID should match", expectedLevelId, result);
        verify(designMetaDataDb, times(1)).getLastOpenedLevelId(planId);
    }

    @Test
    public void testGetLastOpenedLevelId_Null() {
        // Setup
        Long planId = 12345L;

        when(designMetaDataDb.getLastOpenedLevelId(planId))
                .thenReturn(null);

        // Execute
        String result = designMetaDataDb.getLastOpenedLevelId(planId);

        // Verify
        assertNull("Result should be null", result);
        verify(designMetaDataDb, times(1)).getLastOpenedLevelId(planId);
    }

    @Test
    public void testGetLastOpenedLevelId_EmptyString() {
        // Setup
        Long planId = 12345L;
        String expectedLevelId = "";

        when(designMetaDataDb.getLastOpenedLevelId(planId))
                .thenReturn(expectedLevelId);

        // Execute
        String result = designMetaDataDb.getLastOpenedLevelId(planId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Level ID should be empty", "", result);
        verify(designMetaDataDb, times(1)).getLastOpenedLevelId(planId);
    }

    @Test
    public void testUpdateLastOpenedLevelId_Success() {
        // Setup
        Long planId = 12345L;
        String levelId = "updated-level-id";

        when(designMetaDataDb.updateLastOpenedLevelId(planId, levelId))
                .thenReturn(1);

        // Execute
        int result = designMetaDataDb.updateLastOpenedLevelId(planId, levelId);

        // Verify
        assertEquals("Update result should be 1", 1, result);
        verify(designMetaDataDb, times(1)).updateLastOpenedLevelId(planId, levelId);
    }

    @Test
    public void testUpdateLastOpenedLevelId_Failed() {
        // Setup
        Long planId = 12345L;
        String levelId = "updated-level-id";

        when(designMetaDataDb.updateLastOpenedLevelId(planId, levelId))
                .thenReturn(0);

        // Execute
        int result = designMetaDataDb.updateLastOpenedLevelId(planId, levelId);

        // Verify
        assertEquals("Update result should be 0", 0, result);
        verify(designMetaDataDb, times(1)).updateLastOpenedLevelId(planId, levelId);
    }
}
