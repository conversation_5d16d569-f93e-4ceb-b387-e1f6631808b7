/*
 * ProjectSaveParam.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.param;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qunhe.diy.tool.project.service.common.enums.ProjectBusinessEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * project-save
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolProjectSaveParam {
    private Long planId;
    private Long designId;
    private Long userId;
    private Long authorId;
    private Long commId;
    private Double area;
    private Double realArea;
    private Double srcArea;
    private Byte planType;
    private String name;
    private String vrc;
    private Byte sourceId;
    private Long albumId;
    private String modelDataId;
    private String designDataId;
    private Boolean recommend = false;
    private Boolean reworked = false;
    private Byte modelStatus;
    private String uploadPics;
    private Long specId;
    private Long unitId;
    private Boolean designSaved;
    private Integer designAttribute;
    private String designDesc;
    private String levelName;
    private Integer region;

    // ---------------------------不存储project的参数--------------------

    private String darenIncomingId;
    private ProjectBusinessEnum projectBusinessEnum;
    private Integer createdAppId;

}
