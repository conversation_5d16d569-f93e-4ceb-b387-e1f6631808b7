/*
 * ProjectSearchService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.exception.DesignSearchException;
import com.qunhe.diybe.dms.constant.project.ColDict;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.project.platform.project.search.client.DesignTagClient;
import com.qunhe.projectmanagement.client.ProjectClient;
import com.qunhe.projectmanagement.client.data.QueryByUserIdParam;
import com.qunhe.web.standard.data.Result;
import com.qunhe.web.standard.data.list.ListResult;
import org.springframework.stereotype.Service;

import java.util.Collections;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ProjectSearchService {

    private final ProjectClient projectClient;

    private final DesignTagClient designTagClient;


    public long countProject(final Long userId) {
        if (userId == null) {
            return 0;
        }
        QueryByUserIdParam queryByUserIdParam = buildQuery(userId);
        final ListResult<ProjectDesign> listResult =
                projectClient.pageGetProjectByUserIdWithCols(queryByUserIdParam);
        return listResult.getTotalCount();
    }
    private QueryByUserIdParam buildQuery(final Long userId) {
        return QueryByUserIdParam.builder()
                .cols(Collections.singletonList(ColDict.DESIGN_ID.rds()))
                .userId(userId)
                .deleted(false)
                .reworked(false)
                .start(0)
                .num(1)
                .build();
    }

    @SentinelResource(value = "checkDesignTagSearchable")
    public boolean checkDesignTagSearchable(Long designId) {
        Result<Boolean> res = designTagClient.checkSearchable(designId);
        if (res == null || !res.success() || res.getD() == null) {
            throw new DesignSearchException("checkDesignTagSearchable failed, designId: " + designId);
        }
        return res.getD();
    }

}
