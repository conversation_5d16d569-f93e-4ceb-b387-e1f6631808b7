/*
 * KamPageParam.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiyPageParam {

    private String obsDesignId;

    private String obsPlanId;

    private String redirectUrl;

    private String levelId;

    private String obsSrcPlanId;

    private String planName;

    private String askId;

    private Byte planType;

    private String toolName;

    private Boolean stayDiy;

    private Integer stage;

    private Long userId;

    private String obsAppId;

    private boolean fromCoohom;

    private boolean forceUpdate;

    private boolean cooperate;

    private boolean redirectBim;

    /**
     * coohom 协作
     */
    private List<String> tre;

    private Long orderDesignId;
}
