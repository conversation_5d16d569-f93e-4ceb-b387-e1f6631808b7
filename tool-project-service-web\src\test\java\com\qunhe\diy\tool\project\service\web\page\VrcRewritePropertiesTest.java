/*
 * VrcRewritePropertiesTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.core.env.Environment;

import java.util.Collections;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for VrcRewriteProperties
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class VrcRewritePropertiesTest {

    @Mock
    private Environment environment;

    private VrcRewriteProperties vrcRewriteProperties;

    @Before
    public void setUp() {
        // Setup common test data
    }

    @Test
    public void testConstructor_ValidJson() {
        // Setup
        String validJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"TEST_APP_ID\",\"needAppIdMatch\":true}]";
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(validJson);

        // Execute
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Verify
        assertNotNull("VrcRewriteProperties should be created", vrcRewriteProperties);
        VrcRewriteProperties.RewriteVrcInfo info = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertNotNull("Rewrite info should exist", info);
        assertEquals("VRC should match", "V0150R0901", info.getVrc());
        assertEquals("App ID should match", "TEST_APP_ID", info.getAppId());
        assertTrue("Need app ID match should be true", info.isNeedAppIdMatch());
    }

    @Test
    public void testConstructor_EmptyJson() {
        // Setup
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn("");

        // Execute
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Verify
        assertNotNull("VrcRewriteProperties should be created", vrcRewriteProperties);
        VrcRewriteProperties.RewriteVrcInfo info = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertNull("Rewrite info should be null", info);
    }

    @Test
    public void testConstructor_NullJson() {
        // Setup
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(null);

        // Execute
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Verify
        assertNotNull("VrcRewriteProperties should be created", vrcRewriteProperties);
        VrcRewriteProperties.RewriteVrcInfo info = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertNull("Rewrite info should be null", info);
    }

    @Test
    public void testConstructor_InvalidJson() {
        // Setup
        String invalidJson = "invalid json string";
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(invalidJson);

        // Execute
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Verify
        assertNotNull("VrcRewriteProperties should be created", vrcRewriteProperties);
        VrcRewriteProperties.RewriteVrcInfo info = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertNull("Rewrite info should be null", info);
    }

    @Test
    public void testGetRewriteInfo_Exists() {
        // Setup
        String validJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"TEST_APP_ID\",\"needAppIdMatch\":false}]";
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(validJson);
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Execute
        VrcRewriteProperties.RewriteVrcInfo result = vrcRewriteProperties.getRewriteInfo("V0150R0901");

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("VRC should match", "V0150R0901", result.getVrc());
        assertEquals("App ID should match", "TEST_APP_ID", result.getAppId());
        assertFalse("Need app ID match should be false", result.isNeedAppIdMatch());
    }

    @Test
    public void testGetRewriteInfo_NotExists() {
        // Setup
        String validJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"TEST_APP_ID\",\"needAppIdMatch\":true}]";
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(validJson);
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Execute
        VrcRewriteProperties.RewriteVrcInfo result = vrcRewriteProperties.getRewriteInfo("NON_EXISTENT_VRC");

        // Verify
        assertNull("Result should be null", result);
    }

    @Test
    public void testHandleContextRefreshEvent_RelevantKey() {
        // Setup
        String initialJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"OLD_APP_ID\",\"needAppIdMatch\":true}]";
        String updatedJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"NEW_APP_ID\",\"needAppIdMatch\":false}]";
        
        when(environment.getProperty("kam.page.rewriteVrcList"))
                .thenReturn(initialJson)
                .thenReturn(updatedJson);
        
        vrcRewriteProperties = new VrcRewriteProperties(environment);
        
        // Verify initial state
        VrcRewriteProperties.RewriteVrcInfo initialInfo = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertEquals("Initial app ID should match", "OLD_APP_ID", initialInfo.getAppId());
        assertTrue("Initial need app ID match should be true", initialInfo.isNeedAppIdMatch());

        // Create event with relevant key
        Set<String> keys = Collections.singleton("kam.page.rewriteVrcList");
        EnvironmentChangeEvent event = new EnvironmentChangeEvent(keys);

        // Execute
        vrcRewriteProperties.handleContextRefreshEvent(event);

        // Verify updated state
        VrcRewriteProperties.RewriteVrcInfo updatedInfo = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertEquals("Updated app ID should match", "NEW_APP_ID", updatedInfo.getAppId());
        assertFalse("Updated need app ID match should be false", updatedInfo.isNeedAppIdMatch());
    }

    @Test
    public void testHandleContextRefreshEvent_IrrelevantKey() {
        // Setup
        String initialJson = "[{\"vrc\":\"V0150R0901\",\"appId\":\"OLD_APP_ID\",\"needAppIdMatch\":true}]";
        
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(initialJson);
        vrcRewriteProperties = new VrcRewriteProperties(environment);
        
        // Verify initial state
        VrcRewriteProperties.RewriteVrcInfo initialInfo = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertEquals("Initial app ID should match", "OLD_APP_ID", initialInfo.getAppId());

        // Create event with irrelevant key
        Set<String> keys = Collections.singleton("some.other.property");
        EnvironmentChangeEvent event = new EnvironmentChangeEvent(keys);

        // Execute
        vrcRewriteProperties.handleContextRefreshEvent(event);

        // Verify state unchanged
        VrcRewriteProperties.RewriteVrcInfo unchangedInfo = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertEquals("App ID should remain unchanged", "OLD_APP_ID", unchangedInfo.getAppId());
        
        // Verify environment.getProperty was not called again
        verify(environment, times(1)).getProperty("kam.page.rewriteVrcList");
    }

    @Test
    public void testRewriteVrcInfo_DefaultValues() {
        // Setup & Execute
        VrcRewriteProperties.RewriteVrcInfo info = new VrcRewriteProperties.RewriteVrcInfo();

        // Verify
        assertNull("VRC should be null by default", info.getVrc());
        assertNull("App ID should be null by default", info.getAppId());
        assertTrue("Need app ID match should be true by default", info.isNeedAppIdMatch());
    }

    @Test
    public void testRewriteVrcInfo_SettersAndGetters() {
        // Setup
        VrcRewriteProperties.RewriteVrcInfo info = new VrcRewriteProperties.RewriteVrcInfo();

        // Execute
        info.setVrc("TEST_VRC");
        info.setAppId("TEST_APP_ID");
        info.setNeedAppIdMatch(false);

        // Verify
        assertEquals("VRC should match", "TEST_VRC", info.getVrc());
        assertEquals("App ID should match", "TEST_APP_ID", info.getAppId());
        assertFalse("Need app ID match should be false", info.isNeedAppIdMatch());
    }

    @Test
    public void testMultipleVrcEntries() {
        // Setup
        String multipleJson = "[" +
                "{\"vrc\":\"V0150R0901\",\"appId\":\"APP_ID_1\",\"needAppIdMatch\":true}," +
                "{\"vrc\":\"V0150R0902\",\"appId\":\"APP_ID_2\",\"needAppIdMatch\":false}" +
                "]";
        when(environment.getProperty("kam.page.rewriteVrcList")).thenReturn(multipleJson);
        vrcRewriteProperties = new VrcRewriteProperties(environment);

        // Execute & Verify
        VrcRewriteProperties.RewriteVrcInfo info1 = vrcRewriteProperties.getRewriteInfo("V0150R0901");
        assertNotNull("First info should exist", info1);
        assertEquals("First VRC should match", "V0150R0901", info1.getVrc());
        assertEquals("First app ID should match", "APP_ID_1", info1.getAppId());
        assertTrue("First need app ID match should be true", info1.isNeedAppIdMatch());

        VrcRewriteProperties.RewriteVrcInfo info2 = vrcRewriteProperties.getRewriteInfo("V0150R0902");
        assertNotNull("Second info should exist", info2);
        assertEquals("Second VRC should match", "V0150R0902", info2.getVrc());
        assertEquals("Second app ID should match", "APP_ID_2", info2.getAppId());
        assertFalse("Second need app ID match should be false", info2.isNeedAppIdMatch());
    }
}
