/*
 * CoverPicFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.google.common.collect.Maps;
import com.qunhe.log.QHLogger;
import com.qunhe.user.growth.design.excellent.client.client.UserDesignClient;
import com.qunhe.user.growth.design.excellent.common.data.dto.SimpleDesignDto;
import com.qunhe.web.standard.exception.BizzException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Component
@RequiredArgsConstructor
public class CoverPicFacade {

    private static final QHLogger LOG = QHLogger.getLogger(CoverPicFacade.class);

    private final UserDesignClient userDesignClient;
    @SentinelResource(value = "getCoverPicsByPlanIds", fallback = "getCoverPicsByPlanIdsFallback")
    public Map<Long, String> getCoverPicsByPlanIds(List<Long> planIds) {
        try {
            if (CollectionUtils.isEmpty(planIds)) {
                return Maps.newHashMap();
            }
            List<SimpleDesignDto> simpleDesignDtos = userDesignClient.getCoverPicByPlanIds(planIds);
            if (CollectionUtils.isEmpty(simpleDesignDtos)) {
                return Maps.newHashMap();
            }
            return simpleDesignDtos.stream().filter(dto -> dto.getCoverPic() != null).collect(
                    Collectors.toMap(SimpleDesignDto::getPlanId,
                            SimpleDesignDto::getCoverPic, (a, b) -> a));
        } catch (BizzException e) {
            // catch exception
            LOG.message("getCoverImages error", e)
                    .with("planIds", planIds)
                    .error();
        }
        return Collections.emptyMap();
    }

    public Map<Long, String> getCoverPicsByPlanIdsFallback(List<Long> planIds, Throwable e) {
        LOG.message("getCoverPicsByPlanIds fallback", e)
                .with("planIds", planIds)
                .error();
        return Collections.emptyMap();
    }

}
