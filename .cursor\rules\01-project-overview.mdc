---
description:
globs:
alwaysApply: false
---
# 工具方案服务 (tool-project-service)

## 项目概述
tool-project-service 是一个用于管理工具方案的服务。该项目基于 Spring Boot 构建，使用 Maven 进行依赖管理。

## 项目结构
项目采用多模块架构，包含以下主要模块：

- **tool-project-service-common**: 包含共享的实体、常量、枚举等基础代码
- **tool-project-service-client**: 提供给其他服务调用的客户端接口
- **tool-project-service-web**: Web 服务模块，包含 Controller、配置等
- **tool-project-service-biz**: 业务逻辑实现模块

## 核心功能
根据 design.md 文件，该服务主要提供以下功能：
- 方案创建管理
- 方案复制
- 工具方案数据存储与检索

## 技术栈
- Spring Boot
- Maven
- TDDL (分布式数据库)
- SOA (面向服务架构)
- Pilot (应用部署平台)
- Hunter (可能是内部服务框架)

## 配置管理
- 使用 Pilot 进行应用部署
- 环境配置托管到 Toad
- 支持多数据源配置
