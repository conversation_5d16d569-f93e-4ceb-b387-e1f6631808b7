<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ BatchOperationRecordMapper.xml
  ~ Copyright 2024 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.1//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.BatchOperationRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,
        `recordid`,
        `userid`,
        `operator`,
        `rollback`,
        `status`,
        `origin_count`,
        `complete_count`,
        `description`,
        `link`
    </sql>


    <insert id="insertBatchOperationRecord">
        INSERT INTO batch_operation_record (
        <trim suffixOverrides=",">
            <if test="recordId != null">`recordid`,</if>
            <if test="userId != null">`userid`,</if>
            <if test="operator != null">`operator`,</if>
            <if test="rollback != null">`rollback`,</if>
            <if test="status != null">`status`,</if>
            <if test="originCount != null">`origin_count`,</if>
            <if test="completeCount != null">`complete_count`,</if>
            <if test="description != null">`description`,</if>
            <if test="link != null">`link`,</if>
        </trim>
        ) VALUES (
        <trim suffixOverrides=",">
            <if test="recordId != null">#{recordId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="operator != null">#{operator},</if>
            <if test="rollback != null">#{rollback},</if>
            <if test="status != null">#{status},</if>
            <if test="originCount != null">#{originCount},</if>
            <if test="completeCount != null">#{completeCount},</if>
            <if test="description != null">#{description},</if>
            <if test="link != null">#{link},</if>
        </trim>
        )
    </insert>

    <select id="getBatchOperationRecordByRecordId"
            resultType="com.qunhe.diy.tool.project.service.common.data.BatchOperationData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM batch_operation_record
        WHERE recordid = #{recordId}
        LIMIT 1
    </select>

    <select id="getMultiOperateLinksByUserId" resultType="string">
        SELECT link
        FROM batch_operation_record
        WHERE userid = #{userId}
          AND rollback = #{rollback}
    </select>

    <update id="updateCompleteCountAndStatus">
        UPDATE batch_operation_record
        SET complete_count = complete_count + #{additionalCount},
            status         = #{status}
        WHERE recordid = #{recordId}
    </update>

    <update id="updateBatchOperateFinishInfos">
        UPDATE batch_operation_record
        SET status         = #{status},
            link           = #{link},
            complete_count = #{completeCount}
        WHERE recordid = #{recordId}
    </update>

    <insert id="updateOperationRecord"
            parameterType="com.qunhe.diy.tool.project.service.common.data.BatchOperationData">
        UPDATE batch_operation_record
        SET
        <trim suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="completeCount != null">complete_count = #{completeCount},</if>
            <if test="link != null">link = #{link},</if>
        </trim>
        WHERE
        <trim suffixOverrides="AND">
            <if test="recordId != null">recordid = #{recordId} AND</if>
        </trim>
    </insert>

</mapper>