/*
 * JsonMapper.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.instdeco.plan.util.StringUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.rpc.common.utils.StringUtils;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class JsonMapper {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    private static final QHLogger LOG = QHLogger.getLogger(JsonMapper.class);

    public static ObjectMapper mapper() {
        return OBJECT_MAPPER;
    }

    public static <T> T parse(String json, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            LOG.message("parse to object", e)
                    .with("str", json)
                    .with("traceId", SpanContextHolder.getTraceId())
                    .warn();
            return null;
        }
    }

    public static List<Long> getLongs(final String longsStr) {
        if (StringUtils.isBlank(longsStr)) {
            return Collections.emptyList();
        }
        try {
            return OBJECT_MAPPER.readValue(longsStr, new TypeReference<List<Long>>() {
            });
        } catch (final IOException e) {
            LOG.message("getLongs", e)
                    .with("str", longsStr)
                    .with("traceId", SpanContextHolder.getTraceId())
                    .warn();
            return Collections.emptyList();
        }
    }

    public static List<String> getStrings(final String stringsStr) {
        if (StringUtils.isBlank(stringsStr)) {
            return Collections.emptyList();
        }
        try {
            return OBJECT_MAPPER.readValue(stringsStr, new TypeReference<List<String>>() {
            });
        } catch (final IOException e) {
            LOG.message("getStrings", e)
                    .with("str", stringsStr)
                    .with("traceId", SpanContextHolder.getTraceId())
                    .warn();
            return Collections.emptyList();
        }
    }

    @SneakyThrows(IOException.class)
    public static <T> T parse(String in, Class<T> tClass) {
        return OBJECT_MAPPER.readValue(in, tClass);
    }

    @SneakyThrows(JsonProcessingException.class)
    public static String writeValueAsString(final Object object) {
        return OBJECT_MAPPER.writeValueAsString(object);
    }

    @SneakyThrows(JsonProcessingException.class)
    public static byte[] writeValueAsBytes(final Object object) {
        return OBJECT_MAPPER.writeValueAsBytes(object);
    }

    /**
     * 是否包含emoji
     *
     */
    public static boolean containEmoji(final Object object) {
        return StringUtil.containsEmoji(writeValueAsString(object));
    }

    /**
     * 用于解析形如"[]","[id1,id2]"的字符串,则返回非空List的首个元素
     * @param input
     * @return
     */
    public static String catchFirstElementInInputStr(final String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        final List<String> elements = JsonMapper.getStrings(input);
        if (CollectionUtils.isEmpty(elements)) {
            return null;
        }
        return elements.get(0);
    }
}
