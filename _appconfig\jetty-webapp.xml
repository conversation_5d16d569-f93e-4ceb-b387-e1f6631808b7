<!--
  ~ jetty-webapp.xml
  ~ Copyright 2025 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<Configure id="Server" class="org.eclipse.jetty.server.Server">
    <Call class="org.eclipse.jetty.webapp.WebAppContext" name="addSystemClasses">
        <Arg><Ref refid="Server"/></Arg>
        <Arg>
            <Call class="org.eclipse.jetty.util.StringUtil" name="csvSplit">
                <Arg><Property name="jetty.webapp.addSystemClasses"/></Arg>
            </Call>
        </Arg>
    </Call>

    <Call class="org.eclipse.jetty.webapp.WebAppContext" name="addServerClasses">
        <Arg><Ref refid="Server"/></Arg>
        <Arg>
            <Call class="org.eclipse.jetty.util.StringUtil" name="csvSplit">
                <Arg><Property name="jetty.webapp.addServerClasses"/></Arg>
            </Call>
        </Arg>
    </Call>
    <!--     jetty server 启用GzipHandler，只需要修改GzipHandler中的配置参数-->
    <Call name="insertHandler">
        <Arg>
            <New id="GzipHandler" class="org.eclipse.jetty.server.handler.gzip.GzipHandler">
                <!--            response body如果 大于2KB ，会对response body进行Gzip压缩， 同时需要在服务端进行 1.4的配置-->
                <Set name="minGzipSize">2048</Set>
                <!--            仅对application/json类型 response进行Gzip压缩-->
                <Set name="includedMimeTypes">
                    <Array type="java.lang.String">
                        <Item>application/json</Item>
                        <!--如果需要可以加上 比如此应用接口包含text/plain数据，比如diy,pq都由的 -->
                        <Item>text/plain</Item>
                    </Array>
                </Set>
                <!--                开启POST和GET方法的压缩-->
                <Set name="includedMethods">
                    <Array type="java.lang.String">
                        <Item>GET</Item>
                        <Item>POST</Item>
                    </Array>
                </Set>
            </New>
        </Arg>
    </Call>
</Configure>