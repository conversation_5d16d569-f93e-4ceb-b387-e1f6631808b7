/*
 * RedisService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class RedisService {

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public RedisService(@Qualifier(value = "jedisConnectionFactory") final RedisConnectionFactory factory) {
        this.redisTemplate = new StringRedisTemplate(factory);
    }

    public void set(final String key, final String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void setWithExpireTime(final String key, final String value, final long timeout,
            final TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public String get(final String key){
        return redisTemplate.opsForValue().get(key);
    }
}
