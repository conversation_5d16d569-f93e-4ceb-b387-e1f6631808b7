/*
 * BatchRemoveOrRestoreService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.log.QHLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BatchRemoveOrRestoreService {
    private static final QHLogger LOG = QHLogger.getLogger(
            BatchRemoveOrRestoreService.class);

    private static final int PROJECT_USER_SHARD_SIZE = 256;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final GeneralProperties generalProperties;

    private final BatchOperationRecordDb batchOperationRecordDb;

    @Autowired
    public BatchRemoveOrRestoreService(
            final ProjectDesignFacadeDb projectDesignFacadeDb,
            final GeneralProperties generalProperties,
            final BatchOperationRecordDb batchOperationRecordDb) {
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.generalProperties = generalProperties;
        this.batchOperationRecordDb = batchOperationRecordDb;
    }


    @SentinelResource(value = "singleBatchOperateProjects",
            blockHandler = "singleBatchOperateProjectsFallBack")
    public void singleBatchOperateProjects(final String recordId,
            final BatchOperationData batchOperationData,
            final List<Long> designIdsToDeal, final List<Long> totalDealIdList) {
        //按照designid mod256分批，缓解流量放大
        Map<Integer, List<Long>> modIdAndGroupedDesignIdsMap = designIdsToDeal.stream()
                .collect(Collectors.groupingBy(
                        designId -> Math.toIntExact(designId % PROJECT_USER_SHARD_SIZE)));
        List<List<Long>> batches = new ArrayList<>(PROJECT_USER_SHARD_SIZE);
        //每个单表按照MaxPatitionSize继续分批
        final Set<Integer> modIds = modIdAndGroupedDesignIdsMap.keySet();
        for (Integer modid : modIds) {
            final List<Long> designIdsWithinSingleMod = modIdAndGroupedDesignIdsMap.get(modid);
            final List<List<Long>> subPartitions = Lists.partition(designIdsWithinSingleMod,
                    generalProperties.getMaxPatitionSizeForBatchOperate());
            batches.addAll(subPartitions);
        }

        for (List<Long> batch : batches) {
            batchOperateDesignsInPartition(recordId, batch, totalDealIdList,
                    batchOperationData.getRollback() == 1);
        }
    }

    public void singleBatchOperateProjectsFallBack(final String recordId,
            final BatchOperationData batchOperationData,
            final List<Long> designIdsToDeal, final List<Long> totalDealIdList, BlockException e) {
        LOG.message("singleBatchOperateProjects fallback", e)
                .with("recordId", recordId)
                .with("designIdsToDeal", JsonMapper.writeValueAsString(designIdsToDeal))
                .error();
    }

    /**
     * 处理单个分表下的批量方案
     * @param recordId
     * @param designIds
     * @param totalDealIdList
     * @param rollback
     */
    public void batchOperateDesignsInPartition(
            final String recordId, final List<Long> designIds, List<Long> totalDealIdList,
            final boolean rollback) {
        try {
            //去除已删除过的方案，避免es延迟导致重复删除
            designIds.removeAll(totalDealIdList);
            if (CollectionUtils.isEmpty(designIds)) {
                return;
            }

            final int dealCount = rollback ?
                    projectDesignFacadeDb.batchRollbackRemoveProject(designIds) :
                    projectDesignFacadeDb.batchDeleteProject(designIds);
            //原子操作，要么全量成功；否则失败
            if (dealCount == designIds.size()) {
                LOG.message("batchOperateDesignsInPartition success")
                        .with("recordId", recordId)
                        .with("designIds", JsonMapper.writeValueAsString(designIds))
                        .info();
                totalDealIdList.addAll(designIds);
                batchOperationRecordDb.updateCompleteCountAndStatus(dealCount,
                        BatchOperationStatusEnum.GOING.getCode(), recordId);
            }
        } catch (Exception e) {
            //任意异常中止
            LOG.message("batchOperateDesignsInPartition failed", e)
                    .with("recordId", recordId)
                    .with("designIds", JsonMapper.writeValueAsString(designIds))
                    .warn();
        }
    }

}
