/*
 *  PlanTypeUtil.java
 *  Copyright 2022 Qunhe Tech, all rights reserved.
 *  Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.common.util;


import com.qunhe.diybe.dms.data.PlanType;

/**
 * <AUTHOR>
 */
public class PlanTypeUtil {
    public static boolean isBIMPlan(final Byte planType) {
        if (planType == null) {
            return false;
        }
        return PlanType.getProjectType(planType).equals(PlanType.ProjectType.BIM);
    }

    public static boolean isPhotoStudioPlan(final Byte planType) {
        if (planType == null) {
            return false;
        }
        return PlanType.getProjectType(planType).equals(PlanType.ProjectType.PHOTO_STUDIO);
    }

    public static boolean isUpdateBackupPlan(final Byte planType) {
        return planType != null && PlanType.getProjectType(planType) == PlanType.ProjectType.BACKUP;
    }
}
