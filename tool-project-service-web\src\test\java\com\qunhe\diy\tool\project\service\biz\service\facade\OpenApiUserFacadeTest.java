/*
 * OpenApiUserFacadeTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.instdeco.plan.openapi.client.PartnerUserMappingClient;
import com.qunhe.instdeco.plan.openapi.data.PartnerUserMappingRelation;
import com.qunhe.instdeco.plan.openapi.exception.OpenApiClientException;
import com.qunhe.web.standard.exception.BizzException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OpenApiUserFacadeTest {

    @Mock
    private PartnerUserMappingClient mockPartnerUserMappingClient;

    private OpenApiUserFacade openApiUserFacadeUnderTest;

    @Before
    public void setUp() throws Exception {
        openApiUserFacadeUnderTest = new OpenApiUserFacade(mockPartnerUserMappingClient);
    }

    @Test
    public void testGetAppUidByUserIds_SingleUser() throws Exception {
        // Setup
        final Long userId = 123L;
        final String appUid = "app-user-123";
        final Map<Long, String> expectedResult = new HashMap<>();
        expectedResult.put(userId, appUid);

        // Configure PartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(...).
        final PartnerUserMappingRelation partnerUserMappingRelation = new PartnerUserMappingRelation();
        partnerUserMappingRelation.setRelationId(1L);
        partnerUserMappingRelation.setPartnerId(10L);
        partnerUserMappingRelation.setAppUid(appUid);
        partnerUserMappingRelation.setUserId(userId);
        partnerUserMappingRelation.setAppUname("TestUser");
        partnerUserMappingRelation.setCreated(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        partnerUserMappingRelation.setLastModified(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        partnerUserMappingRelation.setType(0);
        
        final List<PartnerUserMappingRelation> partnerUserMappingRelations = Arrays.asList(partnerUserMappingRelation);
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(Arrays.asList(userId)))
                .thenReturn(partnerUserMappingRelations);

        // Run the test
        final Map<Long, String> result = openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(userId));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        assertThat(result).hasSize(1);
        assertThat(result.get(userId)).isEqualTo(appUid);
    }

    @Test
    public void testGetAppUidByUserIds_MultipleUsers() throws Exception {
        // Setup
        final Long userId1 = 123L;
        final Long userId2 = 456L;
        final String appUid1 = "app-user-123";
        final String appUid2 = "app-user-456";
        
        final Map<Long, String> expectedResult = new HashMap<>();
        expectedResult.put(userId1, appUid1);
        expectedResult.put(userId2, appUid2);

        // Configure first relation
        final PartnerUserMappingRelation relation1 = new PartnerUserMappingRelation();
        relation1.setRelationId(1L);
        relation1.setPartnerId(10L);
        relation1.setAppUid(appUid1);
        relation1.setUserId(userId1);
        relation1.setAppUname("TestUser1");
        relation1.setCreated(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation1.setLastModified(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation1.setType(0);

        // Configure second relation
        final PartnerUserMappingRelation relation2 = new PartnerUserMappingRelation();
        relation2.setRelationId(2L);
        relation2.setPartnerId(10L);
        relation2.setAppUid(appUid2);
        relation2.setUserId(userId2);
        relation2.setAppUname("TestUser2");
        relation2.setCreated(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation2.setLastModified(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation2.setType(0);
        
        final List<PartnerUserMappingRelation> partnerUserMappingRelations = Arrays.asList(relation1, relation2);
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(Arrays.asList(userId1, userId2)))
                .thenReturn(partnerUserMappingRelations);

        // Run the test
        final Map<Long, String> result = openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(userId1, userId2));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        assertThat(result).hasSize(2);
        assertThat(result.get(userId1)).isEqualTo(appUid1);
        assertThat(result.get(userId2)).isEqualTo(appUid2);
    }

    @Test
    public void testGetAppUidByUserIds_DuplicateUserIds() throws Exception {
        // Setup
        final Long userId = 123L;
        final String appUid = "app-user-123";
        
        final Map<Long, String> expectedResult = new HashMap<>();
        expectedResult.put(userId, appUid);

        // Configure relation
        final PartnerUserMappingRelation relation = new PartnerUserMappingRelation();
        relation.setRelationId(1L);
        relation.setPartnerId(10L);
        relation.setAppUid(appUid);
        relation.setUserId(userId);
        relation.setAppUname("TestUser");
        relation.setCreated(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation.setLastModified(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        relation.setType(0);
        
        final List<PartnerUserMappingRelation> partnerUserMappingRelations = Arrays.asList(relation);
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(Arrays.asList(userId, userId)))
                .thenReturn(partnerUserMappingRelations);

        // Run the test - calling with duplicate user IDs
        final Map<Long, String> result = openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(userId, userId));

        // Verify the results - should contain only one entry since it's a map
        assertThat(result).isEqualTo(expectedResult);
        assertThat(result).hasSize(1);
    }

    @Test
    public void testGetAppUidByUserIds_EmptyUserIdsList() throws Exception {
        // Setup
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(Collections.emptyList()))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, String> result = openApiUserFacadeUnderTest.getAppUidByUserIds(Collections.emptyList());

        // Verify the results
        assertThat(result).isEmpty();
    }

    @Test
    public void testGetAppUidByUserIds_PartnerUserMappingClientReturnsNoItems() throws Exception {
        // Setup
        final Map<Long, String> expectedResult = new HashMap<>();
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, String> result = openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        assertThat(result).isEmpty();
    }


    @Test
    public void testGetAppUidByUserIds_PartnerUserMappingClientThrowsOpenApiClientException() throws Exception {
        // Setup
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(anyList()))
                .thenThrow(new OpenApiClientException("API client error"));

        // Run the test
        assertThatThrownBy(() -> openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(0L)))
                .isInstanceOf(OpenApiClientException.class)
                .hasMessageContaining("API client error");
    }

    @Test
    public void testGetAppUidByUserIds_PartnerUserMappingClientThrowsBizzException() throws Exception {
        // Setup
        when(mockPartnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(anyList()))
                .thenThrow(new BizzException("Business logic error"));

        // Run the test
        assertThatThrownBy(() -> openApiUserFacadeUnderTest.getAppUidByUserIds(Arrays.asList(0L)))
                .isInstanceOf(BizzException.class)
                .hasMessageContaining("Business logic error");
    }
}
