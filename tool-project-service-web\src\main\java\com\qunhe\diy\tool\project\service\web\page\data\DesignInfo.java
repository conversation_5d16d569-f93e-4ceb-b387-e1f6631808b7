/*
 * DesignInfo.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DesignInfo {
    private String obsDesignId;

    private String obsPlanId;

    private String levelId;

    private String planName;

    private Boolean isOwner = false;

    /**
     * 是否是协同方案
     */
    private Boolean isSynergy = false;
    /**
     * 协同方案是否对方案资产进行隔离
     */
    private Boolean isSynergyIsolate = false;

    /**
     * 是否是复制的方案，若是会返回copy的父方案
     */
    private String obsParentId;
}
