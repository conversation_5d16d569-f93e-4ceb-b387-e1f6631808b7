/*
 * ApiIntegrationTest.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import static io.restassured.specification.ProxySpecification.host;

import java.io.IOException;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.data.DesignSearchableStatus;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;


import io.restassured.RestAssured;
import io.restassured.http.ContentType;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Ignore
public class ApiIntegrationTest {

    @BeforeClass
    public static void before() {
        RestAssured.proxy = host("************").withPort(80);
    }


    @Test
    public void test_create_and_query_tag_search_result() throws IOException {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setName("自动化测试");
        toolProjectSaveParam.setUserId(1111111437L);
        String res = RestAssured.given().header("x-qh-id", 1111111437)
                .body(toolProjectSaveParam)
                .contentType(ContentType.JSON)
                .when()
                .post(ApiConstant.PROJECT_CREATE_API).then().extract().asString();
        Result<ToolProjectHomeDesign> designRes =
                new ObjectMapper().readValue(res, new TypeReference<Result<ToolProjectHomeDesign>>(){} );
        Long designId = designRes.getD().getDesignId();
        res = RestAssured.given().header("x-qh-id", 1111111437)
                .queryParam("design_id", LongCipher.DEFAULT.encrypt(designId))
                .when()
                .get(ApiConstant.PROJECT_SEARCHABLE_STATUS).then().log().all().extract()
                .asString();
        Result<Integer> readyRes =
                new ObjectMapper().readValue(res, new TypeReference<Result<Integer>>(){} );
        Assert.assertEquals("design should be ready", readyRes.getD().intValue(),
                DesignSearchableStatus.TAG_CAN_BE_SEARCHED.getValue());
    }


}
