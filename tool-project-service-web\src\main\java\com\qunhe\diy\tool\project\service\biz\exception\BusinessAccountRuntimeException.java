/*
 * BusinessAccountRuntimeException.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.exception;

/**
 * <AUTHOR>
 *
 */
public class BusinessAccountRuntimeException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public BusinessAccountRuntimeException(final String msg) {
        super(msg);
    }

    public BusinessAccountRuntimeException(final String msg, final Throwable throwable) {
        super(msg, throwable);
    }
}
