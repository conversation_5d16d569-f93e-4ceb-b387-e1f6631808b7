/*
 * CommunityServiceHasTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasExceptionCountDegradeResource;
import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasRtDegradeResource;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.FphFloorplanService;
import com.qunhe.diy.tool.project.service.biz.service.facade.HasSwitchFacade;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
@RunWith(SpringRunner.class)
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@Import({ CommunityService.class, SentinelResourceAspect.class })
public class CommunityServiceHasTest {

    @MockBean
    CommunityDb communityDb;
    @MockBean
    SysDictAreaMapper sysDictAreaMapper;
    @MockBean
    GeneralProperties generalProperties;
    @MockBean
    FphFloorplanService fphFloorplanService;
    @MockBean
    HasSwitchFacade hasSwitchFacade;
    @MockBean
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Autowired
    CommunityService communityService;
    

    @Test
    public void updateCommidIfPreciseIpSupport_fallback_success() {
        // 构造 has 降级规则，模拟异常数降级
        setHasExceptionCountDegradeResource("updateCommidIfPreciseIpSupport");

        // 模拟业务代码抛出异常
        Mockito.when(communityDb.getOrCreateCommunity(anyLong(), any()))
                .thenThrow(new RuntimeException("mock communityDb getOrCreateCommunity exception"));

        // 走到fallback 逻辑，返回空
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();

        try {
            communityService.updateCommidIfPreciseIpSupport(true, toolProjectSaveParam);
        } catch (Exception e) {
            // ignore
            fail("fallback is not work");
        }

    }

    @Test
    public void updateCommidIfPreciseIpSupport_rt_degrade() {
        // 构造 has 降级规则，模拟异常数降级
        setHasRtDegradeResource("updateCommidIfPreciseIpSupport");

        // 走到fallback 逻辑，返回空
        ToolProjectSaveParam toolProjectSaveParam = Mockito.mock(ToolProjectSaveParam.class);

        communityService.updateCommidIfPreciseIpSupport(true, toolProjectSaveParam);

        Mockito.verify(toolProjectSaveParam, Mockito.times(0)).getUserId();

        communityService.updateCommidIfPreciseIpSupport(true, toolProjectSaveParam);

        Mockito.verify(toolProjectSaveParam, Mockito.times(0)).getSourceId();


    }

    
}
