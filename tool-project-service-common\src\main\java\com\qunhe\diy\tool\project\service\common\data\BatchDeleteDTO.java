/*
 * BatchDeleteDTO.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchDeleteDTO {

    /**
     * 没有删除权限的方案id
     */
    private List<Long> noDeleteAuthDesignIds;

    /**
     * 可删除方案的删除结果
     */
    private Boolean deleteResult;
}
