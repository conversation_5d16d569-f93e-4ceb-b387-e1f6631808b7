livenessProbe:
  httpGet:
    path: /healthz

deploy:
- stage: dev
  port: 80
  env:
  - name: JAVA_OPTIONS
    value: -Xmx2G -Dspring.profiles.active=dev
- stage: stable
  port: 80
  env:
  - name: JAVA_OPTIONS
    value: -Xmx2G -Dspring.profiles.active=stable
- stage: prod_test
  port: 80
  env:
  - name: JAVA_OPTIONS
    value: -Xmx2G -Dspring.profiles.active=prod_test
- stage: sit
  port: 80
  env:
  - name: JAVA_OPTIONS
    value: -Xmx1500m -Xms1500m -XX:NewRatio=1 -XX:CMSInitiatingOccupancyFraction=70 -Dspring.profiles.active=sit
- stage: prod
  port: 80
  env:
  - name: JAVA_OPTIONS
    value: -Xmx4G -Xms4G -XX:+UseG1GC -XX:MaxDirectMemorySize=2G -Dspring.profiles.active=prod
