/*
 * ParamCheckUtil.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.util;

import com.google.common.base.Preconditions;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.enums.ProjectBusinessEnum;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ParamCheckUtil {

    private static final int BATCH_LIMIT = 30;

    public static void checkSaveParam(final ToolProjectSaveParam toolProjectSaveParam,
            final Boolean backend) {
        if (toolProjectSaveParam.getProjectBusinessEnum() == null) {
            toolProjectSaveParam.setProjectBusinessEnum(Objects.equals(true, backend) ?
                    ProjectBusinessEnum.BACKEND_NEW : ProjectBusinessEnum.FRONT_NEW);
        }
        checkSaveParam(toolProjectSaveParam);

    }

    public static void checkSaveParam(final ToolProjectSaveParam toolProjectSaveParam) {
        Preconditions.checkArgument(Objects.nonNull(toolProjectSaveParam),
                ErrorCode.PARAM_ERROR.getCode());
        Preconditions.checkArgument(Objects.nonNull(toolProjectSaveParam.getUserId()),
                ErrorCode.USER_ERROR.getCode());

    }

    public static void checkCopyParam(final ToolProjectCopyParam toolProjectCopyParam) {
        Preconditions.checkArgument(Objects.nonNull(toolProjectCopyParam),
                ErrorCode.PARAM_ERROR.getCode());
        Preconditions.checkArgument(Objects.nonNull(toolProjectCopyParam.getSrcDesignId()),
                ErrorCode.PARAM_ERROR.getCode());
        Preconditions.checkArgument(Objects.nonNull(toolProjectCopyParam.getDstUserId()),
                ErrorCode.USER_ERROR.getCode());
    }

    public static void checkRecycle(final Long designId, final Long planId) {
        Preconditions.checkArgument(designId != null || planId != null,
                ErrorCode.PARAM_ERROR.getCode());
    }

    public static void checkBatchOperate(final List<Long> designIds) {
        Preconditions.checkArgument(
                CollectionUtils.isNotEmpty(designIds) && designIds.size() <= BATCH_LIMIT,
                ErrorCode.BATCH_DESIGN_ID_LIMIT.getCode());
    }

    public static void checkBatchOperateDelete(final BatchOperatorVO batchOperatorVO) {
        Preconditions.checkArgument(
                batchOperatorVO != null && batchOperatorVO.getUserId() != null,
                ErrorCode.BATCH_OPERATION_PARAM_INVALID.getCode());
    }
}
