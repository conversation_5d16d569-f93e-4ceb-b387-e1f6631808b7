/*
 * ToadProperties.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Function: TOAD变动的配置类
 *
 * <AUTHOR>
 */

@Component
@RefreshScope
@Data
public class ToadProperties {

    @Value("${feedback.topic:\"account-data-handover_topic\"}")
    private String feedbackTopic;

    @Value("${project.handover.topic:\"project_design_handover\"}")
    private String projectHandoverTopic;

    @Value("${tps.batchOperationParamSizeLimit:30}")
    private int batchOperationParamSizeLimit;

    @Value("${default.dwDrawingVersion:v1}")
    private String dwDrawingVersion;

    @Value("${multiLevel.revision.enable:false}")
    private boolean multiLevelRevisionEnable;
    @Value("${multiLevel.vrc:\"V0150R0901\"}")
    private String multiLevelVrc;
    private Set<String> multiLevelVrcSet;

    @Value("${userRangeCheck:true}")
    private boolean userRangeCheck;

    /**
     * 承载页加载异常错误跳转提示地址
     */
    @Value("${kam.page.errorRedirectUrl:/cloud/tool/cooperation/landing}")
    private String loadingPageErrorRedirectUrl;

    @Value("${qunhe.tool.bim.domain:www.kujiale.com}")
    private String bimDomain;

    @Value("${qunhe.tool.vrc-config}")
    private String vrcConfig;

    @Value("${kam.page.session.errorRedirectUrl:/design/load/error-page}")
    private String loadingPageSessionErrorRedirectUrl;

    @Value("${kam.page.session.errorTip.expireMinutes:2}")
    private Long sessionErrorTipExpireMinutes;

    @Value("${qunhe.legacy.project-stage}")
    private String projectStage;

    @Value("${qunhe.yun.my-design-url:/pcenter/mydesign}")
    private String myDesignUrl;

    @Value("${qunhe.web.host:/cloud}")
    private String host;

    @Value("${dcsOrderGrantAuth:true}")
    private boolean dcsOrderGrantAuth;

    @Value("${enablePageOpenRecycleDiy:false}")
    private boolean enablePageOpenRecycleDiy = false;

    @Value("${qunhe.yun.host}")
    private String yunHost;

    @Value("${qunhe.yun.domain-host}")
    private String domainHost;

    @Value("${qunhe.yun.login-url}")
    private String loginUrl;


    @PostConstruct
    public void init() {
        multiLevelVrcSet = new HashSet<>();
        if (StringUtils.isBlank(multiLevelVrc)) {
            return;
        }
        multiLevelVrcSet.addAll(Arrays.asList(StringUtils.split(multiLevelVrc, ",")));
    }

}
