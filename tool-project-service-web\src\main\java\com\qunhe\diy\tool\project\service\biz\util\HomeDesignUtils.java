/*
 * HomeDesignUtils.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.log.QHLogger;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
public class HomeDesignUtils {

    private static final QHLogger LOG = QHLogger.getLogger(HomeDesignUtils.class);

    public static boolean validLevelInfos(final List<LevelInfo> levelInfos) {
        if (CollectionUtils.isEmpty(levelInfos)) {
            return true;
        }
        Collections.sort(levelInfos);
        val indexes = levelInfos.stream().map(LevelInfo::getIndex).collect(Collectors.toList());
        val firstLevelIndex = indexes.indexOf(1);
        if (firstLevelIndex == -1) {
            LOG.message("validLevelInfos - missing first level")
                    .warn();
            return false;
        }
        for (int i = firstLevelIndex - 1; i >= 0; i--) {
            if (indexes.get(i) != i - firstLevelIndex) {
                LOG.message("validLevelInfos - missing underground level")
                        .with("index", i - firstLevelIndex)
                        .warn();
                return false;
            }
        }
        for (int i = firstLevelIndex + 1; i < indexes.size(); i++) {
            if (indexes.get(i) != i - firstLevelIndex + 1) {
                LOG.message("validLevelInfos - missing overground level")
                        .with("index", i - firstLevelIndex + 1)
                        .warn();
                return false;
            }
        }
        return true;
    }

    public static void correctHomeDesignData(final HomeDesignData homeDesign) {
        val levelInfos = homeDesign.getLevelInfos();
        val overgroundLevelIds = homeDesign.getOvergroundLevels();
        val undergroundLevelIds = homeDesign.getUndergroundLevels();
        overgroundLevelIds.clear();
        undergroundLevelIds.clear();
        reindexLevelInfos(levelInfos);
        levelInfos.forEach(levelInfo -> {
            if (levelInfo.getIndex() > 0) {
                overgroundLevelIds.add(levelInfo.getLevelId());
            } else {
                undergroundLevelIds.add(levelInfo.getLevelId());
            }
        });
        Collections.reverse(undergroundLevelIds);
    }

    public static void reindexLevelInfos(final List<LevelInfo> levelInfos) {
        if (CollectionUtils.isEmpty(levelInfos)) {
            return;
        }
        val indexes = levelInfos.stream().map(LevelInfo::getIndex).collect(Collectors.toList());
        val firstLevelIndex = indexes.indexOf(1);
        if (firstLevelIndex == -1) {
            LOG.message("reindexLevelInfos - missing first level")
                    .with("levelInfos", levelInfos)
                    .error();
            return;
        }
        for (int i = firstLevelIndex - 1; i >= 0; i--) {
            levelInfos.get(i).setIndex(i - firstLevelIndex);
        }
        for (int i = firstLevelIndex + 1; i < indexes.size(); i++) {
            levelInfos.get(i).setIndex(i - firstLevelIndex + 1);
        }
    }

}
