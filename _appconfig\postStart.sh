#!/bin/bash

HEALTHZ_PATH=$HEALTHZ_PATH
APP_STAGE=$APP_STAGE
DEFAULT_SHUT_DOWN_PATH="/_management_/shutDown"
DEFAULT_START_UP_PATH="/_management_/startUp"
PORT=$PORT
START_UP_PATH=$START_UP_PATH

if [ "${START_UP_PATH}"x = ""x ]; then
    START_UP_PATH=${DEFAULT_START_UP_PATH}
fi

coops_header="Authorization:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.ozWn91VE5-0M2HwtcoeswyoeHkh_lw0_e5E7LCXz9JY"
self_start_up_url="http://localhost:${PORT}${START_UP_PATH}"
self_healthz_url="http://localhost:${PORT}${HEALTHZ_PATH}"
pre_start_up_url="http://localhost:${PORT}/pre-start"

# 开始健康检查，直到成功或者超时（600秒超时）
http_code=0
for (( i = 0; i < 60; i++ )); do
		http_code=$(curl -s --max-time 10 -w "%{http_code}" -s -o /dev/null -X GET --header 'Accept: application/json' "${self_healthz_url}")
		if [ "${http_code}" = 200 ]; then
		    echo "调用健康检查接口成功：url:${self_healthz_url}, code:${http_code}" >> ./logs/grace_log_
		    break
		else
		  echo "调用健康检查接口失败：url:${self_healthz_url}, code:${http_code}" >> ./logs/grace_log_
		  sleep 10
		fi
done

# 健康检查是否通过
if [ "${http_code}" != 200 ]; then
        echo "健康检查未通过！" >> ./logs/grace_log_
		    exit 1
fi

# 由于soa的原因，调用上线之前要先调用一下下线接口
self_shut_down_url=${self_start_up_url//"${DEFAULT_START_UP_PATH}"/"${DEFAULT_SHUT_DOWN_PATH}"}
resultCode=$(curl -s --max-time 10 -X POST -w "%{http_code}" -s -o /dev/null --header 'Accept: application/json' --header ${coops_header} "${self_shut_down_url}")
if [ "${resultCode}" != 200 ]; then
      echo "调用shutDown接口出错，url:${self_shut_down_url},http_code：${resultCode}" >> ./logs/grace_log_
		  exit 1
fi


# 调用预热加载功能，对接口不敏感
resultCode=$(curl -s --max-time 30 -X GET -w "%{http_code}" -s -o /dev/null --header 'Accept: application/json' --header ${coops_header} "${pre_start_up_url}")


# 调用上线接口
result=$(curl -s --max-time 10 -X POST --header 'Accept: application/json' --header ${coops_header} "${self_start_up_url}")
if [ "${result}"x = '"ALREADY_UP"'x ] || [ "${result}"x = '"OK"'x ]; then
	    echo "soaPostStart成功：url:${self_start_up_url}, result:${result}" >> ./logs/grace_log_
else
  echo "soaPostStart失败：url:${self_start_up_url}, result:${result}" >> ./logs/grace_log_
  exit 1
fi