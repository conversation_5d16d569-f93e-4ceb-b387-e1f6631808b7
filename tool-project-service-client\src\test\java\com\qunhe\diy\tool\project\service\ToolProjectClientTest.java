/*
 * ToolProjectClientTest.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service;

import java.util.List;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.hunter.http.HeaderOnlyHttpServletRequest;
import com.qunhe.web.standard.data.Result;

/**
 * <AUTHOR>
 */
@Ignore
public class ToolProjectClientTest extends BaseTest {

    @Test
    public void createTest() {
        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .userId(1111141707L)
                .name("tps方案服务测试方案 1")
                .planType((byte) 0)
                .vrc("V0140R0104")
                .build();
        Result<ToolProjectHomeDesign> res = toolProjectCLient.create(toolProjectSaveParam);
        Assert.assertEquals("0", res.getC());
    }

    @Test
    public void copyTest() {
        ToolProjectCopyParam toolProjectCopyParam =
                ToolProjectCopyParam.builder().srcDesignId(37512376L).dstUserId(1111141707L)
                        .build();
        Result<ToolProjectHomeDesign> res = toolProjectCLient.copy(toolProjectCopyParam);
        Assert.assertEquals("0", res.getC());
    }

    @Test
    public void recoverTest() {
        toolProjectCLient.recoverProjectDesign(1111251217L, 37512376L, null);
    }

    /*@Test
    public void batchRecoverTest() {
        toolProjectCLient.batchRecoverProjectDesign(1111167298L, Arrays.asList(38431477L));
    }*/

    @Test
    public void recycleTest() {
        toolProjectCLient.recycleProjectDesign(981503274L,
                1965263680L, null);
    }

    /*@Test
    public void batchRecycleTest() {
        toolProjectCLient.batchRecycleProjectDesign(1111167298L,
                Arrays.asList(38431477L));
    }*/


    @Test
    public void deleteTest() {
        toolProjectCLient.deleteProjectDesign(1049L, 3947L, 3561954L);
    }

    @Test
    public void recoverDeleteTest() {
        toolProjectCLient.recoverDeleteProjectDesign(1049L,
                3947L, 3561954L);
    }

    @Test
    public void testBatchCreateOrUpdateLevel_update_success() {
        Long designId = 40394355L;
        LevelInfo levelInfo1 = new LevelInfo(1, "MXVLQ2VMDSWQGAABAAAAAAY8", "1F");
        LevelInfo levelInfo2 = new LevelInfo(2, null, "2F");
        final HeaderOnlyHttpServletRequest headerOnlyHttpServletRequest =
                new HeaderOnlyHttpServletRequest();
        headerOnlyHttpServletRequest.addHeader("x-qh-id", "1111111437");
        RequestContextHolder.setRequestAttributes(
                new ServletRequestAttributes(headerOnlyHttpServletRequest));
        LevelBatchUpdateResponse levelBatchUpdateResponse = toolProjectCLient.batchCreateOrUpdateLevels(designId,
                Lists.newArrayList(levelInfo1, levelInfo2));
        Assert.assertEquals("update fail", levelBatchUpdateResponse.getStatus(),
                LevelBatchUpdateResponse.UpdateStatus.SUCCEED.getStatus());
    }

    @Test
    public void testCreate_with_coohom_region() {
        Long userId = 1111111437L;
        BaseTest.injectUserIdToRequest(userId);
        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .userId(userId)
                .name("tps coohom region方案服务测试方案 1")
                .planType((byte) 0)
                .vrc("V0150R0205")
                .build();
        Result<ToolProjectHomeDesign> res = toolProjectCLient.create(toolProjectSaveParam);
        Assert.assertEquals("return success", "0", res.getC());
    }

    @Test
    public void testBatchRecycleDesigns() {
        Long userId = 1111141707L;
        List<Long> designIds = Lists.newArrayList();
        for (int j = 0; j < 20; j++) {
            designIds.add(37512376L + j);
        }
        Result<BatchDeleteDTO> res = toolProjectCLient.batchRecycleProjectDesign(userId, designIds);
        Assert.assertEquals("return success code", "0", res.getC());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testBatchRecycleDesigns_param_size_over_limit() {
        Long userId = 1111141707L;
        List<Long> designIds = Lists.newArrayList();
        for (int j = 0; j < 40; j++) {
            designIds.add(37512376L + j);
        }
        toolProjectCLient.batchRecycleProjectDesign(userId, designIds);
    }


}
