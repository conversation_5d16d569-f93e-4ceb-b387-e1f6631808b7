/*
 * DcsOrderAuthFacade.java
 * Copyright 2022 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.custom.dcs.order.client.client.PrivilegeCheckClient;
import com.qunhe.custom.dcs.order.client.exception.DcsOrderApiException;
import com.qunhe.utils.HunterLogger;
import com.qunhe.web.standard.data.Result;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/22
 */
@Component
public class DcsOrderAuthFacade {

    private static final HunterLogger LOG = HunterLogger.getLogger(DcsOrderAuthFacade.class);

    @Autowired
    private PrivilegeCheckClient privilegeCheckClient;

    @SentinelResource(value = "dcsOrder#checkAndUpdateAuth")
    public boolean checkAndUpdateDesignAuth(Long designId, Long orderDesignId, Long userId) throws DcsOrderApiException {
        Result<Boolean> result = privilegeCheckClient.checkAndUpdateDesignAuth(designId, orderDesignId, userId);
        return result != null && BooleanUtils.toBooleanDefaultIfNull(result.getD(), false);
    }

}
