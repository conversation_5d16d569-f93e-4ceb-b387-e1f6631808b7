/*
 * BatchOperationServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.CosDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.BatchRemoveOrRestoreService;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.project.platform.project.search.common.enums.ColDict;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class BatchOperationServiceTest {

    static {
        System.setProperty("qunhe.terra.stage", "dev");
    }

    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    GeneralProperties generalProperties;
    @Mock
    BatchOperationRecordDb batchOperationRecordDb;
    @Mock
    CosDb cosDb;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    BatchRemoveOrRestoreService batchRemoveOrRestoreService;
    @InjectMocks
    BatchOperationService batchOperationService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetBatchOperateStatus() {
        final String recordId = "request3";
        final BatchOperationData batchOperationData = BatchOperationData.builder()
                .recordId(recordId)
                .status(2)
                .rollback(0)
                .build();
        when(batchOperationRecordDb.getBatchOperationRecordByRecordId(recordId)).thenReturn(
                batchOperationData);

        BatchOperationStatusEnum result = batchOperationService.getBatchOperateStatus(recordId);
        assertEquals("operate finish", BatchOperationStatusEnum.DONE, result);
    }

    @Test
    public void testGetBatchDealDesignIdsFromLinks_empty() {
        final Long tarUserId = 11L;
        final List<String> multiOperateLinks = new ArrayList<>(1);
        when(batchOperationRecordDb.getMultiOperateLinksByUserId(tarUserId, 0)).thenReturn(
                multiOperateLinks);
        final List<Long> result = batchOperationService.getBatchDealDesignIdsFromLinks(tarUserId);
        assertEquals("empty links", 0, result.size());
    }

    @Test
    public void testGetBatchDealDesignIdsFromLinks_deleted_designIds() {
        final Long tarUserId = 11L;
        final String key1 = "/tps/batch_operate/batch_delete/record1.json";
        final String key2 = "/tps/batch_operate/batch_delete/record2.json";

        final List<String> multiOperateLinks = new ArrayList<>(2);
        multiOperateLinks.add("https://qhtbdoss.kujiale.com" + key1);
        multiOperateLinks.add("https://qhtbdoss.kujiale.com" + key2);
        when(batchOperationRecordDb.getMultiOperateLinksByUserId(tarUserId, 0)).thenReturn(
                multiOperateLinks);
        when(cosDb.getBatchDealDesignIdsFromCosNoThrow(key1)).thenReturn(
                Arrays.asList(1L));
        when(cosDb.getBatchDealDesignIdsFromCosNoThrow(key2)).thenReturn(
                Arrays.asList(2L));

        final List<Long> result = batchOperationService.getBatchDealDesignIdsFromLinks(tarUserId);

        assertEquals("designIds get by links", Arrays.asList(1L, 2L), result);
    }

    @Test
    public void testBatchRemoveProjects() {
        final String recordId = "request4";
        final BatchOperationData batchOperationData = BatchOperationData.builder()
                .userId(11L)
                .originCount(10)
                .recordId(recordId)
                .rollback(0)
                .build();
        final Long tarUserId = batchOperationData.getUserId();

        when(generalProperties.getSingleQuerySizeForBatchOperate()).thenReturn(100);

        final ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(1L);
        when(projectDesignFacadeDb.searchProjectsByUserIdIgnoreDelete(0, 100, tarUserId,
                Collections.singletonList(ColDict.DESIGN_ID.getEsField()))).thenReturn(
                Arrays.asList(projectDesign));

        when(generalProperties.getMaxPatitionSizeForBatchOperate()).thenReturn(1);
        when(projectDesignFacadeDb.batchDeleteProject(Arrays.asList(1L))).thenReturn(0);
        when(generalProperties.getMaxGapTimeForBatchOperate()).thenReturn(1L);
        // Run the test
        batchOperationService.batchRemoveProjects(recordId,
                batchOperationData.getUserId(), batchOperationData);
        verify(cosDb, times(1)).uploadSuccessDealDesignIds(any(), any(), any());
    }

    @Test
    public void testBatchRollBackProjects() {
        final String recordId = "request5";
        final BatchOperationData batchOperationData = BatchOperationData.builder()
                .userId(11L)
                .originCount(10)
                .recordId(recordId)
                .rollback(1)
                .build();
        final Long tarUserId = batchOperationData.getUserId();
        final List<Long> distinctDesignIds = Arrays.asList(1L, 2L, 3L);

        when(generalProperties.getSingleQuerySizeForBatchOperate()).thenReturn(10);
        when(generalProperties.getMaxPatitionSizeForBatchOperate()).thenReturn(1);
        when(projectDesignFacadeDb.batchRollbackRemoveProject(any())).thenReturn(1);
        doAnswer(new Answer<Void>() {
            @Override
            public Void answer(InvocationOnMock invocation) throws Throwable {
                throw new DegradeException("fallback");
            }
        }).when(batchRemoveOrRestoreService).singleBatchOperateProjects(any(), any(), any(), any());
        // Run the test
        batchOperationService.batchRollBackProjects(tarUserId, recordId, batchOperationData,
                distinctDesignIds);
        verify(cosDb, times(1)).uploadSuccessDealDesignIds(any(), any(), any());

    }
}