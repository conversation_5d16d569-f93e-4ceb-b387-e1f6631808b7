/*
 * ToolProjectRestApiServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.CoverPicFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.OpenApiUserFacade;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesign;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignStatus;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiLevelBatchUpdateRequest;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.module.restapi.common.exception.RestApiProcessException;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.data.ProjectDesignUserAuth;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.web.standard.data.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ToolProjectRestApiServiceTest {

    @Mock
    private ProjectClient mockProjectClient;
    @Mock
    private ProjectAuthClient mockProjectAuthClient;
    @Mock
    private CoverPicFacade mockCoverPicFacade;
    @Mock
    private OpenApiUserFacade mockOpenApiUserFacade;
    @Mock
    private HomeDesignFacadeDb mockHomeDesignFacadeDb;
    @Mock
    private LevelDesignFacadeDb mockLevelDesignFacadeDb;

    private ToolProjectRestApiService toolProjectRestApiServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        toolProjectRestApiServiceUnderTest = new ToolProjectRestApiService(mockProjectClient,
                mockProjectAuthClient, mockCoverPicFacade, mockOpenApiUserFacade,
                mockHomeDesignFacadeDb, mockLevelDesignFacadeDb);
    }

    @Test
    public void testGetProjectDesigns_Success() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(1L);
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock project design
        ProjectDesign design = new ProjectDesign();
        design.setDesignId(1L);
        design.setPlanId(10L);
        design.setUserId(100L);
        design.setAuthorId(200L);
        design.setDeleted(0);
        design.setDesignName("Test Design");
        design.setDesignDesc("Test Description");
        design.setCommunityName("Test Community");
        design.setSysdictareaFullname("Test Area");
        design.setCreated(new Timestamp(System.currentTimeMillis()));
        design.setModifiedTime(new Timestamp(System.currentTimeMillis()));
        design.setVrc("V0150R0105");
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.singletonList(design));

        // Mock cover pics
        when(mockCoverPicFacade.getCoverPicsByPlanIds(anyList()))
                .thenReturn(Collections.singletonMap(10L, "cover.jpg"));

        // Mock app uids
        when(mockOpenApiUserFacade.getAppUidByUserIds(anyList()))
                .thenReturn(Collections.singletonMap(100L, "user100"));

        // Mock home design data
        HomeDesignData homeDesignData = new HomeDesignData();
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("level1");
        levelInfo.setIndex(1);
        levelInfo.setName("First Floor");
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));
        when(mockHomeDesignFacadeDb.batchGetHomeDesignData(anyList()))
                .thenReturn(Collections.singletonMap(1L, homeDesignData));

        // Mock level design data
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(100.0);
        levelDesignData.setIbArea(80.0);
        levelDesignData.setOutdoorArea(20.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        when(mockLevelDesignFacadeDb.batchGetLevelDesigns(anyList()))
                .thenReturn(Collections.singletonMap("level1", levelDesignData));

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds,
                operatorUserId);

        // Verify
        assertNotNull("not null", result);
        assertEquals("should equal", 1, result.size());
        RestApiDesign restApiDesign = result.get(0);
        assertEquals("should equal", "Test Design", restApiDesign.getName());
        assertEquals("should equal", "cover.jpg", restApiDesign.getCoverPic());
        assertEquals("should equal", "user100", restApiDesign.getOwnerAppUid());
        assertEquals("should equal", 1, restApiDesign.getLevels().size());
    }

    @Test(expected = RestApiProcessException.class)
    public void testGetProjectDesigns_AuthCheckFailed() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check failure
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.emptyList()));

        // Execute
        toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);
    }

    @Test
    public void testGetProjectDesigns_EmptyResult() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(1L);
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock empty project designs
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.emptyList());

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds,
                operatorUserId);

        // Verify
        assertNotNull("should not null", result);
        assertEquals("should equal", 0, result.size());
    }

    @Test
    public void testBatchUpdateLevels_Success() throws Exception {
        // Setup
        Long designId = 1L;
        RestApiLevelBatchUpdateRequest request = new RestApiLevelBatchUpdateRequest();
        request.setBatchRequests(Collections.singletonList(new LevelInfo()));

        // Mock successful update
        LevelBatchUpdateResponse response = new LevelBatchUpdateResponse();
        response.setStatus(LevelBatchUpdateResponse.UpdateStatus.SUCCEED.getStatus());
        HomeDesignData homeDesignData = new HomeDesignData();
        List<LevelInfo> levelInfos = Collections.singletonList(new LevelInfo());
        homeDesignData.setLevelInfos(levelInfos);
        response.setHomeDesignData(homeDesignData);

        when(mockHomeDesignFacadeDb.batchCreateOrUpdateLevels(eq(designId), anyList()))
                .thenReturn(response);

        // Execute
        List<LevelInfo> result = toolProjectRestApiServiceUnderTest.batchUpdateLevels(designId,
                request);

        // Verify
        assertNotNull("should not null", result);
        assertEquals("should equal", levelInfos, result);
        verify(mockHomeDesignFacadeDb).batchCreateOrUpdateLevels(eq(designId), anyList());
    }

    @Test(expected = RestApiProcessException.class)
    public void testBatchUpdateLevels_Failed() throws Exception {
        // Setup
        Long designId = 1L;
        RestApiLevelBatchUpdateRequest request = new RestApiLevelBatchUpdateRequest();
        request.setBatchRequests(Collections.singletonList(new LevelInfo()));

        // Mock failed update
        LevelBatchUpdateResponse response = new LevelBatchUpdateResponse();
        response.setStatus(LevelBatchUpdateResponse.UpdateStatus.HOME_DESIGN_NOT_EXIST.getStatus());

        when(mockHomeDesignFacadeDb.batchCreateOrUpdateLevels(eq(designId), anyList()))
                .thenReturn(response);

        // Execute - should throw exception
        toolProjectRestApiServiceUnderTest.batchUpdateLevels(designId, request);
    }

    @Test
    public void testGetProjectDesigns_DeletedStatus() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(1L);
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock project design with deleted status
        ProjectDesign design = new ProjectDesign();
        design.setDesignId(1L);
        design.setUserId(1L);
        design.setDeleted(1);
        design.setVrc("V0150");
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.singletonList(design));

        // Mock home design data
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setLevelInfos(Collections.emptyList());
        when(mockHomeDesignFacadeDb.batchGetHomeDesignData(anyList()))
                .thenReturn(Collections.singletonMap(1L, homeDesignData));

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds,
                operatorUserId);

        // Verify
        assertNotNull("should equal", result);
        assertEquals("should equal", 1, result.size());
        assertEquals("should equal", RestApiDesignStatus.DELETED, result.get(0).getStatus());
    }

    @Test
    public void testGetProjectDesigns_RecycledStatus() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(1L);
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock project design with system user
        ProjectDesign design = new ProjectDesign();
        design.setDesignId(1L);
        design.setUserId(1L);
        design.setVrc("V0150");
        design.setDeleted(0);
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.singletonList(design));

        // Mock home design data
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setLevelInfos(Collections.emptyList());
        when(mockHomeDesignFacadeDb.batchGetHomeDesignData(anyList()))
                .thenReturn(Collections.singletonMap(1L, homeDesignData));

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds,
                operatorUserId);

        // Verify
        assertNotNull("should equal", result);
        assertEquals("should equal", 1, result.size());
        assertEquals("should equal", RestApiDesignStatus.RECYCLED, result.get(0).getStatus());
    }

    @Test
    public void testGetProjectDesigns_AreaCalculation() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;

        // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(1L);
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock project design
        ProjectDesign design = new ProjectDesign();
        design.setDesignId(1L);
        design.setVrc("V0150R0105");
        design.setDeleted(0);
        design.setUserId(2L);
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.singletonList(design));

        // Mock home design data with level
        HomeDesignData homeDesignData = new HomeDesignData();
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("level1");
        levelInfo.setIndex(1);
        levelInfo.setName("level1");
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));
        when(mockHomeDesignFacadeDb.batchGetHomeDesignData(anyList()))
                .thenReturn(Collections.singletonMap(1L, homeDesignData));

        // Mock level design data with different area values
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(100.0);
        levelDesignData.setSourceArea(150.0);
        levelDesignData.setOutdoorArea(50.0);
        levelDesignData.setIbArea(200.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        when(mockLevelDesignFacadeDb.batchGetLevelDesigns(anyList()))
                .thenReturn(Collections.singletonMap("level1", levelDesignData));

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds,
                operatorUserId);

        // Verify
        assertNotNull("should equal", result);
        assertEquals("should equal", 1, result.size());
        assertEquals("should equal", 1, result.get(0).getLevels().size());
        assertEquals("should equal", 150.0, result.get(0).getLevels().get(0).getSourceArea(), 0.01);
    }

    @Test
    public void testGetProjectDesigns_NullIbArea() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;
        ProjectDesign design = setupBasicMocks(designIds, operatorUserId, "V0150R0105"); // Non-BIM

        // Mock level design data with null ibArea
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(100.0);
        levelDesignData.setSourceArea(110.0); // sourceArea > realArea, but not BIM
        levelDesignData.setIbArea(null); // Set ibArea to null
        levelDesignData.setOutdoorArea(50.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        setupLevelMocks(design, levelDesignData);

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have 1 design", 1, result.size());
        assertEquals("Should have 1 level", 1, result.get(0).getLevels().size());
        assertNull("ibArea should be null", result.get(0).getLevels().get(0).getIbArea());
    }

    @Test
    public void testGetProjectDesigns_NullSourceArea_Bim() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;
        ProjectDesign design = setupBasicMocks(designIds, operatorUserId, "V0150R0105"); // BIM

        // Mock level design data with null sourceArea
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(100.0);
        levelDesignData.setSourceArea(null); // Set sourceArea to null
        levelDesignData.setIbArea(200.0);
        levelDesignData.setOutdoorArea(50.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        setupLevelMocks(design, levelDesignData);

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have 1 design", 1, result.size());
        assertEquals("Should have 1 level", 1, result.get(0).getLevels().size());
    }

     @Test
    public void testGetProjectDesigns_NullSourceArea_NonBim() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;
        ProjectDesign design = setupBasicMocks(designIds, operatorUserId, "V0150R0105"); // Non-BIM

        // Mock level design data with null sourceArea
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(100.0);
        levelDesignData.setSourceArea(null); // Set sourceArea to null
        levelDesignData.setIbArea(200.0);
        levelDesignData.setOutdoorArea(50.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        setupLevelMocks(design, levelDesignData);

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have 1 design", 1, result.size());
        assertEquals("Should have 1 level", 1, result.get(0).getLevels().size());
    }

    @Test
    public void testGetProjectDesigns_NullRealArea() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;
        ProjectDesign design = setupBasicMocks(designIds, operatorUserId, "V0150R0105"); // Non-BIM

        // Mock level design data with null realArea and area
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(null); // Set realArea to null
        levelDesignData.setArea(null); // Set legacy area to null as well
        levelDesignData.setSourceArea(150.0); // sourceArea > realArea (which is 0)
        levelDesignData.setIbArea(200.0);
        levelDesignData.setOutdoorArea(50.0);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        setupLevelMocks(design, levelDesignData);

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have 1 design", 1, result.size());
        assertEquals("Should have 1 level", 1, result.get(0).getLevels().size());
        assertEquals("realArea should be 0.0", 0.0, result.get(0).getLevels().get(0).getRealArea(), 0.01);
    }

     @Test
    public void testGetProjectDesigns_AllAreasNull() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        Long operatorUserId = 100L;
        ProjectDesign design = setupBasicMocks(designIds, operatorUserId, "V0150R0105"); // Non-BIM

        // Mock level design data with all relevant areas null
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(1L);
        levelDesignData.setLevelId("level1");
        levelDesignData.setRealArea(null);
        levelDesignData.setArea(null);
        levelDesignData.setSourceArea(null);
        levelDesignData.setIbArea(null);
        levelDesignData.setOutdoorArea(null);
        levelDesignData.setLastModified(new Timestamp(System.currentTimeMillis()));
        setupLevelMocks(design, levelDesignData);

        // Execute
        List<RestApiDesign> result = toolProjectRestApiServiceUnderTest.getProjectDesigns(designIds, operatorUserId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should have 1 design", 1, result.size());
        assertEquals("Should have 1 level", 1, result.get(0).getLevels().size());
        assertEquals("realArea should be 0.0", 0.0, result.get(0).getLevels().get(0).getRealArea(), 0.01);
         assertNull("ibArea should be null", result.get(0).getLevels().get(0).getIbArea());
         assertNull("outdoorArea should be null", result.get(0).getLevels().get(0)
                 .getOutdoorArea());
    }

    // Helper method to reduce boilerplate setup code
    private ProjectDesign setupBasicMocks(List<Long> designIds, Long operatorUserId, String vrc) throws Exception {
         // Mock auth check
        ProjectDesignUserAuth auth = new ProjectDesignUserAuth();
        auth.setDesignId(designIds.get(0));
        auth.setAuth(AuthCheckType.READ.getAuthPoint());
        when(mockProjectAuthClient.batchQueryUserAuthForDesigns(any()))
                .thenReturn(Result.ok(Collections.singletonList(auth)));

        // Mock project design
        ProjectDesign design = new ProjectDesign();
        design.setDesignId(designIds.get(0));
        design.setPlanId(10L);
        design.setUserId(operatorUserId); // Use operatorUserId for simplicity here
        design.setAuthorId(200L);
        design.setDeleted(0);
        design.setDesignName("Test Design");
        design.setVrc(vrc); // Set VRC based on parameter
        design.setCreated(new Timestamp(System.currentTimeMillis()));
        design.setModifiedTime(new Timestamp(System.currentTimeMillis()));
        when(mockProjectClient.getProjectDesignsByDesignIds(anyList(), anyList()))
                .thenReturn(Collections.singletonList(design));

        // Mock cover pics
        when(mockCoverPicFacade.getCoverPicsByPlanIds(anyList()))
                .thenReturn(Collections.singletonMap(10L, "cover.jpg"));

        // Mock app uids
        when(mockOpenApiUserFacade.getAppUidByUserIds(anyList()))
                .thenReturn(Collections.singletonMap(operatorUserId, "user" + operatorUserId));

        return design;
    }

    // Helper method to setup level related mocks
    private void setupLevelMocks(ProjectDesign design, LevelDesignData levelDesignData) {
        // Mock home design data with level
        HomeDesignData homeDesignData = new HomeDesignData();
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId(levelDesignData.getLevelId());
        levelInfo.setIndex(1);
        levelInfo.setName("level1");
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));
        when(mockHomeDesignFacadeDb.batchGetHomeDesignData(anyList()))
                .thenReturn(Collections.singletonMap(design.getDesignId(), homeDesignData));

        // Mock level design data
        when(mockLevelDesignFacadeDb.batchGetLevelDesigns(anyList()))
                .thenReturn(Collections.singletonMap(levelDesignData.getLevelId(), levelDesignData));
    }

    // ... existing code ...
}
