/*
 * ProjectHandoverServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverDesignMessage;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverJobRspMessage;
import com.qunhe.middleware.rocketmq.client.QunheMQProducer;
import com.qunhe.project.platform.project.search.client.DesignTagClient;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ExecutorService;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProjectHandoverServiceTest {
    @Mock
    private ExecutorService handoverExecutor;
    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    DesignTagClient designTagClient;
    @Mock
    ToadProperties toadProperties;
    @Mock
    QunheMQProducer userMQProducer;
    @Mock
    QunheMQProducer designMQProducer;
    @InjectMocks
    ProjectHandoverService testProjectHandoverService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        Long designId = 35338021L;
//        ExecutorService handoverExecutor = PowerMockito.mock(ExecutorService.class);
//        PowerMockito.when(handoverExecutor.submit(Mockito.any(Task.class))).thenAnswer(invocation -> {
//            Callable callable = invocation.getArgument(0);
//            callable.call();
//
//            CompletableFuture<Long> future = CompletableFuture.completedFuture(designId);
//            future.complete(designId); // 设置返回值为1
//            return future;
//        });

        // 使用Whitebox将模拟的asyncExecutor设置到被测试对象中
        Whitebox.setInternalState(testProjectHandoverService, "handoverExecutor", handoverExecutor);
        Whitebox.setInternalState(testProjectHandoverService, "projectDesignFacadeDb", projectDesignFacadeDb);
        Whitebox.setInternalState(testProjectHandoverService, "designTagClient", designTagClient);
    }

    @Test
    public void testHandoverProject() {
        final Long fromUserId = 981503274L;
        Long designId = 35338021L;
        List<Long> returnDesignIds = new LinkedList<>();
        returnDesignIds.add(designId);
        when(projectDesignFacadeDb.getDesignsByUserId(fromUserId)).thenReturn(returnDesignIds);

        List<Long> fromUserIds = new ArrayList<>();
        fromUserIds.add(fromUserId);
        testProjectHandoverService.handoverProject(1111251217L,fromUserIds,1L);
        verify(projectDesignFacadeDb).getDesignsByUserId(fromUserId);
    }

    @Test
    public void testFillHandoverJobRsp() {
        when(projectDesignFacadeDb.getProjectByDesignId(anyLong())).thenReturn(null);

        HandoverJobRspMessage handoverJobRspMessage = HandoverJobRspMessage.builder()
                .category(1)
                .build();
        HandoverJobRspMessage result = testProjectHandoverService.fillHandoverJobRsp(handoverJobRspMessage);
        HandoverJobRspMessage message = HandoverJobRspMessage.builder().taskInfo(null).taskId(null).category(1).build();
        Assert.assertEquals("message cate is 1", message, result);
    }

    @Test
    public void testSendFeedbackMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {
        when(toadProperties.getFeedbackTopic()).thenReturn("getFeedbackTopicResponse");
        HandoverJobRspMessage handoverJobRspMessage = HandoverJobRspMessage.builder()
                .category(1)
                .build();
        testProjectHandoverService.sendFeedbackMessage(handoverJobRspMessage,1111251217L);
        verify(userMQProducer).send(any(Message.class));
    }

    @Test
    public void testSendDesignMessage() throws RemotingException, InterruptedException, MQClientException, MQBrokerException {
        when(toadProperties.getProjectHandoverTopic()).thenReturn("getProjectHandoverTopicResponse");

        HandoverDesignMessage handoverDesignMessage = HandoverDesignMessage.builder()
                .designId("3FO4MD5DUII7")
                .fromUserId(1111119001L)
                .toUserId(1111251217L)
                .build();
        testProjectHandoverService.sendDesignMessage(handoverDesignMessage);
        verify(designMQProducer).send(any(Message.class));
    }
}
