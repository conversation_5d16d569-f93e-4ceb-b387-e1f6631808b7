/*
 * VrcUtils.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.utils;

import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.PlanType;
import com.qunhe.diybe.dms.data.VrcEnum;
import lombok.var;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class VrcUtils {

    public static String getVrcWithDefault() {
        var vrc = ToolTypeContextHolder.getVrcHolder().get();
        if (StringUtils.isBlank(vrc)) {
            vrc = VrcEnum.BIM.getCode();
        }
        return vrc;
    }

    public static String getVrcWithDefault(VrcEnum defaultValue) {
        var vrc = ToolTypeContextHolder.getVrcHolder().get();
        if (StringUtils.isBlank(vrc)) {
            vrc = defaultValue.getCode();
        }
        return vrc;
    }

    public static boolean isYunDesign(DiyDesignInfo designInfo) {
        return designInfo.getPlanType() != null &&
                PlanType.getProjectType(designInfo.getPlanType()) == PlanType.ProjectType.YUN_DESIGN;
    }

}
