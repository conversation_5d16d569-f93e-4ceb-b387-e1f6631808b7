/*
 * CoohomDiyPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.util.PlanTypeUtil;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DesignInfo;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.FlashCustomizeSetting;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.diy.AppType;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diy.tool.project.service.web.page.diy.TreEnum;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.hunter.util.IpUtil;
import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.instdeco.plan.graylaunchmw.data.UserInfoData;
import com.qunhe.instdeco.plan.util.HttpUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.ALLOW;
import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.YUN_TU_PERSSION;

/**
 * <AUTHOR>
 */
@Component
public class CoohomDiyPageHandler extends AbstractDiyPageHandler {

    private static final QHLogger LOG = QHLogger.getLogger(CoohomDiyPageHandler.class);

    public CoohomDiyPageHandler(UserInfoFacade userInfoFacade,
            BusinessAccountDb businessAccountDb, BusinessAccessService businessAccessService,
            ProjectDesignFacadeDb projectDesignFacadeDb, VrcAppIdDataDb vrcAppIdDataDb,
            SessionConflictHandler sessionConflictHandler, GrayLunchService grayLunchService,
            ToadProperties toadProperties, UserDb userDb, SaaSConfigService saaSConfigService,
            ToolLinkConfigCache toolLinkConfigCache, SaasConfigFacade saasConfigFacade,
            AuthCheckService authCheckService) {
        super(userInfoFacade, businessAccountDb, businessAccessService,
                projectDesignFacadeDb, vrcAppIdDataDb, sessionConflictHandler, grayLunchService,
                toadProperties, userDb, saaSConfigService, toolLinkConfigCache, saasConfigFacade,
                authCheckService);
    }


    private ToolPageResult handlePlanSpecificRedirectsOrErrors(DiyDesignInfo diyDesignInfo,
            HttpServletRequest request, Long userId)
            throws IOException {
        if (PlanTypeUtil.isBIMPlan(diyDesignInfo.getPlanType())) {
            LOG.message("CoohomHandler: BIM Plan, redirecting to Coohom BIM page.")
                    .with("designId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
            return redirectCoohomBIMPage(request);
        }

        if (PlanTypeUtil.isPhotoStudioPlan(diyDesignInfo.getPlanType())) {
            LOG.message("CoohomHandler: Photo studio plan, returning 404.")
                    .with("designId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
            return ToolPageResult.create(org.springframework.http.HttpStatus.NOT_FOUND, null,
                    "影棚方案请到营销工具后台查看");
        }
        if (PlanTypeUtil.isUpdateBackupPlan(diyDesignInfo.getPlanType())) {
            LOG.message("CoohomHandler: Update backup plan, returning 404.")
                    .with("designId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
            return ToolPageResult.create(org.springframework.http.HttpStatus.NOT_FOUND, null,
                    "升级备份方案不支持直接打开");
        }
        return null;
    }

    private ToolPageResult handleForceOrTreUpdate(DiyPageParam param, HttpServletRequest request,
            String obsDesignId, DiyDesignInfo diyDesignInfo, Long userId) throws IOException {
        List<String> tre = param.getTre();
        if (param.isForceUpdate()) {
            LOG.message("CoohomHandler: Force update plan.")
                    .with("designId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
            return redirectByUrl(redirectUpdateUrl(request, obsDesignId));
        } else if (tre != null && tre.contains(TreEnum.UPDATE_BIM.getTreId())) {
            LOG.message("CoohomHandler: TRE force update plan.")
                    .with("designId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
            final List<String> tres = tre.stream().filter(
                    t -> !TreEnum.UPDATE_BIM.getTreId().equals(t)).collect(
                    Collectors.toList());
            return redirectByUrl(
                    redirectUpdateUrlWithTre(request, diyDesignInfo.getDesignId(), tres));
        }
        return null;
    }

    private void updateVrcIfNecessary(String currentVrc, Long appIdNum, DiyPageParam param,
            String actualAppId, Long userId, DiyDesignInfo diyDesignInfo, H5Model h5Model) {
        String vrc = currentVrc;
        if (VrcEnum.YUNTU_FPI.getCode().equals(vrc)) {
            final int index = vrc.indexOf('R');
            final String dstVrcCode = vrcAppIdDataDb.getVrcByVtypeAndAppId(
                    vrc.substring(0, index), Math.toIntExact(appIdNum),
                    System.getenv(APP_STAGE));
            if (org.apache.commons.lang3.StringUtils.isEmpty(dstVrcCode)) {
                LOG.message("CoohomHandler: Invalid VRC or AppID for VRC update.")
                        .with("VRC", vrc)
                        .with("ObsAppId", param.getObsAppId())
                        .with("ActualAppId", actualAppId)
                        .with("userId", userId)
                        .error();
            }
            vrc = dstVrcCode;
            projectDesignFacadeDb.updateVrc(diyDesignInfo.getPlanId(), vrc);
            LOG.message("CoohomHandler: VRC updated successfully.")
                    .with("dstVrcCode", dstVrcCode)
                    .with("DesignId", diyDesignInfo.getDesignId())
                    .with("userId", userId)
                    .info();
        }
        h5Model.setVrc(vrc);
    }

    private void populateDesignInfoDetails(DesignInfo designInfo, DiyDesignInfo diyDesignInfo,
            DiyPageParam param, ProjectDesign projectDesign) {
        designInfo.setObsPlanId(LongCipher.DEFAULT.encrypt(diyDesignInfo.getPlanId()));
        if (param.getLevelId() == null) {
            designInfo.setLevelId(diyDesignInfo.generateLevelId());
        } else {
            designInfo.setLevelId(param.getLevelId());
        }
        designInfo.setPlanName(diyDesignInfo.getName());

        Integer copyParentId = projectDesign.getCopyLogParentId();
        if (copyParentId != null) {
            designInfo.setObsParentId(LongCipher.DEFAULT.encrypt(Long.valueOf(copyParentId)));
        }
    }


    @Override
    protected ToolPageResult handleExistingDesign(HttpServletRequest request,
            HttpServletResponse response,
            DiyPageParam param,
            String actualAppId,
            Long appIdNum,
            Long rootAccountId,
            boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException {
        Long userId = param.getUserId();
        final String obsDesignId = param.getObsDesignId();
        final H5Model h5Model = new H5Model();
        h5Model.setBimAccess(bimAccess);

        long designId = LongCipher.DEFAULT.decrypt(obsDesignId);

        if (!authCheckService.checkAuthFromDesignId(userId, param.getOrderDesignId(), designId,
                param.isCooperate())) {
            LOG.message("CoohomHandler: Auth check failed for existing design.")
                    .with("userId", userId)
                    .with("designId", designId)
                    .warn();
            return redirectDefaultPage(request);
        }

        final DiyDesignInfo diyDesignInfo = projectDesignFacadeDb.getDiyDesignInfo(designId);
        final DesignInfo designInfo = new DesignInfo();
        designInfo.setObsDesignId(obsDesignId);

        if (diyDesignInfo != null) {
            ToolPageResult planSpecificResponse = handlePlanSpecificRedirectsOrErrors(
                    diyDesignInfo, request, userId);
            if (planSpecificResponse != null) {
                return planSpecificResponse;
            }

            ProjectDesign projectDesign = projectDesignFacadeDb.getVrcAndParentId(
                    diyDesignInfo.getDesignId());

            if (bimAccess) {
                ToolPageResult updateResponse = handleForceOrTreUpdate(param, request,
                        obsDesignId, diyDesignInfo, userId);
                if (updateResponse != null) {
                    return updateResponse;
                }
            }

            populateDesignInfoDetails(designInfo, diyDesignInfo, param, projectDesign);

            String initialVrc = projectDesign.getVrc();
            updateVrcIfNecessary(initialVrc, appIdNum, param, actualAppId,
                    userId, diyDesignInfo, h5Model);
        }
        h5Model.setDesignInfo(designInfo);

        return finalizeH5ModelAndRespond(request, param, userId, rootAccountId, h5Model,
                obsDesignId);
    }

    @Override
    protected ToolPageResult handleNewDesign(HttpServletRequest request,
            HttpServletResponse response,
            DiyPageParam param,
            String actualAppId,
            Long appIdNum,
            Long rootAccountId,
            boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException {
        Long userId = param.getUserId();
        final H5Model h5Model = new H5Model();
        h5Model.setBimAccess(bimAccess);

        // Coohom specific: BIM redirect logic for new designs
        if (AppType.isBimApp(appIdNum)) {
            LOG.message("CoohomHandler: New design, BIM App type, redirecting to Coohom BIM page.")
                    .with("appIdNum", appIdNum)
                    .with("userId", userId)
                    .info();
            return redirectCoohomBIMPage(request);
        }
        if (bimAccess && param.isRedirectBim() && needRedirectBIM(request, userId, rootAccountId)) {
            LOG.message("CoohomHandler: New design, BIM access and redirectBim=true, " +
                            "redirecting to Coohom BIM page.")
                    .with("userId", userId)
                    .info();
            return redirectCoohomBIMPage(request);
        }

        return finalizeH5ModelAndRespond(request, param, userId, rootAccountId, h5Model, null);
    }

    public ToolPageResult finalizeH5ModelAndRespond(HttpServletRequest request,
            DiyPageParam param,
            Long userId,
            Long rootAccountId,
            H5Model h5Model,
            String obsDesignId) throws IOException {
        LOG.message("CoohomHandler: Finalizing H5Model.")
                .with("userId", userId)
                .with("DesignId", obsDesignId)
                .with("ToolName", param.getToolName())
                .with("Referer", HttpUtil.getReferer(request))
                .info();

        final UserInfoData userInfoData = new UserInfoData();
        userInfoData.setRootAccountId(rootAccountId);
        userInfoData.setUserId(userId);
        final boolean yunTuPermission = grayLunchService.checkVersion(YUN_TU_PERSSION, ALLOW,
                new HttpRequestMetaData(request, userInfoData));

        h5Model.setProjectStage(toadProperties.getProjectStage());
        h5Model.setRedirectUrl(param.getRedirectUrl());
        h5Model.setStayDiy(param.getStayDiy());

        fillUserInfo(userId, h5Model);

        h5Model.setStage(param.getStage());
        h5Model.setVrcConfig(toadProperties.getVrcConfig());
        h5Model.setIsYunTuUser(yunTuPermission);
        h5Model.setAbTestConfig(
                saaSConfigService.getAbTestResult(YUN_DESIGN_APP_KEY,
                        request.getHeader(COOKIE_HEADER_KEY), userId,
                        IpUtil.getUserIp(request)));

        if (obsDesignId != null && h5Model.getDesignInfo() != null) {
            final String upgradeSuperFloorPlanAb = h5Model.getAbTestConfig().get(
                    UPGRADE_SUPER_FLOORPLAN);
            final String upgradePublicDecorationAb = h5Model.getAbTestConfig().get(
                    UPGRADE_PUBLIC_DECORATION);
            if (checkUpgradeAccess(upgradeSuperFloorPlanAb, upgradePublicDecorationAb,
                    h5Model.getVrc())) {
                LOG.message("CoohomHandler: AB test upgrade access.")
                        .with("designId", obsDesignId)
                        .with("userId", userId)
                        .info();
                return redirectByUrl(
                        redirectSuperOrPublicUpgradeUrl(obsDesignId, upgradeSuperFloorPlanAb,
                                upgradePublicDecorationAb, request));
            }
        }

        if (saaSConfigService.checkRecycleDiyAuthWithAbConfig(userId, h5Model.getAbTestConfig())) {
            if (obsDesignId == null) {
                LOG.message(
                                "CoohomHandler: Recycle DIY Auth (AB), new design, redirecting to" +
                                        " Coohom BIM page.")
                        .with("userId", userId)
                        .info();
                return redirectCoohomBIMPage(request);
            } else {
                if (toadProperties.isEnablePageOpenRecycleDiy()) {
                    LOG.message(
                                    "CoohomHandler: Recycle DIY Auth (AB), existing design, " +
                                            "redirecting to update URL.")
                            .with("designId", obsDesignId)
                            .with("userId", userId)
                            .info();
                    return redirectByUrl(redirectUpdateUrl(request, obsDesignId));
                }
            }
        }

        fillToolConfig(h5Model);
        fillFavorIcon(userId, h5Model);
        fillAccountInfo(userId, rootAccountId, h5Model);
        fillPlanInfo(param.getObsSrcPlanId(), param.getPlanName(), param.getAskId(),
                param.getPlanType(), h5Model);

        if (CooperateHelper.isCooperate()) {
            h5Model.setObsOwnerUserId(
                    LongCipher.DEFAULT.encrypt(CooperateHelper.getCooperateOwner()));
        }
        final FlashCustomizeSetting customerService = new FlashCustomizeSetting();
        customerService.setShow(true);
        h5Model.setCustomerServiceJson(JsonMapper.writeValueAsString(customerService));

        h5Model.setRedirectDecoration(useNewDecorationVersion(request));
        LOG.message("CoohomHandler: Returning H5Model:")
                .with("H5Model", h5Model.toString())
                .debug();
        return ToolPageResult.success(JsonMapper.writeValueAsString(h5Model));
    }
} 