/*
 * DesignMetaDataDb.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.DesignMetaDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */
@Component
public class DesignMetaDataDb {

    @Autowired
    private DesignMetaDataMapper designMetaDataMapper;


    public int insertLastOpenedLevelId(final Long planId, final String levelId) {
        return designMetaDataMapper.insertLastOpenedLevelId(planId, levelId);
    }

    public String getLastOpenedLevelId(final Long planId) {
        return designMetaDataMapper.getLastOpenedLevelId(planId);
    }

    public int updateLastOpenedLevelId(final Long planId, final String LastOpenedLevelId) {
        return designMetaDataMapper.updateLastOpenedLevelId(planId, LastOpenedLevelId);
    }

}
