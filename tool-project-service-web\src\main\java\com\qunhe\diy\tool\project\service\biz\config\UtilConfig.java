/*
 * UtilConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.diy.tool.project.service.biz.service.ProjectAuthService;
import com.qunhe.diy.tool.project.service.biz.util.DegradeAuthPermissionEvaluator;
import com.qunhe.hunter.constant.TraceKeys;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.log.QHLogger;
import com.qunhe.middleware.common.starter.CommonAware;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.security.AccessDeniedException;
import com.qunhe.project.platform.project.auth.security.AccessDenyExceptionHandler;
import com.qunhe.project.platform.project.auth.security.AuthPermissionEvaluator;
import com.qunhe.project.platform.project.auth.security.DefaultAuthPermissionEvaluator;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.utils.uniqueid.StandaloneUniqueIdGenerator;
import com.qunhe.web.standard.data.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;
import java.net.SocketException;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * Created on 2018.10.26.
 */
@Configuration
public class UtilConfig {
    private static final QHLogger LOG = QHLogger.getLogger(UtilConfig.class);

    @Bean
    public LongCrypt longCrypt() {
        return new LongCrypt(71284230948672L, 35);
    }

    @Bean
    public ProjectAuthService projectAuthService(ProjectAuthClient client, LongCrypt longCrypt, ToadProperties toadProperties) {
        return new ProjectAuthService(client, longCrypt);
    }


    @Bean
    public StandaloneUniqueIdGenerator uniqueIdGenerator()
            throws UnsupportedEncodingException, SocketException {
        return new StandaloneUniqueIdGenerator(4,
                "y834lkfsdjl8534u9fwejl978423789543ilusfdu2345hkljlkjlkjl2173089askdjflkja1928039189023818190283kja;slkdjf1293102983190283asjldkfjksadjflk129083099fjlelicsdv978432579ofsesvd7u9243879543tryiufweruio789534ufeuoiyf34o");
    }

    @Bean
    public AuthPermissionEvaluator authPermissionEvaluator(ProjectAuthClient client){
        return new DegradeAuthPermissionEvaluator(new DefaultAuthPermissionEvaluator(client));
    }

    @Bean
    public AccessDenyExceptionHandler accessDenyExceptionHandler() {
        return new CustomAccessDenyExceptionHandler();
    }

    static class CustomAccessDenyExceptionHandler implements AccessDenyExceptionHandler {
        @Override
        public ResponseEntity<Object> handleBadRequest(HttpServletRequest req,
                AccessDeniedException e) {
            //打印hunterid 和 上游api
            LOG.message("Auth Access check fail")
                    .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                    .with("traceId", SpanContextHolder.getTraceId())
                    .warn();
            return new ResponseEntity<>(Result.error(e.getMessage()), HttpStatus.FORBIDDEN);
        }
    }
}
