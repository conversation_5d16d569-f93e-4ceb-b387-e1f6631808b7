FROM registry.qunhequnhe.com/public/maven:3.5.2 as builder
COPY . /usr/src/app
WORKDIR /usr/src/app
RUN mvn clean package -U -DskipTests

FROM registry.qunhequnhe.com/infra/soa-jetty:0.3.1
COPY --from=builder /usr/src/app/tool-project-service-web/target/tool-project-service.war \
    /var/lib/jetty/webapps/root.war
COPY _appconfig/start.ini /var/lib/jetty/start.ini

COPY --from=builder /usr/src/app/_appconfig/preStop.sh /var/lib/jetty/
COPY --from=builder /usr/src/app/_appconfig/postStart.sh /var/lib/jetty/
COPY --from=builder /usr/src/app/_appconfig/jetty-webapp.xml /var/lib/jetty/etc/jetty-webapp.xml

RUN curl -v http://nexus.qunhequnhe.com/repository/tools/jacocoagent.jar -o /var/lib/jetty/jacoco.agent.jar
