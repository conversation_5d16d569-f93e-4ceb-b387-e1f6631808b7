/*
 * ToolPageClient.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.client;

import com.qunhe.diy.tool.project.service.api.ToolPageAPi;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.factory.ApiFactory;
import com.qunhe.rpc.common.builder.ParamPairsBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Component
public class ToolPageClient {

    public final ToolPageAPi toolPageAPi;

    @Autowired
    public ToolPageClient(final ApiFactory apiFactory) {
        this.toolPageAPi = apiFactory.getApiInstance(ToolPageAPi.class);
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getBimKujialeToolPage(
            HttpServletRequest request
    ) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        final ParamPairsBuilder builder = new ParamPairsBuilder(parameterMap.size());
        parameterMap.forEach((key, value) -> builder.append(key, String.join(", ", value)));
        return toolPageAPi.getBimKujialeToolPage(builder.build());
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getBimCoohomToolPage(
            HttpServletRequest request
    ) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        final ParamPairsBuilder builder = new ParamPairsBuilder(parameterMap.size());
        parameterMap.forEach((key, value) -> builder.append(key, String.join(", ", value)));
        return toolPageAPi.getBimCoohomToolPage(builder.build());
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getDiyKujialeToolPage(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        final ParamPairsBuilder builder = new ParamPairsBuilder(parameterMap.size());
        parameterMap.forEach((key, value) -> builder.append(key, String.join(", ", value)));
        return toolPageAPi.getDiyKujialeToolPage(builder.build());
    }

    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getDiyCoohomToolPage(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        final ParamPairsBuilder builder = new ParamPairsBuilder(parameterMap.size());
        parameterMap.forEach((key, value) -> builder.append(key, String.join(", ", value)));
        return toolPageAPi.getDiyCoohomToolPage(builder.build());
    }


}
