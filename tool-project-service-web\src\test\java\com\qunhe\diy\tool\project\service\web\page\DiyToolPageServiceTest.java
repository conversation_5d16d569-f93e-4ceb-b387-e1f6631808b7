package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.diy.YunDesignDb;
import com.qunhe.diy.tool.project.service.web.page.handler.CoohomDiyPageHandler;
import com.qunhe.diy.tool.project.service.web.page.handler.KujialeDiyPageHandler;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DiyToolPageServiceTest {

    @InjectMocks
    private DiyToolPageService diyToolPageService;

    @Mock
    private YunDesignDb yunDesignDb;
    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private ToadProperties toadProperties;
    @Mock
    private KujialeDiyPageHandler kujialeDiyPageHandler;
    @Mock
    private CoohomDiyPageHandler coohomDiyPageHandler;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private DiyPageParam param;

    private static final Long TEST_USER_ID = 12345L;
    private static final String TEST_OBS_PLAN_ID = "obsPlanId123";
    private static final Long CREATED_DESIGN_ID = 98765L;
    private static final String ENCRYPTED_DESIGN_ID = LongCipher.DEFAULT.encrypt(CREATED_DESIGN_ID);
    private static final String MOCK_APP_ID_FROM_HEADER = "appIdFromHeader";
    private static final Long MOCK_APP_ID_NUM = 111L;
    private static final String MOCK_APP_ID_FROM_PARAM = LongCipher.DEFAULT.encrypt(
            MOCK_APP_ID_NUM);
    private static final String MOCK_OBS_DESIGN_ID = "obsDesignId123";

    @Before
    public void setUp() {
        when(param.getUserId()).thenReturn(TEST_USER_ID);
    }

    /**
     * 测试 handlePage 方法：当 obsPlanId 不为空且 obsDesignId 为空时，应创建设计并重定向。
     */
    @Test
    public void testHandlePage_withObsPlanIdAndNullObsDesignId_shouldCreateDesignAndRedirect()
            throws AccessAuthenticatorException, YunDesignException, IOException,
            URISyntaxException {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID)).thenReturn(
                CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.host.com");
        when(request.getQueryString()).thenReturn("param1=value1&planid=somePlanId");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult pageServiceResult = diyToolPageService.handlePage(request, response,
                    param);

            verify(yunDesignDb).getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID);
            assertEquals("响应状态码应为 FOUND", HttpStatus.FOUND.value(), pageServiceResult.getStatusCode());
            String expectedRedirectUrl =
                    "//test.host.com" + DiyToolPageService.H5_PAGE_PATH + ENCRYPTED_DESIGN_ID +
                            "&param1=value1";
            assertEquals("重定向URL不正确", expectedRedirectUrl, pageServiceResult.getRedirectUrl());
        }
    }

    /**
     * 测试 handlePage 方法：当 obsPlanId 不为空且 obsDesignId 为空时，且 ORIGINAL_HOST_HEADER_NAME 为空，使用
     * toadProperties.getYunHost()。
     */
    @Test
    public void testHandlePage_withObsPlanIdAndNullObsDesignId_noOriginalHost_shouldUseYunHost()
            throws AccessAuthenticatorException, YunDesignException, IOException,
            URISyntaxException {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID)).thenReturn(
                CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(null);
        when(toadProperties.getYunHost()).thenReturn("yun.another-host.com");
        when(request.getQueryString()).thenReturn("query=empty");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult pageServiceResult = diyToolPageService.handlePage(request, response,
                    param);

            verify(yunDesignDb).getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID);
            verify(toadProperties).getYunHost();
            assertEquals("响应状态码应为 FOUND", HttpStatus.FOUND.value(), pageServiceResult.getStatusCode());
            String expectedRedirectUrl =
                    "yun.another-host.com" + DiyToolPageService.H5_PAGE_PATH + ENCRYPTED_DESIGN_ID +
                            "&query=empty";
            assertEquals("重定向URL不正确", expectedRedirectUrl, pageServiceResult.getRedirectUrl());
        }
    }

    /**
     * 测试 handlePage 方法：当 obsPlanId 不为空且 obsDesignId 为空时，QueryString 为空。
     */
    @Test
    public void testHandlePage_withObsPlanIdAndNullObsDesignId_emptyQueryString()
            throws AccessAuthenticatorException, YunDesignException, IOException,
            URISyntaxException {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID)).thenReturn(
                CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.host.com");
        when(request.getQueryString()).thenReturn("");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult pageServiceResult = diyToolPageService.handlePage(request, response,
                    param);
            assertEquals("响应状态码应为 FOUND", HttpStatus.FOUND.value(), pageServiceResult.getStatusCode());
            String expectedRedirectUrl =
                    "//test.host.com" + DiyToolPageService.H5_PAGE_PATH + ENCRYPTED_DESIGN_ID + "&";
            assertEquals("重定向URL不正确", expectedRedirectUrl, pageServiceResult.getRedirectUrl());
        }
    }

    /**
     * 测试 handlePage 方法：当 obsPlanId 不为空且 obsDesignId 为空时，QueryString 为 null。
     */
    @Test
    public void testHandlePage_withObsPlanIdAndNullObsDesignId_nullQueryString()
            throws AccessAuthenticatorException, YunDesignException, IOException,
            URISyntaxException {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(TEST_OBS_PLAN_ID, TEST_USER_ID)).thenReturn(
                CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.host.com");
        when(request.getQueryString()).thenReturn(null);

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult pageServiceResult = diyToolPageService.handlePage(request, response,
                    param);
            assertEquals("响应状态码应为 FOUND", HttpStatus.FOUND.value(), pageServiceResult.getStatusCode());
            String expectedRedirectUrl =
                    "//test.host.com" + DiyToolPageService.H5_PAGE_PATH + ENCRYPTED_DESIGN_ID + "&";
            assertEquals("重定向URL不正确", expectedRedirectUrl, pageServiceResult.getRedirectUrl());
        }
    }


    /**
     * 测试 handlePage 方法：当请求非来自 Coohom 时，应调用 KujialeDiyPageHandler。
     */
    @Test
    public void testHandlePage_notFromCoohom_shouldCallKujialeHandler()
            throws AccessAuthenticatorException, YunDesignException, IOException {
        when(param.getObsDesignId()).thenReturn(MOCK_OBS_DESIGN_ID);
        when(param.isFromCoohom()).thenReturn(false);
        when(param.getObsAppId()).thenReturn(MOCK_APP_ID_FROM_PARAM);

        ToolPageResult expectedHandlerResult = ToolPageResult.builder()
            .statusCode(HttpStatus.I_AM_A_TEAPOT.value())
            .body("Kujiale Handler Response")
            .build();

        when(kujialeDiyPageHandler.handlePage(eq(request), eq(response), eq(param),
                eq(MOCK_APP_ID_FROM_PARAM), eq(MOCK_APP_ID_NUM)))
                .thenReturn(expectedHandlerResult);

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class);
                MockedStatic<StringUtils> staticStringUtils = Mockito.mockStatic(
                        StringUtils.class)) {

            staticStringUtils.when(() -> StringUtils.isNotBlank(MOCK_APP_ID_FROM_PARAM)).thenReturn(
                    true);

            ToolPageResult actualResult = diyToolPageService.handlePage(request, response,
                    param);

            verify(kujialeDiyPageHandler).handlePage(request, response, param,
                    MOCK_APP_ID_FROM_PARAM, MOCK_APP_ID_NUM);
            assertEquals("返回的PageServiceResult不正确", expectedHandlerResult, actualResult);
        }
    }

    /**
     * 测试 getAppId 方法：当 inputAppId 不为空时，应返回 inputAppId。
     */
    @Test
    public void testGetAppId_withInputAppId_shouldReturnInputAppId() {
        try (MockedStatic<StringUtils> staticStringUtils = Mockito.mockStatic(StringUtils.class)) {
            staticStringUtils.when(() -> StringUtils.isNotBlank(MOCK_APP_ID_FROM_PARAM)).thenReturn(
                    true);
            String appId = diyToolPageService.getAppId(request, MOCK_APP_ID_FROM_PARAM,
                    TEST_USER_ID);
            assertEquals("AppId 应为 inputAppId", MOCK_APP_ID_FROM_PARAM, appId);
        }
    }

    /**
     * 测试 getAppId 方法：当 inputAppId 为空，但请求头中有 appId 时，应返回请求头中的 appId。
     */
    @Test
    public void testGetAppId_withNullInputAppId_HeaderNotEmpty_shouldReturnHeaderAppId() {
        when(request.getHeader(DiyToolPageService.APP_ID_HEADER)).thenReturn(
                MOCK_APP_ID_FROM_HEADER);
        try (MockedStatic<StringUtils> staticStringUtils = Mockito.mockStatic(StringUtils.class)) {
            staticStringUtils.when(() -> StringUtils.isNotBlank(null)).thenReturn(false);
            staticStringUtils.when(() -> StringUtils.isNotBlank(MOCK_APP_ID_FROM_HEADER))
                    .thenReturn(true);
            String appId = diyToolPageService.getAppId(request, null, TEST_USER_ID);
            assertEquals("AppId 应为 header中的AppId", MOCK_APP_ID_FROM_HEADER, appId);
        }
    }

    /**
     * 测试 getAppId 方法：当 inputAppId 和请求头中 appId 均为空，且用户为 Coohom 用户时，应返回 COOHOM_DIY_APP_ID。
     */
    @Test
    public void testGetAppId_NoInputOrHeader_CoohomUser_shouldReturnCoohomAppId() {
        when(request.getHeader(DiyToolPageService.APP_ID_HEADER)).thenReturn(null);
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(true);
        try (MockedStatic<StringUtils> staticStringUtils = Mockito.mockStatic(StringUtils.class)) {
            staticStringUtils.when(() -> StringUtils.isNotBlank(null)).thenReturn(false);
            String appId = diyToolPageService.getAppId(request, null, TEST_USER_ID);
            assertEquals("AppId 应为 COOHOM_DIY_APP_ID", DiyToolPageService.COOHOM_DIY_APP_ID,
                    appId);
        }
    }

    /**
     * 测试 getAppId 方法：当 inputAppId 和请求头中 appId 均为空，且用户非 Coohom 用户时，应返回 DEFAULT_APP_ID。
     */
    @Test
    public void testGetAppId_NoInputOrHeader_NotCoohomUser_shouldReturnDefaultAppId() {
        when(request.getHeader(DiyToolPageService.APP_ID_HEADER)).thenReturn(null);
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);
        try (MockedStatic<StringUtils> staticStringUtils = Mockito.mockStatic(StringUtils.class)) {
            staticStringUtils.when(() -> StringUtils.isNotBlank(null)).thenReturn(false);
            String appId = diyToolPageService.getAppId(request, null, TEST_USER_ID);
            assertEquals("AppId 应为 DEFAULT_APP_ID", DiyToolPageService.DEFAULT_APP_ID, appId);
        }
    }

    /**
     * 测试 removePlanIdFromQuery 方法（通过 redirect 间接测试）：查询字符串包含 planid 和其他参数。
     */
    @Test
    public void testRedirect_removePlanId_withPlanIdAndOtherParams() throws Exception {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(anyString(), anyLong())).thenReturn(CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("host.com");
        when(request.getQueryString()).thenReturn("param1=val1&planid=123&param2=val2");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult toolPageResult = diyToolPageService.handlePage(request, response,
                    param);
            String expectedQueryPart = "param1=val1&param2=val2";
            String actualRedirectUrl = toolPageResult.getRedirectUrl();
            org.junit.Assert.assertNotNull("Redirect URL should not be null", actualRedirectUrl);
            assertTrue("重定向URL的查询参数部分不正确", actualRedirectUrl.endsWith(expectedQueryPart));
            assertFalse("重定向URL不应包含planid", actualRedirectUrl.contains("planid="));
        }
    }

    /**
     * 测试 removePlanIdFromQuery 方法（通过 redirect 间接测试）：查询字符串仅包含 planid。
     */
    @Test
    public void testRedirect_removePlanId_onlyPlanId() throws Exception {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(anyString(), anyLong())).thenReturn(CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("host.com");
        when(request.getQueryString()).thenReturn("planid=123");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult toolPageResult = diyToolPageService.handlePage(request, response,
                    param);
            String expectedUrlEnd = DiyToolPageService.H5_PAGE_PATH + ENCRYPTED_DESIGN_ID + "&";
            String actualRedirectUrl = toolPageResult.getRedirectUrl();
            org.junit.Assert.assertNotNull("Redirect URL should not be null", actualRedirectUrl);
            assertTrue("重定向URL的查询参数部分不正确", actualRedirectUrl.endsWith(expectedUrlEnd));
            assertFalse("重定向URL不应包含planid", actualRedirectUrl.contains("planid="));
        }
    }

    /**
     * 测试 removePlanIdFromQuery 方法（通过 redirect 间接测试）：查询字符串不包含 planid。
     */
    @Test
    public void testRedirect_removePlanId_noPlanId() throws Exception {
        when(param.getObsDesignId()).thenReturn(null);
        when(param.getObsPlanId()).thenReturn(TEST_OBS_PLAN_ID);
        when(yunDesignDb.getOrCreateDesign(anyString(), anyLong())).thenReturn(CREATED_DESIGN_ID);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("host.com");
        when(request.getQueryString()).thenReturn("param1=val1&param2=val2");

        try (MockedStatic<LongCipher> staticLongCipher = Mockito.mockStatic(LongCipher.class)) {

            ToolPageResult toolPageResult = diyToolPageService.handlePage(request, response,
                    param);
            String expectedQueryPart = "param1=val1&param2=val2";
            String actualRedirectUrl = toolPageResult.getRedirectUrl();
            org.junit.Assert.assertNotNull("Redirect URL should not be null", actualRedirectUrl);
            assertTrue("重定向URL的查询参数部分不正确", actualRedirectUrl.endsWith(expectedQueryPart));
        }
    }
} 