/*
 * YunDesignDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.diy;

import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.instdeco.designapi.clients.YunDesignClient;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/7
 */
@Component
@RequiredArgsConstructor
public class YunDesignDb {

    private static final QHLogger LOG = QHLogger.getLogger(YunDesignDb.class);

    private final YunDesignClient yunDesignClient;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    public Long getOrCreateDesign(final String obsPlanId, final Long userId)
            throws YunDesignException {
        final Long planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        Long designId = projectDesignFacadeDb.getDesignId(planId);
        if (designId != null)  {
            return designId;
        }
        return createDesign(planId, userId);
    }

    public Long createDesign(final Long planId, final Long userId)
            throws YunDesignException {
        return yunDesignClient.getOrCreateDesignWithPlanId(planId, userId);
    }




}
