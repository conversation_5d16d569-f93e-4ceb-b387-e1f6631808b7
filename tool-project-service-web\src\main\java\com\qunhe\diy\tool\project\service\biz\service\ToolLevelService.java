/*
 * ToolLevelService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.util.HomeDesignUtils;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelDesignCreateResponse;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.log.QHLogger;
import com.qunhe.rcs.data.Module;
import com.qunhe.rcsapi.clients.RevisionControlClientV2;
import com.qunhe.utils.uniqueid.NotEnoughUniqueIdException;
import com.qunhe.utils.uniqueid.UniqueIdGenerationUnrecoverableException;
import com.qunhe.utils.uniqueid.UniqueIdGenerator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.qunhe.diybe.dms.constant.CommonConstants.LEVEL_NUM_LIMIT;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
@Service
@RequiredArgsConstructor
public class ToolLevelService {

    private static final QHLogger LOG = QHLogger.getLogger(ToolLevelService.class);

    private final LevelDesignFacadeDb levelDesignFacadeDb;
    private final HomeDesignFacadeDb homeDesignFacadeDb;
    private final UniqueIdGenerator uniqueIdGenerator;
    private final RevisionControlClientV2 revisionControlClientV2;

    /**
     * 新增楼层
     * @param homeDesign HomeDesignData
     * @param userId userId
     * @param upward 向上/下新增楼层
     * @param insert 是：currentLevelId不能为空，在当前楼层上/下插入楼层；否：在最上层/最下层新增楼层
     * @param currentLevelId insert=true
     * @param levelName 楼层名称
     */
    public LevelDesignCreateResponse createLevel(final HomeDesignData homeDesign, final Long userId,
                                                 final boolean upward, final boolean insert,
                                                 final String currentLevelId, final String levelName)
            throws UniqueIdGenerationUnrecoverableException, NotEnoughUniqueIdException {
        final String levelId = uniqueIdGenerator.generateCaseInsensetiveUniqueId();

        final LevelDesignData source;
        List<LevelInfo> levelInfos = homeDesign.getLevelInfos();
        // 插入楼层
        if (insert) {
            LevelInfo levelInfo = levelInfos.stream()
                    .filter(li -> li.getLevelId().equals(currentLevelId))
                    .findFirst()
                    .orElse(null);
            if (levelInfo != null) {
                insertLevelAboveLevel(levelInfos, levelInfo, upward, levelName, levelId);
            } else {
                return new LevelDesignCreateResponse(
                        LevelDesignCreateResponse.CreateStatus.LEVEL_NO_EXIST.getStatus(),
                        null, null);
            }
            source = levelDesignFacadeDb.getLevelDesign(currentLevelId);
        } else {
            // 插入到最上层或最下层
            if (upward) {
                source = levelDesignFacadeDb.getLevelDesign(levelInfos.get(levelInfos.size() - 1).getLevelId());
                levelInfos.add(new LevelInfo(
                        levelInfos.get(levelInfos.size() - 1).getIndex() + 1, levelId, levelName));
            } else {
                source = levelDesignFacadeDb.getLevelDesign(levelInfos.get(0).getLevelId());
                levelInfos.add(0,
                        new LevelInfo(levelInfos.get(0).getIndex() - 1, levelId, levelName));
            }
        }
        if (source == null) {
            LOG.message("createLevelDesign - could not get source level")
                    .with("planId", homeDesign.getId())
                    .with("userId", userId)
                    .warn();
            return new LevelDesignCreateResponse(
                    LevelDesignCreateResponse.CreateStatus.LEVEL_NO_EXIST.getStatus(), null, null);
        }

        HomeDesignUtils.correctHomeDesignData(homeDesign);
        if (homeDesign.getOvergroundLevels().size() > LEVEL_NUM_LIMIT ||
                homeDesign.getUndergroundLevels().size() > LEVEL_NUM_LIMIT) {
            return new LevelDesignCreateResponse(
                    LevelDesignCreateResponse.CreateStatus.NUM_LIMIT_EXCEED.getStatus(),
                    null, null);
        }

        final Long planId = homeDesign.getId();
        final Long designId = homeDesign.getDesignId();
        final LevelDesignData createLevel = new LevelDesignData();
        createLevel.setFloorPlanDataId(levelId);
        createLevel.setLevelId(levelId);
        createLevel.setLastModified(new Date());
        createLevel.setPlanId(planId);
        createLevel.setDesignId(designId);
        createLevel.setPlanImgs(source.getPlanImgs());
        createLevel.setArea(source.getArea());
        createLevel.setRealArea(source.getRealArea());
        createLevel.setIbArea(source.getIbArea());
        createLevel.setSourceArea(source.getSourceArea());
        createLevel.setOutdoorArea(source.getOutdoorArea());
        createLevel.setLevelMetadata(source.getLevelMetadata());
        if (createLevel.getPlanId() == null) {
            createLevel.setPlanId(source.getPlanId());
        }
        if (createLevel.getDesignId() == null) {
            createLevel.setDesignId(source.getDesignId());
        }
        levelDesignFacadeDb.saveLevelDesign(createLevel);
        homeDesignFacadeDb.saveHomeDesign(homeDesign);
        return new LevelDesignCreateResponse(
                LevelDesignCreateResponse.CreateStatus.SUCCEED.getStatus(),
                levelId, source.getLevelId());
    }

    public boolean updateLevelInfos(final List<LevelInfo> levelInfos, final Long planId,
                                    final Long designId, final Long userId) {
        final HomeDesignData homeDesignData = homeDesignFacadeDb.getOrCreateHomeDesign(planId, designId, userId);
        if (homeDesignData == null) {
            LOG.message("updateLevelInfos - get homeDesign failed")
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        Collections.sort(levelInfos);
        if (!HomeDesignUtils.validLevelInfos(levelInfos)) {
            LOG.message("updateLevelInfos - invalid levelInfos")
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("levelInfos", levelInfos)
                    .warn();
            return false;
        }
        Set<String> originLevelIds = new HashSet<>(homeDesignData.generateLevelDataMap().values());
        Set<String> levelIds = levelInfos.stream()
                .map(LevelInfo::getLevelId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (!originLevelIds.equals(levelIds)) {
            LOG.message("updateLevelInfos - invalid levelInfos")
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("levelInfos", levelInfos)
                    .warn();
            return false;
        }
        if (levelInfos.stream().noneMatch(levelInfo -> levelInfo.getIndex().equals(1) &&
                levelInfo.getLevelId().equals(homeDesignData.defaultLevel()))) {
            LOG.message("updateLevelInfos - default level can not change")
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("levelInfos", levelInfos)
                    .warn();
            return false;
        }
        homeDesignData.setLevelInfos(levelInfos);
        return homeDesignFacadeDb.saveHomeDesign(homeDesignData);
    }

    public boolean deleteLevel(final String levelId, final Long userId) {
        final LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        if (levelDesign == null) {
            LOG.message("deleteLevelDesign - could not get levelDesign")
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        final Long designId = levelDesign.getDesignId();
        final HomeDesignData homeDesignData = homeDesignFacadeDb.getHomeDesign(designId);
        if (homeDesignData == null) {
            LOG.message("deleteLevelDesign - could not get homeDesign")
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        if (CollectionUtils.isNotEmpty(homeDesignData.getOvergroundLevels()) &&
                Objects.equals(levelId, homeDesignData.getOvergroundLevels().get(0))) {
            LOG.message("deleteLevelDesign - could not delete default level in homeDesign")
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        if (!homeDesignData.getLevelInfos().removeIf(levelInfo -> levelInfo.getLevelId().equals(levelId))) {
            LOG.message("deleteLevelDesign - levelId not exists")
                    .with("levelId", levelId)
                    .warn();
            return false;
        }
        HomeDesignUtils.correctHomeDesignData(homeDesignData);
        return homeDesignFacadeDb.saveHomeDesign(homeDesignData);
    }

    public boolean recoverLevel(final Long designId, final String levelId, final Long userId,
                                final Integer levelIndex, final String levelName) {
        final LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        if (levelDesign == null) {
            LOG.message("recoverLevel - could not get levelDesign")
                    .with("designId", designId)
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        if (!levelDesign.getDesignId().equals(designId)) {
            LOG.message("recoverLevel - check designId failed")
                    .with("designId", designId)
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }

        HomeDesignData homeDesignData = homeDesignFacadeDb.getHomeDesign(designId);
        if (homeDesignData == null) {
            LOG.message("recoverLevel - could not get homeDesignData")
                    .with("designId", designId)
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }

        if (levelIndex == 0 || levelIndex == 1 ||
                levelIndex < (-homeDesignData.getUndergroundLevels().size() - 1)
                || levelIndex > (homeDesignData.getOvergroundLevels().size() + 1)) {
            LOG.message("recoverLevel - levelIndex out of range")
                    .with("levelIndex", levelIndex)
                    .with("designId", designId)
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        if (homeDesignData.generateLevelDataMap().containsValue(levelId)) {
            LOG.message("recoverLevel - level is existed")
                    .with("levelIndex", levelIndex)
                    .with("designId", designId)
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        recoverLevelWithIndex(homeDesignData, levelId, levelIndex, levelName);
        HomeDesignUtils.correctHomeDesignData(homeDesignData);
        return homeDesignFacadeDb.saveHomeDesign(homeDesignData);
    }

    /**
     * 保存多层历史版本
     */
    @Async("multiLevelRevisionExecutor")
    public void saveMultiLevelRevision(final Long designId, final Long userId) {
        HomeDesignData homeDesignData = homeDesignFacadeDb.getHomeDesign(designId);
        if (homeDesignData == null) {
            LOG.message("saveMultiLevelRevision - homeDesignData is null")
                    .with("designId", designId)
                    .with("userId", userId)
                    .warn();
            return;
        }
        try {
            final String actionId = System.currentTimeMillis() + "-" + designId;
            byte[] data = JsonMapper.writeValueAsBytes(homeDesignData);
            revisionControlClientV2.saveModuleRevision(designId.toString(), data, Module.HOME_DESIGN_DATA,
                    actionId, false, userId);
        } catch (Exception e) {
            // 记录错误日志
            LOG.message("saveMultiLevelRevision error", e)
                    .with("designId", designId)
                    .with("userId", userId)
                    .error();
        }
    }

    /**
     * 在指定的楼层上/下 新增楼层
     * @param levelInfos 楼层
     * @param levelInfo 当前的楼层
     * @param upward 向上/下新增
     * @param levelName 新增的楼层名称
     * @param levelId 新增的楼层levelId
     */
    private void insertLevelAboveLevel(final List<LevelInfo> levelInfos, final LevelInfo levelInfo,
                                       final boolean upward, final String levelName, final String levelId) {
        int index = levelInfos.indexOf(levelInfo);
        if (upward && levelInfo.getIndex() > 0) {
            for (int i = index + 1; i < levelInfos.size(); i++) {
                levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() + 1);
            }
            levelInfos.add(index + 1,
                    new LevelInfo(levelInfo.getIndex() + 1, levelId, levelName));
            return;
        }
        if (upward) {
            for (int i = index; i >= 0; i--) {
                levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() - 1);
            }
            levelInfos.add(index + 1,
                    new LevelInfo(levelInfo.getIndex(), levelId, levelName));
            return;
        }
        if (levelInfo.getIndex() > 1) {
            for (int i = index; i < levelInfos.size(); i++) {
                levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() + 1);
            }
            levelInfos.add(index,
                    new LevelInfo(levelInfo.getIndex(), levelId, levelName));
        } else {
            for (int i = index - 1; i >= 0; i--) {
                levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() - 1);
            }
            levelInfos.add(index,
                    new LevelInfo(levelInfo.getIndex() - 1, levelId, levelName));
        }
    }
    
    /**
     * 恢复楼层到指定index
     */
    private void recoverLevelWithIndex(final HomeDesignData homeDesignData, final String levelId,
                         final Integer levelIndex, final String levelName) {
        List<LevelInfo> levelInfos = homeDesignData.getLevelInfos();

        LevelInfo levelInfo = levelInfos.stream()
                .filter(li -> Objects.equals(li.getIndex(), levelIndex))
                .findFirst()
                .orElse(null);

        if (levelInfo != null) {
            //levelInfo不为null，说明是插入
            int index = levelInfos.indexOf(levelInfo);
            if (levelInfo.getIndex() > 1) {
                for (int i = index; i < levelInfos.size(); i++) {
                    levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() + 1);
                }
                levelInfos.add(index, new LevelInfo(levelIndex, levelId, levelName));
            } else {
                for (int i = index; i >= 0; i--) {
                    levelInfos.get(i).setIndex(levelInfos.get(i).getIndex() - 1);
                }
                levelInfos.add(index + 1, new LevelInfo(levelIndex, levelId, levelName));
            }
        } else {
            //levelInfo为null，说明是在最上层或最下层重建
            if (levelIndex > 0) {
                levelInfos.add(new LevelInfo(levelIndex, levelId, levelName));
            } else {
                levelInfos.add(0, new LevelInfo(levelIndex, levelId, levelName));
            }
        }
    }

}
