/*
 * UserDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.interceptors.utils.SessionUserUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.usergrowth.uic.rpc.client.UicUserInfoClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class UserDb {
    private final static QHLogger LOG = QHLogger.getLogger(UserDb.class);

    @Autowired
    private UicUserInfoClient uicUserInfoClient;

    public static Long getUserIdBySession() {
        return SessionUserUtil.getUserId().orElse(null);
    }

    public static Long getUserIdBySession(final boolean checkCooperateStatus) {
        if (checkCooperateStatus && CooperateHelper.isCooperate()) {
            return CooperateHelper.getCooperateOwner();
        }
        return getUserIdBySession();
    }

    public static SessionUser getSessionUserBySession() {
        return SessionUserUtil.getSessionUser().orElse(null);
    }

    public UserDto getUserBySession() {
        return getUser(getUserIdBySession());
    }

    public UserDto getUser(final long userId) {
        try {

            return uicUserInfoClient.getUserById(userId);
        } catch (final Exception e) {
            // catch and return null
            LOG.message("getUser failed", e).with("userId", userId)
                    .error();
            return null;
        }
    }

}
