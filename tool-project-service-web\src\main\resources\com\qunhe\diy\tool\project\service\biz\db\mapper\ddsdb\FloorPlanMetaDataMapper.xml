<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ DesignMetaDataMapper.xml
  ~ Copyright 2019 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.1//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.FloorPlanMetaDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qunhe.diy.tool.project.service.common.data.FloorPlanMetaData">
        <id column="planid" property="planId" />
        <result column="unitid" property="unitId" />
        <result column="alphasetting" property="alphaSetting" />
        <result column="imgvisible" property="imgVisible" />
        <result column="designstage" property="designStage" />
        <result column="reverse02" property="reverse02" />
        <result column="reverse03" property="lastOpenedLevelId" />
        <result column="reverse04" property="dwDrawingVersion" />
        <result column="created" property="created" />
        <result column="lastmodified" property="lastmodified" />
        <result column="configuration" property="configuration" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        planid,
        unitid,
        alphasetting,
        imgvisible,
        designstage,
        reverse02,
        reverse03,
        reverse04,
        reverse05,
        reverse06,
        created,
        lastmodified,
        configuration
    </sql>

    <!-- 注意！尽量不要直接使用 select *,请根据实际情况选用需要的字段 -->
    <select id="selectByPlanId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM floorplan_metadata
        WHERE planid = #{planId}
        LIMIT 1
    </select>

    <insert id="addOrUpdateDwDrawingVersion">
        INSERT INTO `floorplan_metadata`
            (planid, reverse04)
        VALUES
            (#{planId}, #{drawingVersion} )
        ON DUPLICATE KEY UPDATE reverse04 = #{drawingVersion}
    </insert>

</mapper>
