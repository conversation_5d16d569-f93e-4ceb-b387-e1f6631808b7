/*
 * JedisConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.diy.tool.project.service.biz.properties.RedisProperties;
import com.qunhe.middleware.spore2.factory.SporeJedisConnectionFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import redis.clients.jedis.JedisPoolConfig;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(RedisProperties.class)
@RequiredArgsConstructor
public class JedisConfig {

    @Value("${qunhe.tps.redisId}")
    private String redisId;

    private final RedisProperties redisProperties;

    @Bean
    public JedisPoolConfig jedisPoolConfig() {
        final JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        // 最大活动对象数
        jedisPoolConfig.setMaxTotal(10);
        // 最大能够保持idel状态的对象数
        jedisPoolConfig.setMaxIdle(10);
        // 最小能够保持idel状态的对象数
        jedisPoolConfig.setMinIdle(0);
        // 当池内没有返回对象时，最大等待时间
        jedisPoolConfig.setMaxWaitMillis(100L);
        return jedisPoolConfig;
    }

    @Bean("jedisConnectionFactory")
    public JedisConnectionFactory redisConnectionFactory(JedisPoolConfig jedisPoolConfig) {
        // redisId从rops首页获取
        final JedisConnectionFactory jedisConnectionFactory = new SporeJedisConnectionFactory(
                redisId);
        // 默认db
        jedisConnectionFactory.setDatabase(0);
        // 连接超时时间
        jedisConnectionFactory.setTimeout(2000);
        // jedis连接池设置
        jedisConnectionFactory.setPoolConfig(jedisPoolConfig);
        return jedisConnectionFactory;
    }

    @Bean(name = { "toolVersionRedis", "diyRedisConnectionFactory" })
    public SporeJedisConnectionFactory diyRedisConnectionFactory() {
        return constructRedisConnectionFactory(redisProperties.getDiyRedis());
    }

    private SporeJedisConnectionFactory constructRedisConnectionFactory(
            final RedisProperties.Redis redis) {
        final SporeJedisConnectionFactory connectionFactory =
                new SporeJedisConnectionFactory(redis.getRedisId());
        // 默认db
        connectionFactory.setPoolConfig(poolConfig(redis));
        connectionFactory.setDatabase(redis.getDatabase());
        return connectionFactory;
    }

    private static JedisPoolConfig poolConfig(final RedisProperties.Redis redis) {
        final JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(redis.getMaxIdle());
        poolConfig.setMaxTotal(redis.getMaxTotal());
        poolConfig.setMaxWaitMillis(redis.getMaxWaitMillis());
        poolConfig.setTestOnBorrow(redis.isTestOnBorrow());
        return poolConfig;
    }

    @Bean
    public RedisTemplate<Object, Object> redisTemplate(
            @Qualifier("jedisConnectionFactory") JedisConnectionFactory jedisConnectionFactory) {
        final RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(jedisConnectionFactory);
        return template;
    }

    @Bean
    public RedisMessageListenerContainer container(JedisConnectionFactory jedisConnectionFactory) {
        final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(jedisConnectionFactory);
        return container;
    }

    @Bean(name = { "diyCacheRedisConnectionFactory" })
    public SporeJedisConnectionFactory diyCacheRedisConnectionFactory() {
        RedisProperties.Redis redis = redisProperties.getDiyCacheRedis();
        final SporeJedisConnectionFactory connectionFactory = new SporeJedisConnectionFactory
                (redis.getRedisId());
        final JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(redis.getMaxIdle());
        poolConfig.setMaxTotal(redis.getMaxTotal());
        poolConfig.setMaxWaitMillis(redis.getMaxWaitMillis());
        poolConfig.setTestOnBorrow(redis.isTestOnBorrow());
        connectionFactory.setPoolConfig(poolConfig);
        connectionFactory.setDatabase(redis.getDatabase());
        return connectionFactory;
    }


    @Bean("commonCacheRedisTemplate")
    public StringRedisTemplate commonCacheRedisTemplate(
            @Qualifier("diyCacheRedisConnectionFactory")
            final JedisConnectionFactory redisConnectionFactory) {
        return new StringRedisTemplate(redisConnectionFactory);
    }


}
