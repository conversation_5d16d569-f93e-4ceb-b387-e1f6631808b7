/*
 * ToolPageSwitch.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.toggle;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.log.QHLogger;
import com.qunhe.middleware.toad.client.DefaultToadClient;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Component
public class ToolPageSwitch {

    public static final QHLogger LOG = QHLogger.getLogger(ToolPageSwitch.class);

    public static final String TOOL_PAGE_SWITCH = "toolPageSwitch";

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final int PERCENT_100 = 100;

    private static final String DEV = "dev";
    private static final String SIT = "sit";

    private static final String TYPE = "app";
    private static final String ID = "tool-be.diy.tool-project-service";
    private static final String TOKEN =
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9" +
                    ".eyJ1c2VybmFtZSI6ImFwcC10b29sLWJlLmRpeS50b29sLXByb2plY3Qtc2VydmljZS1wYXJlbnQifQ.wrTSyJlN8ZE7ejalqfUX1_x3GStbWMOEWr2cfs9kW6k";

    private static final String STAGE = "APP_STAGE";

    private SwitchData switchData;

    private DefaultToadClient getTpsToadClient() {
        String stage = System.getenv(STAGE);
        if (StringUtils.isEmpty(stage)) {
            throw new IllegalArgumentException("cant get serviceStage");
        }
        //内网统一用sit
        if (StringUtils.equals(DEV, stage)) {
            stage = SIT;
        }
        final DefaultToadClient toadClient = new DefaultToadClient(TYPE, ID,
                stage, TOKEN);
        toadClient.init();
        return toadClient;
    }

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @PostConstruct
    public void init() {
        DefaultToadClient tpsToadClient = getTpsToadClient();
        try {
            switchData = MAPPER.readValue(tpsToadClient.getConfigByKey(TOOL_PAGE_SWITCH),
                    SwitchData.class);
            LOG.message("tool page switch data init success")
                    .with("switchData", switchData)
                    .info();
        } catch (Exception e) {
            LOG.message("tool page switch data init error", e)
                    .error();
            throw new IllegalStateException(e);
        }

        tpsToadClient.addConfigChangeListener(map -> {
            try {
                if (map.containsKey(TOOL_PAGE_SWITCH)) {
                    switchData = MAPPER.readValue(map.get(TOOL_PAGE_SWITCH),
                            SwitchData.class);
                    LOG.message("tool page switch data changed")
                            .with("switchData", switchData)
                            .info();
                }
            } catch (Exception e) {
                LOG.message("tool page switch data changed error", e)
                        .error();
            }

        });
    }


    public boolean shouldRedirect(Long userId, Long rootAccountId) {
        if (userId == null && rootAccountId == null) {
            LOG.message("tool page switch data：userId and rootAccountId are both null, return " +
                            "false")
                    .warn();
            return false;
        }

        //判断accountId
        if (inAccountBlackList(rootAccountId, switchData)) {
            return false;
        }
        //判断userid
        if (inUserBlackList(userId, switchData)) {
            return false;
        }

        if (inAccountWhiteList(rootAccountId, switchData)) {
            return true;
        }

        if (inUserWhiteList(userId, switchData)) {
            return true;
        }

        // 个人用户
        if (rootAccountId == null) {
            return isUserPercentageMatch(userId, switchData);
        } else {
            if (isAccountPercentageMatch(rootAccountId, switchData)) {
                if (switchData.isIncludeSka()) {
                    return switchData.getSkaAccountList().contains(rootAccountId);
                } else {
                    return true;
                }
            } else {
                return false;
            }
        }

    }

    private boolean inUserWhiteList(@Nullable Long userId, SwitchData switchData) {
        return userId != null && switchData.getWhiteUserList().contains(userId);
    }

    private boolean inUserBlackList(@Nullable Long userId, SwitchData switchData) {
        return userId != null && switchData.getBlackUserList().contains(userId);
    }

    private boolean inAccountWhiteList(Long rootAccountId, SwitchData switchData) {
        return rootAccountId != null && switchData.getWhiteAccountList().contains(rootAccountId);
    }

    private boolean inAccountBlackList(Long rootAccountId, SwitchData switchData) {
        return rootAccountId != null && switchData.getBlackAccountList().contains(rootAccountId);
    }

    private boolean isAccountPercentageMatch(Long rootAccountId, SwitchData switchData) {
        return rootAccountId != null && rootAccountId % PERCENT_100 < switchData.getKaPercentage();
    }

    private boolean isUserPercentageMatch(Long userId, SwitchData switchData) {
        return userId != null && userId % PERCENT_100 < switchData.getPercentage();
    }


}