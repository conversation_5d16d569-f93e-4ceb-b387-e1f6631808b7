/*
 * KujialeDiyPageHandlerTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.util.PlanTypeUtil;
import com.qunhe.diy.tool.project.service.web.context.ToolType;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.diy.AppType;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diy.tool.project.service.web.page.diy.TreEnum;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.ALLOW;
import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.YUN_TU_PERSSION;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * KujialeDiyPageHandler 的单元测试类。
 * <AUTHOR>
 * @date 2025/5/12
 */
@RunWith(MockitoJUnitRunner.class)
public class KujialeDiyPageHandlerTest {

    @InjectMocks
    private KujialeDiyPageHandler kujialeDiyPageHandler;

    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private BusinessAccountDb businessAccountDb;
    @Mock
    private BusinessAccessService businessAccessService;
    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    private VrcAppIdDataDb vrcAppIdDataDb;
    @Mock
    private SessionConflictHandler sessionConflictHandler;
    @Mock
    private GrayLunchService grayLunchService;
    @Mock
    private ToadProperties toadProperties;
    @Mock
    private UserDb userDb;
    @Mock
    private SaaSConfigService saaSConfigService;
    @Mock
    private ToolLinkConfigCache toolLinkConfigCache;
    @Mock
    private SaasConfigFacade saasConfigFacade;
    @Mock
    private AuthCheckService authCheckService;

    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private DiyPageParam param;
    
    private static final Long USER_ID = 1L;
    private static final Long ROOT_ACCOUNT_ID = 10L;
    private static final Long DESIGN_ID_DECRYPTED = 123L;
    private static final String OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(DESIGN_ID_DECRYPTED);
    private static final String ENCRYPTED_USER_ID = LongCipher.DEFAULT.encrypt(USER_ID);
    private static final String ENCRYPTED_PLAN_ID_RAW = LongCipher.DEFAULT.encrypt(DESIGN_ID_DECRYPTED + 1);
    private static final String ACTUAL_APP_ID = "actualAppId";
    private static final Long APP_ID_NUM = 100L;
    
    private static final Byte MOCK_BIM_PLAN_TYPE = 10; 
    private static final Byte MOCK_PHOTO_STUDIO_PLAN_TYPE = 20; 


    @Before
    public void setUp() {
        when(param.getUserId()).thenReturn(USER_ID);
        lenient().when(param.getObsDesignId()).thenReturn(OBS_DESIGN_ID);
        lenient().when(param.isFromCoohom()).thenReturn(false);

        lenient().when(businessAccountDb.getRootAccountForBAndC(USER_ID)).thenReturn(ROOT_ACCOUNT_ID);
        lenient().when(businessAccessService.checkBimAccess(USER_ID)).thenReturn(false);

        lenient().when(toadProperties.getDomainHost()).thenReturn("https://test.coohom.com");
        lenient().when(toadProperties.getYunHost()).thenReturn("https://yun.coohom.com");
        lenient().when(toadProperties.getLoginUrl()).thenReturn("https://www.coohom.com/login");
        lenient().when(toadProperties.getProjectStage()).thenReturn("test");
        lenient().when(toadProperties.getVrcConfig()).thenReturn("test_vrc_config");

        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(USER_ID);
        sessionUser.setUserVersion((byte)1);
        UserDto userDto = new UserDto();
        userDto.setUserName("testUser");
        lenient().when(userDb.getUserBySession()).thenReturn(userDto);
        lenient().when(userInfoFacade.checkKubiInfo(USER_ID)).thenReturn(100);
        lenient().when(userInfoFacade.getUserNameByUserId(USER_ID)).thenReturn("Test User");

        com.qunhe.instdeco.plan.businessconfig.BusinessConfig toolLinkConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfig.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap allToolConfigs = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap diyConfigs = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement recentChangesConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement renderFeedbackConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement diyCreate3dPanoConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);

        lenient().when(toolLinkConfigCache.getDefaultConfig()).thenReturn(toolLinkConfig);
        lenient().when(toolLinkConfig.getAllToolConfig()).thenReturn(allToolConfigs);
        lenient().when(toolLinkConfig.getDiyConfig()).thenReturn(diyConfigs);
        lenient().when(allToolConfigs.get("recentChanges")).thenReturn(recentChangesConfig);
        lenient().when(recentChangesConfig.getShow()).thenReturn(true);
        lenient().when(diyConfigs.get("renderFeedback")).thenReturn(renderFeedbackConfig);
        lenient().when(renderFeedbackConfig.getShow()).thenReturn(true);
        lenient().when(diyConfigs.get("diyCreate3dPano")).thenReturn(diyCreate3dPanoConfig);
        lenient().when(diyCreate3dPanoConfig.getShow()).thenReturn(true);

        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap favorIconConfigMap = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement favorIconElement = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement.class);
        lenient().when(saaSConfigService.getBusinessConfigMap(USER_ID, SaaSConfigService.COMMON_CONFIG_FAVOR_ICON))
                .thenReturn(favorIconConfigMap);
        lenient().when(favorIconConfigMap.isNotEmpty()).thenReturn(true);
        lenient().when(favorIconConfigMap.get(SaaSConfigService.COMMON_CONFIG_FAVOR_ICON)).thenReturn(favorIconElement);
        lenient().when(favorIconElement.getShow()).thenReturn(true);
        lenient().when(favorIconElement.getPicUrl()).thenReturn("http://example.com/icon.png");

        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap langConfigMap = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement langElement = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement.class);
        lenient().when(saaSConfigService.getBusinessConfigMap(USER_ID, SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE))
                .thenReturn(langConfigMap);
        lenient().when(langConfigMap.isEmpty()).thenReturn(false);
        lenient().when(langConfigMap.get(SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE)).thenReturn(langElement);
        lenient().when(langElement.getStatus()).thenReturn(0);

        lenient().when(saaSConfigService.getAbTestResult(anyString(), any(), anyLong(), anyString()))
                .thenReturn(new HashMap<>());

        lenient().when(grayLunchService.checkVersion(eq(YUN_TU_PERSSION), eq(ALLOW), any(HttpRequestMetaData.class)))
                .thenReturn(true);
    }

    /**
     * 测试 handleExistingDesign 方法：权限检查失败场景。
     * 预期：重定向到默认页面。
     */
    @Test
    public void testHandleExistingDesign_AuthFailed() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(false);
        when(projectDesignFacadeDb.getPlanId(DESIGN_ID_DECRYPTED)).thenReturn(null);
        when(request.getQueryString()).thenReturn("designid=" + OBS_DESIGN_ID); 

        ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

        assertNotNull("PageServiceResult不应为null", result);
        assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
        assertTrue("Location头应包含登录URL", result.getRedirectUrl().startsWith(toadProperties.getLoginUrl()));
    }

    /**
     * 测试 handleExistingDesign 方法：方案是BIM方案。
     * 预期：重定向到BIM页面。
     */
    @Test
    public void testHandleExistingDesign_BimPlan() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(diyDesignInfo.getUserId()).thenReturn(USER_ID);
        when(diyDesignInfo.getPlanType()).thenReturn(MOCK_BIM_PLAN_TYPE);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);

        try (MockedStatic<PlanTypeUtil> planTypeUtilMockedStatic = mockStatic(PlanTypeUtil.class)) {
            planTypeUtilMockedStatic.when(() -> PlanTypeUtil.isBIMPlan(MOCK_BIM_PLAN_TYPE)).thenReturn(true);

            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            assertTrue("Location头应包含BIM路径", result.getRedirectUrl().contains(AbstractDiyPageHandler.BIM_PATH));
        }
    }

    /**
     * 测试 handleExistingDesign 方法：强制更新BIM方案且用户有BIM权限。
     * 预期：重定向到更新URL。
     */
    @Test
    public void testHandleExistingDesign_ForceUpdate_BimAccessTrue() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        when(param.isForceUpdate()).thenReturn(true);

        ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, true);

        assertNotNull("PageServiceResult不应为null", result);
        assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
        String location = result.getRedirectUrl();
        assertTrue("Location头应包含升级路径", location.contains(AbstractDiyPageHandler.UPGRADE_PATH));
        assertTrue("Location头应包含加密的设计ID", location.contains(OBS_DESIGN_ID));
    }
    
    /**
     * 测试 handleExistingDesign 方法：TRE参数要求更新BIM方案。
     * 预期：重定向到带有TRE参数的更新URL。
     */
    @Test
    public void testHandleExistingDesign_TreUpdateBim_BimAccessTrue() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate())).thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        when(param.isForceUpdate()).thenReturn(false);
        when(param.getTre()).thenReturn(Arrays.asList(TreEnum.UPDATE_BIM.getTreId()));

        ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, true);

        assertNotNull("PageServiceResult不应为null", result);
        assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
        String location = result.getRedirectUrl();
        assertTrue("Location头应包含升级路径", location.contains(AbstractDiyPageHandler.UPGRADE_PATH));
        assertTrue("Location头应包含加密的设计ID", location.contains(OBS_DESIGN_ID));
        assertTrue("Location头不应再包含UPDATE_BIM的tre参数", !location.contains(TreEnum.UPDATE_BIM.getTreId()+"&") && !location.contains("="+TreEnum.UPDATE_BIM.getTreId()));
    }

    /**
     * 测试 handleExistingDesign 方法：摄影棚方案。
     * 预期：返回404。
     */
    @Test
    public void testHandleExistingDesign_PhotoStudioPlan() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(diyDesignInfo.getPlanType()).thenReturn(MOCK_PHOTO_STUDIO_PLAN_TYPE);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);

        try (MockedStatic<PlanTypeUtil> planTypeUtilMockedStatic = mockStatic(PlanTypeUtil.class)) {
            planTypeUtilMockedStatic.when(() -> PlanTypeUtil.isPhotoStudioPlan(MOCK_PHOTO_STUDIO_PLAN_TYPE)).thenReturn(true);

            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 NOT_FOUND (404)", org.springframework.http.HttpStatus.NOT_FOUND.value(), result.getStatusCode());
        }
    }

    /**
     * 测试 handleExistingDesign 方法：发生会话冲突。
     * 预期：重定向到会话冲突处理器返回的URI。
     */
    @Test
    public void testHandleExistingDesign_SessionConflict() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = Mockito.mock(ProjectDesign.class);
        when(projectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_DECRYPTED)).thenReturn(projectDesign);

        URI conflictUri = new URI("http://example.com/session_conflict");
        try (MockedStatic<ToolTypeContextHolder> toolTypeContextHolderMockedStatic = mockStatic(ToolTypeContextHolder.class)) {
            toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType).thenReturn(ToolType.YUN_DESIGN);
            when(sessionConflictHandler.checkSessionConflict(DESIGN_ID_DECRYPTED, USER_ID, ToolTypeContextHolder.getToolType() == ToolType.FOP_ORDER))
                    .thenReturn(conflictUri);

            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);
            
            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            assertEquals("Location头应为冲突URI", conflictUri.toString(), result.getRedirectUrl());
        }
    }
    

    /**
     * 测试 handleExistingDesign 方法：正常流程，无特殊情况。
     * 预期：返回包含H5Model的OK响应。
     */
    @Test
    public void testHandleExistingDesign_NormalFlow() throws Exception {
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate()))
                .thenReturn(true);
        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(diyDesignInfo.getPlanId()).thenReturn(DESIGN_ID_DECRYPTED + 1);
        when(diyDesignInfo.getName()).thenReturn("Test Design");
        when(diyDesignInfo.generateLevelId()).thenReturn("level1");

        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = Mockito.mock(ProjectDesign.class);
        when(projectDesign.getVrc()).thenReturn("NORMAL_VRC");
        when(projectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_DECRYPTED)).thenReturn(projectDesign);
        
        try (MockedStatic<ToolTypeContextHolder> toolTypeContextHolderMockedStatic = mockStatic(ToolTypeContextHolder.class);
             MockedStatic<CooperateHelper> cooperateHelperMockedStatic = mockStatic(CooperateHelper.class)) {
            toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType).thenReturn(ToolType.YUN_DESIGN);
            when(sessionConflictHandler.checkSessionConflict(DESIGN_ID_DECRYPTED, USER_ID, false )).thenReturn(null);
            cooperateHelperMockedStatic.when(CooperateHelper::isCooperate).thenReturn(false);
    
            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);
    
            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 OK (200)", org.springframework.http.HttpStatus.OK.value(), result.getStatusCode());
            assertNotNull("响应体不应为空", result.getBody());
            assertTrue("响应体应为字符串类型", result.getBody() instanceof String);
            String responseBody = (String) result.getBody();
            assertTrue("响应体应包含 designInfo", responseBody.contains("\"designInfo\":{"));
            assertTrue("响应体应包含 obsDesignId", responseBody.contains("\"obsDesignId\":\"" + OBS_DESIGN_ID + "\""));
            assertTrue("响应体应包含 obsPlanId", responseBody.contains("\"obsPlanId\":\"" + ENCRYPTED_PLAN_ID_RAW + "\""));
        }
    }

    /**
     * 测试 handleNewDesign 方法：App类型为BIM。
     * 预期：重定向到BIM页面。
     */
    @Test
    public void testHandleNewDesign_BimAppType() throws Exception {
        lenient().when(param.getObsDesignId()).thenReturn(null);
        Long bimAppIdNum = 200L;

        try (MockedStatic<AppType> appTypeMockedStatic = mockStatic(AppType.class)) {
            appTypeMockedStatic.when(() -> AppType.isBimApp(bimAppIdNum)).thenReturn(true);

            ToolPageResult result = kujialeDiyPageHandler.handleNewDesign(request, response, param, ACTUAL_APP_ID, bimAppIdNum, ROOT_ACCOUNT_ID, false);
            
            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            assertTrue("Location头应包含BIM路径", result.getRedirectUrl().contains(AbstractDiyPageHandler.BIM_PATH));
        }
    }

    /**
     * 测试 handleNewDesign 方法：用户有BIM权限且参数要求重定向到BIM，且needRedirectBIM返回true。
     * 预期：重定向到BIM页面。
     */
    @Test
    public void testHandleNewDesign_BimAccessAndRedirectBim_AndNeedRedirectTrue() throws Exception {
        lenient().when(param.getObsDesignId()).thenReturn(null);
        lenient().when(param.isRedirectBim()).thenReturn(true);

        when(grayLunchService.getGrayVersion(eq(AbstractDiyPageHandler.API_PAGE_BIM_REDIRECT), any(HttpRequestMetaData.class)))
            .thenReturn(ALLOW);
        lenient().when(saasConfigFacade.bimSwitchConfig(USER_ID)).thenReturn(true);

        ToolPageResult result = kujialeDiyPageHandler.handleNewDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, true);

        assertNotNull("PageServiceResult不应为null", result);
        assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
        assertTrue("Location头应包含BIM路径", result.getRedirectUrl().contains(AbstractDiyPageHandler.BIM_PATH));
    }

    /**
     * 测试 handleNewDesign 方法：正常流程，非BIM场景。
     * 预期：返回包含H5Model的OK响应。
     */
    @Test
    public void testHandleNewDesign_NormalFlow() throws Exception {
        lenient().when(param.getObsDesignId()).thenReturn(null);

        try (MockedStatic<AppType> appTypeMockedStatic = mockStatic(AppType.class);
             MockedStatic<CooperateHelper> cooperateHelperMockedStatic = mockStatic(CooperateHelper.class)) {
            appTypeMockedStatic.when(() -> AppType.isBimApp(APP_ID_NUM)).thenReturn(false);
            cooperateHelperMockedStatic.when(CooperateHelper::isCooperate).thenReturn(false);

            ToolPageResult result = kujialeDiyPageHandler.handleNewDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 OK (200)", org.springframework.http.HttpStatus.OK.value(), result.getStatusCode());
            assertNotNull("响应体不应为空", result.getBody());
            assertTrue("响应体应为字符串类型", result.getBody() instanceof String);
            String responseBody = (String) result.getBody();
            assertTrue("响应体应包含 projectStage", responseBody.contains("\"projectStage\":\"test\""));
            assertTrue("响应体应包含加密的用户ID", responseBody.contains("\"obsUserId\":\""+ENCRYPTED_USER_ID+"\""));
        }
    }
    
    /**
     * 测试 finalizeH5ModelAndRespond 方法（通过调用公共方法间接测试）：AB测试命中升级授权。
     * 预期：重定向到升级URL。
     */
    @Test
    public void testFinalizeH5Model_UpgradeAccessViaExistingDesign() throws Exception {
        when(param.getObsDesignId()).thenReturn(OBS_DESIGN_ID);
        when(param.isForceUpdate()).thenReturn(true);
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = Mockito.mock(ProjectDesign.class);
        lenient().when(projectDesign.getVrc()).thenReturn("SOME_VRC_ALLOWING_UPGRADE");
        when(projectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_DECRYPTED)).thenReturn(projectDesign);

        Map<String, String> abTestConfig = new HashMap<>();
        abTestConfig.put(AbstractDiyPageHandler.UPGRADE_SUPER_FLOORPLAN, "true");
        lenient().when(saaSConfigService.getAbTestResult(anyString(), any(), anyLong(), anyString())).thenReturn(abTestConfig);
        
        try (MockedStatic<CooperateHelper> cooperateHelperMockedStatic = mockStatic(CooperateHelper.class);
             MockedStatic<ToolTypeContextHolder> toolTypeContextHolderMockedStatic = mockStatic(ToolTypeContextHolder.class)) {
             toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType).thenReturn(ToolType.YUN_DESIGN);
            lenient().when(sessionConflictHandler.checkSessionConflict(DESIGN_ID_DECRYPTED, USER_ID, ToolTypeContextHolder.getToolType() == ToolType.FOP_ORDER)).thenReturn(null);
             cooperateHelperMockedStatic.when(CooperateHelper::isCooperate).thenReturn(false);

            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request
                    , response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, true);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            assertTrue("Location头应包含升级路径", result.getRedirectUrl().contains(AbstractDiyPageHandler.UPGRADE_PATH));
        }
    }

    /**
     * 测试 finalizeH5ModelAndRespond 方法（通过调用公共方法间接测试）：回收站DIY授权(AB) - 新建设计。
     * 预期：重定向到BIM页面。
     */
    @Test
    public void testFinalizeH5Model_RecycleDiyAuth_NewDesign() throws Exception {
        lenient().when(param.getObsDesignId()).thenReturn(null);
        Map<String, String> abTestConfig = new HashMap<>();
        lenient().when(saaSConfigService.getAbTestResult(anyString(), any(), anyLong(), anyString())).thenReturn(abTestConfig);
        lenient().when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(USER_ID, abTestConfig)).thenReturn(true);

        try (MockedStatic<AppType> appTypeMockedStatic = mockStatic(AppType.class);
             MockedStatic<CooperateHelper> cooperateHelperMockedStatic = mockStatic(CooperateHelper.class)) {
            appTypeMockedStatic.when(() -> AppType.isBimApp(APP_ID_NUM)).thenReturn(false);
            cooperateHelperMockedStatic.when(CooperateHelper::isCooperate).thenReturn(false);

            ToolPageResult result = kujialeDiyPageHandler.handleNewDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            assertTrue("Location头应包含BIM路径", result.getRedirectUrl().contains(AbstractDiyPageHandler.BIM_PATH));
        }
    }
    
    /**
     * 测试 finalizeH5ModelAndRespond 方法（通过调用公共方法间接测试）：回收站DIY授权(AB) - 现有设计，且开启了页面打开回收站DIY。
     * 预期：重定向到更新URL。
     */
    @Test
    public void testFinalizeH5Model_RecycleDiyAuth_ExistingDesign_EnablePageOpenRecycleDiy() throws Exception {
        when(param.getObsDesignId()).thenReturn(OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(USER_ID, param.getOrderDesignId(), DESIGN_ID_DECRYPTED, param.isCooperate())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = Mockito.mock(DiyDesignInfo.class);
        when(diyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_DECRYPTED);
        when(projectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_DECRYPTED)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = Mockito.mock(ProjectDesign.class);
        when(projectDesign.getVrc()).thenReturn("SOME_VRC");
        when(projectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_DECRYPTED)).thenReturn(projectDesign);

        Map<String, String> abTestConfig = new HashMap<>();
        lenient().when(saaSConfigService.getAbTestResult(anyString(), any(), anyLong(), anyString())).thenReturn(abTestConfig);
        lenient().when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(USER_ID, abTestConfig)).thenReturn(true);
        lenient().when(toadProperties.isEnablePageOpenRecycleDiy()).thenReturn(true);

        try (MockedStatic<CooperateHelper> cooperateHelperMockedStatic = mockStatic(CooperateHelper.class);
             MockedStatic<ToolTypeContextHolder> toolTypeContextHolderMockedStatic = mockStatic(ToolTypeContextHolder.class)) {
            cooperateHelperMockedStatic.when(CooperateHelper::isCooperate).thenReturn(false);
            toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType).thenReturn(ToolType.YUN_DESIGN);
            when(sessionConflictHandler.checkSessionConflict(DESIGN_ID_DECRYPTED, USER_ID, ToolTypeContextHolder.getToolType() == ToolType.FOP_ORDER)).thenReturn(null);

            ToolPageResult result = kujialeDiyPageHandler.handleExistingDesign(request, response, param, ACTUAL_APP_ID, APP_ID_NUM, ROOT_ACCOUNT_ID, false);

            assertNotNull("PageServiceResult不应为null", result);
            assertEquals("响应状态码应为 FOUND (302)", org.springframework.http.HttpStatus.FOUND.value(), result.getStatusCode());
            String location = result.getRedirectUrl();
            assertTrue("Location头应包含升级路径", location.contains(AbstractDiyPageHandler.UPGRADE_PATH));
            assertTrue("Location头应包含加密的设计ID", location.contains(OBS_DESIGN_ID));
        }
    }
}