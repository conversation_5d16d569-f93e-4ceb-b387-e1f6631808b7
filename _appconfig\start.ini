#===========================================================
# Jetty Startup
#
# Starting Jetty from this {jetty.home} is not recommended.
#
# A proper {jetty.base} directory should be configured, instead
# of making changes to this {jetty.home} directory.
#
# See documentation about {jetty.base} at
# http://www.eclipse.org/jetty/documentation/current/startup.html
#
# A demo-base directory has been provided as an example of
# this sort of setup.
#
# $ cd demo-base
# $ java -jar ../start.jar
#
#===========================================================

# To disable the warning message, comment the following line
#--module=home-base-warning

# ---------------------------------------
# Module: ext
--module=ext


# ---------------------------------------
# Module: resources
--module=resources


# ---------------------------------------
# Module: server
--module=server

### ThreadPool configuration
## Minimum number of threads
# jetty.threadPool.minThreads=10

## Maximum number of threads
# jetty.threadPool.maxThreads=200

## Thread idle timeout (in milliseconds)
# jetty.threadPool.idleTimeout=60000

### Common HTTP configuration
## Scheme to use to build URIs for secure redirects
# jetty.httpConfig.secureScheme=https

## Port to use to build URIs for secure redirects
# jetty.httpConfig.securePort=8443

## Response content buffer size (in bytes)
# jetty.httpConfig.outputBufferSize=32768

## Max response content write length that is buffered (in bytes)
# jetty.httpConfig.outputAggregationSize=8192

## Max request headers size (in bytes)
# jetty.httpConfig.requestHeaderSize=8192

## Max response headers size (in bytes)
# jetty.httpConfig.responseHeaderSize=8192

## Whether to send the Server: header
# jetty.httpConfig.sendServerVersion=true

## Whether to send the Date: header
# jetty.httpConfig.sendDateHeader=false

## Max per-connection header cache size (in nodes)
# jetty.httpConfig.headerCacheSize=512

## Whether, for requests with content, delay dispatch until some content has arrived
# jetty.httpConfig.delayDispatchUntilContent=true

## Maximum number of error dispatches to prevent looping
# jetty.httpConfig.maxErrorDispatches=10

## Maximum time to block in total for a blocking IO operation (default -1 is to use idleTimeout on progress)
# jetty.httpConfig.blockingTimeout=-1

### Server configuration
## Whether ctrl+c on the console gracefully stops the Jetty server
# jetty.server.stopAtShutdown=true

## Dump the state of the Jetty server, components, and webapps after startup
# jetty.server.dumpAfterStart=false

## Dump the state of the Jetty server, components, and webapps before shutdown
# jetty.server.dumpBeforeStop=false


# ---------------------------------------
# Module: http
--module=http

### HTTP Connector Configuration

## Connector host/address to bind to
jetty.http.host=0.0.0.0

## Connector port to listen on
jetty.http.port=80

## Connector idle timeout in milliseconds
# jetty.http.idleTimeout=30000

## Connector socket linger time in seconds (-1 to disable)
# jetty.http.soLingerTime=-1

## Number of acceptors (-1 picks default based on number of cores)
# jetty.http.acceptors=-1

## Number of selectors (-1 picks default based on number of cores)
# jetty.http.selectors=-1

## ServerSocketChannel backlog (0 picks platform default)
jetty.http.acceptQueueSize=16384

## Thread priority delta to give to acceptor threads
# jetty.http.acceptorPriorityDelta=0

## HTTP Compliance: RFC7230, RFC2616, LEGACY
# jetty.http.compliance=RFC7230

# ---------------------------------------
# Module: deploy
--module=deploy

# Monitored directory name (relative to $jetty.base)
# jetty.deploy.monitoredDir=webapps

# Monitored directory scan period (seconds)
# jetty.deploy.scanInterval=1

# Whether to extract *.war files
# jetty.deploy.extractWars=true

# ---------------------------------------
# Module: jsp
--module=jsp


# ---------------------------------------
# Module: websocket
--module=websocket


# ---------------------------------------
# Module: jstl
#--module=jstl

# ---------------------------------------
# Module: jvm
--module=jvm

--exec
-agentlib:jdwp=transport=dt_socket,address=9999,server=y,suspend=n

# Memory config
#-Xmx128m
#-Xms128m
#-XX:NewRatio=3
-Djava.security.egd=file:/dev/./urandom
#-Xmn1024m
# G1GC config
-XX:+UseG1GC
-XX:+ExplicitGCInvokesConcurrent
# -XX:ConcGCThreads=2
# -XX:+UnlockExperimentalVMOptions

# CMS GC config
#-XX:+DisableExplicitGC
#-XX:+UseConcMarkSweepGC
#-XX:CMSInitiatingOccupancyFraction=80

# GC log
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCDateStamps
-XX:+PrintGCTimeStamps
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=2
-XX:GCLogFileSize=128M
-Xloggc:/var/lib/jetty/logs/gc.log

# other config
-XX:+PrintCommandLineFlags
-XX:-OmitStackTraceInFastThrow
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/lib/jetty/logs/dump.hprof
-Dfile.encoding=UTF-8
#-Dqunhe.service.version=beta
