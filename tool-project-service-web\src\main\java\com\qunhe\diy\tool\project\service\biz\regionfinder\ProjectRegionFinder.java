/*
 * ProjectRegionFinder.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.regionfinder;

import org.jetbrains.annotations.NotNull;

import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFindResult;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFinderParam;

/** 决定一个方案的region
 * <AUTHOR>
 * @date 2024/3/22
 */
public interface ProjectRegionFinder {

    @NotNull
    RegionFindResult findRegion(RegionFinderParam param);

}
