/*
 * CosDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.fasterxml.jackson.core.type.TypeReference;
import com.qunhe.diy.tool.project.service.biz.config.OssConfig;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationTypeEnum;
import com.qunhe.log.QHLogger;
import com.qunhe.middleware.terra.client.qhossclient.QhOssClient;
import com.qunhe.middleware.terra.common.model.QhObjectMetadata;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CosDb {
    private static final QHLogger LOG = QHLogger.getLogger(CosDb.class);

    private static final QhOssClient BATCH_OPERATION_COS_CLIENT =
            OssConfig.getBatchOperationCosClient();

    public String uploadSuccessDealDesignIds(final List<Long> designIds,
            final String recordId, final BatchOperationTypeEnum typeEnum) {
        try {
            final byte[] result = JsonMapper.writeValueAsBytes(designIds);
            String key = OssConfig.generateCosKey(typeEnum.getDesc(),
                    recordId);
            key += OssConfig.JSON_SUFFIX;
            final QhObjectMetadata metadata = new QhObjectMetadata();
            metadata.setContentType(OssConfig.OCTET_STREAM);
            metadata.setContentLength((long) result.length);
            BATCH_OPERATION_COS_CLIENT.putObjectToLogicBucket(key, result, metadata);
            return OssConfig.QHTBD_CDN + key;
        } catch (Exception e) {
            //上传cos失败
            LOG.message("uploadSuccessDealDesignIds error", e)
                    .withPoJo(designIds)
                    .with("recordId", recordId)
                    .error();
            return null;
        }
    }

    public List<Long> getBatchDealDesignIdsFromCosNoThrow(final String key) {
        try (InputStream inputStream =
                OssConfig.getBatchOperationCosClient().getObjectFromLogicBucket(key)) {
            final byte[] bytes = IOUtils.toByteArray(inputStream);
            if (bytes != null && bytes.length > 0) {
                return JsonMapper.parse(new String(bytes), new TypeReference<List<Long>>(){});
            }
        } catch (final Exception e) {
            //从cos拉取待被处理的方案失败
            LOG.message("getBatchDealDesignIdsFromCosNoThrow - get fail", e)
                    .with("key", key)
                    .error();
        }
        return Collections.emptyList();
    }
}
