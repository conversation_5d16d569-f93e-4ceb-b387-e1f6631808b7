/*
 * TreEnum.java
 * Copyright 2022 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.diy;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
@Getter
public enum TreEnum {
    UPDATE_BIM("000.000.000", "打开4.0方案进行升级，升级后跳转到5.0工具");

    private final String treId;
    private final String desc;

    TreEnum(final String treId, final String desc) {
        this.treId = treId;
        this.desc = desc;
    }


}
