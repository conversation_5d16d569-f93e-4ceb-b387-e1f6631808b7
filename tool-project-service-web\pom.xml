<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ starter-pom.xml
  ~ Copyright 2018 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tool-project-service-web</artifactId>
    <version>0.0.18-SNAPSHOT</version>
    <packaging>war</packaging>
    <parent>
        <groupId>com.qunhe.diy</groupId>
        <artifactId>tool-project-service-parent</artifactId>
        <version>0.0.18-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <dependencies>
        <dependency>
            <groupId>com.qunhe.utils</groupId>
            <artifactId>apiencrypt2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>toad-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module</groupId>
            <artifactId>layoutdesign-common-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module</groupId>
            <artifactId>summer-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>designinfoservice-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>hunter-pilot-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.monitor</groupId>
            <artifactId>qunhe-spring-boot-starter-faros</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.i18n</groupId>
            <artifactId>localeutils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe</groupId>
            <artifactId>diymanage-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>toad-pilot-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.utils</groupId>
            <artifactId>log</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>

        <dependency>
            <artifactId>render-idcutil</artifactId>
            <groupId>com.qunhe.render</groupId>
        </dependency>
        <dependency>
            <artifactId>diyrenderclient</artifactId>
            <groupId>com.qunhe.instdeco.diy</groupId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>qunhe-rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>site-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.saas</groupId>
            <artifactId>saas-design-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.saas</groupId>
            <artifactId>saas-config-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>general-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe</groupId>
            <artifactId>diyservice-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module</groupId>
            <artifactId>tool-version</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco.diy</groupId>
            <artifactId>design-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>libra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.logcomplex</groupId>
            <artifactId>userinfo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.custom</groupId>
            <artifactId>dcs-order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.user-growth</groupId>
            <artifactId>property-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>cicd-pilot-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>soa-pilot-boot-starter</artifactId>
            <version>${soa.version}</version>
        </dependency>

        <dependency>
            <artifactId>diyrender-handler</artifactId>
            <groupId>com.qunhe.instdeco.render</groupId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.project-platform</groupId>
            <artifactId>project-auth-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.saas.sharelib</groupId>
            <artifactId>assembly-share-oplog</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qunhe.monitor</groupId>
            <artifactId>qunhe-spring-boot-starter-has-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module.meter</groupId>
            <artifactId>meter-thread-pool-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>mybatis-tddl-pilot-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.utils</groupId>
            <artifactId>math</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco.diy</groupId>
            <artifactId>drsapi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qunhe.middleplatform</groupId>
            <artifactId>project-management-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>diymanage-data</artifactId>
                    <groupId>com.qunhe.diybe</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>tool-project-service-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco.diy</groupId>
            <artifactId>diyutils</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>toad-client</artifactId>
                    <groupId>com.qunhe.middleware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>math</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ant</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>appconfig</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>vraymodelprocess</artifactId>
                    <groupId>com.qunhe.render</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logbase</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>propertyloader</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xalan</artifactId>
                    <groupId>xalan</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springutil</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cglib</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>qunhe-lang</artifactId>
                    <groupId>com.qunhe.utils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aliyun-sdk-oss</artifactId>
                    <groupId>com.aliyun.oss</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>interceptors</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>plan-common</artifactId>
                    <groupId>com.qunhe.instdeco</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.usergrowth</groupId>
            <artifactId>uic-rpc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.user-growth</groupId>
            <artifactId>floor-plan-cool-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>yuncore-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.project-platform</groupId>
            <artifactId>project-search-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>diymanage-data</artifactId>
                    <groupId>com.qunhe.diybe</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.saas</groupId>
            <artifactId>kjl-commercialization-access-control-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qunhe.mdw</groupId>
            <artifactId>pilot-boot-starter-has</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>renderpicdb</artifactId>

        </dependency>


        <dependency>
            <groupId>com.qunhe.hunter</groupId>
            <artifactId>hunter-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.hunter</groupId>
            <artifactId>hunter-spring-web-servlet-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>1.3.2.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>exacloud-test</artifactId>
            <version>0.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>tool-project-service-client</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.project-platform</groupId>
            <artifactId>project-auth-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>synergy-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>yuncore-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>project-public-service-client</artifactId>
        </dependency>

        <dependency>
            <artifactId>spore2</artifactId>
            <groupId>com.qunhe.middleware</groupId>
        </dependency>

        <dependency>
            <groupId>com.qunhe.mdw</groupId>
            <artifactId>qh-switch-pilot-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.house-property</groupId>
            <artifactId>floorplan-home-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>businessaccount-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.user-growth</groupId>
            <artifactId>uic-passport-user-session-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module</groupId>
            <artifactId>restapi-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.user-growth</groupId>
            <artifactId>design-excellent-client</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>open-api</artifactId>
            <version>1.10.20</version>
        </dependency>

        <dependency>
            <groupId>com.qunhe.diybe</groupId>
            <artifactId>diymanage-data</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-junit4</artifactId>
            <version>2.1.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.23.1</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.qunhe.mdw</groupId>
            <artifactId>security-detect-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>rcs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>plan-common</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>tool-project-service</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.9.v20180320</version>
            </plugin>
            <plugin>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- JaCoCo Maven Plugin for Code Coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
