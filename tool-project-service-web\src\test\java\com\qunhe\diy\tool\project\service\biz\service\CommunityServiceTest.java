/*
 * CommunityServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.FphFloorplanService;
import com.qunhe.diy.tool.project.service.biz.service.facade.HasSwitchFacade;
import com.qunhe.diy.tool.project.service.common.constant.CommonConstant;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.house.property.floorplan.home.common.enums.AreaGradeEnum;
import com.qunhe.house.property.floorplan.home.common.model.AreaItemDTO;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class CommunityServiceTest {
    @Mock
    CommunityDb communityDb;
    @Mock
    SysDictAreaMapper sysDictAreaMapper;
    @Mock
    GeneralProperties generalProperties;
    @Mock
    FphFloorplanService fphFloorplanService;
    @Mock
    HasSwitchFacade hasSwitchFacade;
    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @InjectMocks
    CommunityService communityService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetProjectCommunityByAreaIdAndName() {
        final Community oldCommunity = new Community();
        oldCommunity.setAreaId(175L);
        oldCommunity.setName("oldCommunityName");
        oldCommunity.setCommId(11111L);

        when(communityDb.getOrCreateCommunity(anyLong(), anyString())).thenReturn(oldCommunity);

        final Community result = communityService.getOrCreateProjectCommunityByAreaIdAndName(
                175L, "oldCommunityName");
        Assert.assertEquals("community check", oldCommunity, result);
        Assert.assertEquals("commid should equals 11111", result.getCommId().longValue(),
                11111L);
    }

    @Test
    public void testCreateProjectCommunityByAreaIdAndName() {
        final Community newCommunity = new Community();
        newCommunity.setAreaId(175L);
        newCommunity.setName("communityName");
        when(communityDb.getOrCreateCommunity(anyLong(), anyString())).thenReturn(newCommunity);

        final Community result = communityService.getOrCreateProjectCommunityByAreaIdAndName(
                175L, "communityName");
        Assert.assertEquals("community check", newCommunity, result);
        Assert.assertEquals("commid should equals null", null, result.getCommId());
    }


    @Test
    public void testCalculateLocationParamByAreaGrade_CITY() {
        final Long areaId = 36L;
        final AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(areaId)
                .areaName("北京")
                .areaGrade(AreaGradeEnum.CITY)
                .build();
        final SysDictArea sysDictArea = new SysDictArea();
        sysDictArea.setParentAreaId(1L);
        when(sysDictAreaMapper.getById(areaId)).thenReturn(sysDictArea);
        final LocationParam locationParam = communityService.calculateLocationParamByAreaGrade(
                areaItemDTO);
        Assert.assertEquals("correct cityId should equals 36", locationParam.getAreaId(),
                areaId.intValue());
        Assert.assertEquals("correct provinceId should equals 1", locationParam.getSysParentAreaId(), 1);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_REGION() {
        final Long areaId = 50L;
        final AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(areaId)
                .areaName("大兴区")
                .areaGrade(AreaGradeEnum.REGION)
                .build();
        final SysDictArea sysDictArea1 = new SysDictArea();
        sysDictArea1.setParentAreaId(36L);
        when(sysDictAreaMapper.getById(areaId)).thenReturn(sysDictArea1);

        final SysDictArea sysDictArea2 = new SysDictArea();
        sysDictArea2.setParentAreaId(1L);
        when(sysDictAreaMapper.getById(36L)).thenReturn(sysDictArea2);
        final LocationParam locationParam = communityService.calculateLocationParamByAreaGrade(
                areaItemDTO);
        Assert.assertEquals("correct cityId should equals 36", locationParam.getAreaId(), 36);
        Assert.assertEquals("correct provinceId should equals 1", locationParam.getSysParentAreaId(), 1);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_STATE() {
        final Long areaId = 1L;
        final AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(areaId)
                .areaName("浙江省")
                .areaGrade(AreaGradeEnum.STATE)
                .build();

        Community community = new Community();
        community.setAreaId(200L);
        Community[] communities = new Community[1];
        communities[0] = community;
        when(communityDb.getCommunities("未知小区", null)).thenReturn(communities);

        final SysDictArea sysDictArea1 = new SysDictArea();
        sysDictArea1.setParentAreaId(11L);
        when(sysDictAreaMapper.getById(200L)).thenReturn(sysDictArea1);

        final LocationParam locationParam = communityService.calculateLocationParamByAreaGrade(
                areaItemDTO);
        Assert.assertEquals("correct cityId should equals 200", locationParam.getAreaId(), 200);
        Assert.assertEquals("correct provinceId should equals 11", locationParam.getSysParentAreaId(),
                11);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_STATE_error() {
        final Long areaId = 1L;
        final AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(areaId)
                .areaName("浙江省")
                .areaGrade(AreaGradeEnum.STATE)
                .build();

        Community community = new Community();
        community.setAreaId(200L);
        Community[] communities = new Community[1];
        communities[0] = community;
        when(communityDb.getCommunities("未知小区", null)).thenReturn(communities);

        when(sysDictAreaMapper.getById(200L)).thenReturn(null);

        final LocationParam locationParam = communityService.calculateLocationParamByAreaGrade(
                areaItemDTO);
        Assert.assertEquals("fall back to default city hangzhou", locationParam.getAreaId(), 175);
        Assert.assertEquals("fall back to default province zhejiang",
                locationParam.getSysParentAreaId(), 11L);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_CITY_FAIL_TO_DEFAULT() {
        final Long areaId = 36L;
        final AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(areaId)
                .areaName("北京")
                .areaGrade(AreaGradeEnum.CITY)
                .build();
        when(sysDictAreaMapper.getById(areaId)).thenReturn(null);
        final LocationParam locationParam = communityService.calculateLocationParamByAreaGrade(
                areaItemDTO);
        Assert.assertEquals("fall back to default city hangzhou", locationParam.getAreaId(),
                175);
        Assert.assertEquals("fall back to default province zhejiang",
                locationParam.getSysParentAreaId(), 11);
    }

    @Test
    public void getLocationParam_areaId_from_design() {
        final boolean isCoohom = false;
        final Long designId = 1L;
        final Long userId = 2L;
        final String areaId = "1";
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setCommunityName("杭州");
        projectDesign.setCommLogicAreaId(2);
        projectDesign.setCommLogicProvinceId(3);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(projectDesign);
        LocationParam locationParam = communityService.getLocationParam(isCoohom, designId, userId, areaId);
        Assert.assertEquals("correct cityId should equals projectDesign.getCommLogicAreaId()", (Integer) locationParam.getAreaId(),
                projectDesign.getCommLogicAreaId());
    }

    @Test
    public void getLocationParam_first_create_but_coohom() {
        final boolean isCoohom = true;
        final Long designId = 1L;
        final Long userId = 2L;
        final String areaId = null;
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setCommunityName("杭州");
        projectDesign.setCommLogicAreaId(2);
        projectDesign.setCommLogicProvinceId(3);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(projectDesign);
        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        LocationParam locationParam = communityService.getLocationParam(isCoohom, designId, userId, areaId);
        Assert.assertEquals("default location", locationParam.getAreaId(),
                LocationParam.buildDefaultLocation().getAreaId());
    }



    @Test
    public void testGetLocationParam_noDesign_ipSwitchDisabled() {
        when(generalProperties.getEnableFphIpLocation()).thenReturn(false);
        LocationParam result = communityService.getLocationParam(false, 1L, (byte)6);
        LocationParam defaultLocation = LocationParam.buildDefaultLocation();
        Assert.assertEquals("correct cityId should equals 175", defaultLocation.getAreaId(), result.getAreaId());
    }

    @Test
    public void testGetLocationParam_noDesign_isCoohom() {
        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        LocationParam result = communityService.getLocationParam(true, 1L, (byte)6);
        LocationParam defaultLocation = LocationParam.buildDefaultLocation();
        Assert.assertEquals("correct cityId should equals 175", defaultLocation.getAreaId(), result.getAreaId());
    }

    @Test
    public void testGetLocationParam_noDesign_illegalSourceId() {
        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Arrays.asList(1, 2, 6));
        LocationParam result = communityService.getLocationParam(false, 1L, (byte)99);
        LocationParam defaultLocation = LocationParam.buildDefaultLocation();
        Assert.assertEquals("correct cityId should equals 175", defaultLocation.getAreaId(), result.getAreaId());
    }

    @Test
    public void testGetLocationParam_noDesign_nullSourceId_stillChecksList() {
        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        List<Integer> legalIds = new ArrayList<>();
        legalIds.add(1);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(legalIds);

        LocationParam result = communityService.getLocationParam(false, 1L, (byte)6);
        LocationParam defaultLocation = LocationParam.buildDefaultLocation();
        Assert.assertEquals("correct cityId should equals 175", defaultLocation.getAreaId(), result.getAreaId());
    }

    @Test
    public void testGetLocationParam_noDesign_nullSourceId_stillChecksList_withException() {
        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        List<Integer> legalIds = new ArrayList<>();
        legalIds.add(1);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(legalIds);

        LocationParam result = communityService.getLocationParam(false, 1L, (byte)6);
        LocationParam defaultLocation = LocationParam.buildDefaultLocation();
        Assert.assertEquals("correct cityId should equals 175", defaultLocation.getAreaId(), result.getAreaId());
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_areaIdNull() {
        AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(null).areaGrade(AreaGradeEnum.CITY).build();
        SysDictArea sysDictArea = new SysDictArea();
        sysDictArea.setParentAreaId(11L);
        when(sysDictAreaMapper.getById(175L)).thenReturn(sysDictArea);

        LocationParam result = communityService.calculateLocationParamByAreaGrade(areaItemDTO);
        Assert.assertEquals("correct cityId should equals 175", result.getAreaId(), 175);
        Assert.assertEquals("correct provinceId should equals 11", result.getSysParentAreaId(), 11);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_areaGradeNull() {
        AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(36L).areaGrade(null).build();
        SysDictArea sysDictArea = new SysDictArea();
        sysDictArea.setParentAreaId(1L);
        when(sysDictAreaMapper.getById(36L)).thenReturn(sysDictArea);

        LocationParam result = communityService.calculateLocationParamByAreaGrade(areaItemDTO);
        Assert.assertEquals("correct cityId should equals 36", result.getAreaId(), 36);
        Assert.assertEquals("correct provinceId should equals 1", result.getSysParentAreaId(), 1);
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_NATION() {
        AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(0L).areaGrade(AreaGradeEnum.NATION).build();
        LocationParam result = communityService.calculateLocationParamByAreaGrade(areaItemDTO);
        LocationParam defaultParam = LocationParam.buildDefaultLocation();
        Assert.assertEquals("Should return default areaId for NATION", defaultParam.getAreaId(), result.getAreaId());
        Assert.assertEquals("Should return default sysParentAreaId for NATION", defaultParam.getSysParentAreaId(), result.getSysParentAreaId());
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_STATE_noCommunities() {
        AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(1L).areaGrade(AreaGradeEnum.STATE).build();
        when(communityDb.getCommunities(CommonConstant.DEFAULT_COMMUNITY_NAME, null)).thenReturn(new Community[0]);

        LocationParam result = communityService.calculateLocationParamByAreaGrade(areaItemDTO);
        LocationParam defaultParam = LocationParam.buildDefaultLocation();
        Assert.assertEquals("Should return default areaId for STATE with no communities", defaultParam.getAreaId(), result.getAreaId());
        Assert.assertEquals("Should return default sysParentAreaId for STATE with no communities", defaultParam.getSysParentAreaId(), result.getSysParentAreaId());
    }

    @Test
    public void testCalculateLocationParamByAreaGrade_REGION_cityParentError() {
        Long regionAreaId = 50L;
        AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(regionAreaId).areaGrade(AreaGradeEnum.REGION).build();
        when(sysDictAreaMapper.getById(regionAreaId)).thenReturn(null);

        LocationParam result = communityService.calculateLocationParamByAreaGrade(areaItemDTO);
        Assert.assertEquals("Should return default areaId for REGION with city parent error", 175, result.getAreaId());
        Assert.assertEquals("Should return default sysParentAreaId for REGION with city parent error", 11, result.getSysParentAreaId());
    }



    @Test
    public void testGetParentAreaId_mapperReturnsNull() {
        Long areaId = 1L;
        when(sysDictAreaMapper.getById(areaId)).thenReturn(null);
        int parentAreaId = communityService.getParentAreaId(areaId);
        Assert.assertEquals("Should return -1 for areaId with no parent", -1, parentAreaId);
    }

    @Test
    public void testGetParentAreaId_parentIdIsNull() {
        Long areaId = 1L;
        SysDictArea sysDictArea = new SysDictArea();
        sysDictArea.setAreaId(areaId);
        sysDictArea.setParentAreaId(null);
        when(sysDictAreaMapper.getById(areaId)).thenReturn(sysDictArea);
        int parentAreaId = communityService.getParentAreaId(areaId);
        Assert.assertEquals("Should return -1 for areaId with null parent", -1, parentAreaId);
    }

    @Test
    public void testGetParentAreaId_parentIdIsNotNull() {
        Long areaId = 1L;
        Long parentId = 2L;
        SysDictArea sysDictArea = new SysDictArea();
        sysDictArea.setAreaId(areaId);
        sysDictArea.setParentAreaId(parentId);
        when(sysDictAreaMapper.getById(areaId)).thenReturn(sysDictArea);
        int parentAreaId = communityService.getParentAreaId(areaId);
        Assert.assertEquals("Should return correct parentAreaId", parentId.intValue(), parentAreaId);
    }

    @Test
    public void testUpdateCommidIfPreciseIpSupport_commIdExists() {
        ToolProjectSaveParam param = new ToolProjectSaveParam();
        param.setCommId(123L);
        param.setUserId(1L);
        param.setSourceId((byte)1);

        communityService.updateCommidIfPreciseIpSupport(false, param);
        verify(fphFloorplanService, never()).getIpLocation(anyString());
        Assert.assertEquals("commId should not change", Long.valueOf(123L), param.getCommId());
    }


    @Test
    public void testUpdateCommidIfPreciseIpSupport_communityNotFound() {
        try (MockedStatic<SpanContextHolder> mockedSpanContextHolder = mockStatic(SpanContextHolder.class)) {

            ToolProjectSaveParam param = new ToolProjectSaveParam();
            param.setCommId(null);
            param.setUserId(1L);
            param.setSourceId((byte)6);

            when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
            when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Collections.singletonList(6));
            when(hasSwitchFacade.isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, 1L)).thenReturn(true);
            AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(175L).areaGrade(AreaGradeEnum.CITY).build();
            when(fphFloorplanService.getIpLocation(anyString())).thenReturn(areaItemDTO);
            SysDictArea sysDictArea = new SysDictArea();
            sysDictArea.setParentAreaId(11L);
            when(sysDictAreaMapper.getById(175L)).thenReturn(sysDictArea);
            when(communityDb.getOrCreateCommunity(eq(175L), eq("未知小区"))).thenReturn(null);

            communityService.updateCommidIfPreciseIpSupport(false, param);
            Assert.assertNull("commId should remain null as community not found", param.getCommId());
        }
    }

    @Test
    public void testUpdateCommidIfPreciseIpSupport_success() {
        try (MockedStatic<SpanContextHolder> mockedSpanContextHolder = mockStatic(SpanContextHolder.class)) {

            ToolProjectSaveParam param = new ToolProjectSaveParam();
            param.setCommId(null);
            param.setUserId(1L);
            param.setSourceId((byte)6);

            when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
            when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Collections.singletonList(6));
            when(hasSwitchFacade.isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, 1L)).thenReturn(true);
            AreaItemDTO areaItemDTO = AreaItemDTO.builder().areaId(175L).areaGrade(AreaGradeEnum.CITY).build();
            when(fphFloorplanService.getIpLocation(anyString())).thenReturn(areaItemDTO);
            SysDictArea sysDictArea = new SysDictArea();
            sysDictArea.setParentAreaId(11L);
            when(sysDictAreaMapper.getById(175L)).thenReturn(sysDictArea);
            Community community = new Community();
            community.setCommId(999L);
            when(communityDb.getOrCreateCommunity(eq(175L), eq("未知小区"))).thenReturn(community);

            communityService.updateCommidIfPreciseIpSupport(false, param);
            Assert.assertEquals("commId should be updated", Long.valueOf(999L), param.getCommId());
        }
    }

    @Test
    public void testUpdateCommidIfPreciseIpSupportFallback() {
        ToolProjectSaveParam param = new ToolProjectSaveParam();
        param.setCommId(123L);
        param.setUserId(456L);
        Throwable throwable = new RuntimeException("Test exception for fallback");

        communityService.updateCommidIfPreciseIpSupportFallback(false, param, throwable);

        Assert.assertEquals("CommId should remain unchanged after fallback", Long.valueOf(123L), param.getCommId());
    }
}