/*
 * CommonUtil.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.util;

import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diybe.dms.data.PlanType;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.instdeco.plan.util.StringUtil;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class SecurityUtil {
    private static final int DESIGN_NAME_LENGTH_LIMIT = 100;
    private static final String[] ILLEGAL_SYMBOL = {"&quot;", "&lt;", "&gt;"};
    private static final String[] ESCAPE_CHARACTER = {"\b","\\t", "\\n", "\\r", "\\f"};

    public static void defendXxsAttack(final ToolProjectModifyParam modifyParam) {
        if (modifyParam == null) {
            return;
        }
        modifyParam.setName(StringEscapeUtils.escapeHtml4(StringEscapeUtils.unescapeHtml4(modifyParam.getName())));
    }

    /**
     * 处理方案名称的特殊字符问题
     * https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80599470733
     * @param modifyParam
     * @return
     */
     public static ProjectDesign filterModifyParam(final ProjectDesign design,
             final ToolProjectModifyParam modifyParam) {
         String designName = modifyParam.getName();
         if(StringUtils.isBlank(designName) || StringUtil.containsEmoji(designName)){
            throw new IllegalArgumentException("designName is illegal.");
         }
         designName = removeInvalidCharacters(designName);
         if(StringUtils.equals("" , designName) || designName.length() > DESIGN_NAME_LENGTH_LIMIT){
             throw new IllegalArgumentException("designName's length is illegal.");
         }
         design.setDesignName(designName);
         design.setCommunityName(modifyParam.getCommName());
         design.setCommunityAreaId(Integer.valueOf(modifyParam.getAreaId()));
         design.setCommLogicAreaId(Integer.valueOf(modifyParam.getAreaId()));
         if (modifyParam.getDesignType() != PlanType.DesignType.UNKNOWN) {
             byte planType = new PlanType.Builder()
                     .setProjectType(PlanType.getProjectType(design.getPlanType()))
                     .setDesignType(PlanType.getDesignType(modifyParam.getDesignType().value()))
                     .build();
             design.setPlanType(planType);
         }
         return design;
     }

    /**
     * 方案名称去除非法字符
     * @param value
     */
     private static String removeInvalidCharacters(String value){
         if(!StringUtils.isBlank(value)){
             //去除特殊转义符
             Pattern pattern;
             Matcher matcher;
             for(String s : ESCAPE_CHARACTER) {
                 pattern = Pattern.compile(s);
                 matcher = pattern.matcher(value);
                 value = matcher.replaceAll("");
             }

             //去除非法字符
             for (String is : ILLEGAL_SYMBOL) {
                 value = value.replaceAll(is,"");
             }

         }
         return value;
     }

}
