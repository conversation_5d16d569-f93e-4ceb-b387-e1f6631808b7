/*
 * WarmUpDesign.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.config;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "warmup")
@Setter
@Getter
public class WarmUpConfig {

    private int iterationCount = 5;

    private int timeoutMills = 30000;

    private Long userId = 1111111437L;

    private Long designId = 38516096L;

}
