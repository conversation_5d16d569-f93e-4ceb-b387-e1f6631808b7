/*
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.assembly.oplog.utils.JsonUtils;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@Service
public class ErrorTipService {

    public static final String ERROR_TIP_ID_PREFIX = "errortip_id_";

    @Autowired
    @Qualifier("commonCacheRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ToadProperties toadProperties;

    public String save(ErrorTipData errorTipData) {
        String key = ERROR_TIP_ID_PREFIX + UUID.randomUUID();
        redisTemplate.opsForValue().set(key, JsonUtils.toString(errorTipData),
                toadProperties.getSessionErrorTipExpireMinutes(), TimeUnit.MINUTES);
        return key;
    }

    public ErrorTipData get(String key) {
        String data = redisTemplate.opsForValue().get(key);
        if (data != null) {
            return JsonUtils.parseString(data, ErrorTipData.class);
        }
        return ErrorTipData.DEFAULT;
    }

}
