/*
 * IpParam.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.qunhe.diy.tool.project.service.common.constant.CommonConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LocationParam {
    private int areaId;

    private int sysParentAreaId;

    private String name;

    /**
     * 默认：浙江杭州，未知小区
     * @return
     */
    public static LocationParam buildDefaultLocation() {
        return LocationParam.builder()
                .areaId(CommonConstant.DEFAULT_AREA_ID)
                .sysParentAreaId(CommonConstant.DEFAULT_PROVINCE_ID)
                .name(CommonConstant.DEFAULT_COMMUNITY_NAME)
                .build();
    }
}
