IP=$PODIP
APP_STAGE=$APP_STAGE
 
PORT=6789
DEFAULT_SHUT_DOWN_PATH="/_management_/shutDown"
SHUT_DOWN_PATH=$SHUT_DOWN_PATH
VIP=$VIP
 
if [ "${SHUT_DOWN_PATH}"x = ""x ]; then
    SHUT_DOWN_PATH=${DEFAULT_SHUT_DOWN_PATH}
fi
 
 
COOPS_DEV_IP="soa-gov-dev.qunhequnhe.com"
COOPS_PROD_TEST_IP="soa-gov-prod-test.qunhequnhe.com"
COOPS_PROD_IP="soa-gov.qunhequnhe.com"
 
COOPS_IP=${COOPS_DEV_IP}
if [[ ${APP_STAGE} == "dev" ]]; then
    COOPS_IP=${COOPS_DEV_IP}
elif [[ ${APP_STAGE} == "prod_test" ]]; then
    COOPS_IP=${COOPS_PROD_TEST_IP}
elif [[ ${APP_STAGE} == "prod" ]]; then
    COOPS_IP=${COOPS_PROD_IP}
else
    echo "找不到对应stage：${APP_STAGE}"
    exit 1
fi
 
addr="${IP}:${PORT}"
coops_url="http://${COOPS_IP}/api/soagov/prestop?vip=${VIP}&&addr=${addr}"
 
coops_header="Authorization:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.ozWn91VE5-0M2HwtcoeswyoeHkh_lw0_e5E7LCXz9JY"
echo "${coops_url}"
 
# 调用服务的下线接口
self_shut_down_url="http://localhost:${PORT}${SHUT_DOWN_PATH}"
selfRes=$(curl -s --max-time 10 -X POST --header 'Accept: application/json' --header ${coops_header} ${self_shut_down_url})
echo "${self_shut_down_url}"
echo "${selfRes}"
 
# 检查soa中是否已经被注销
result=$(curl -s --max-time 10 -X GET --header 'Accept: application/json' --header ${coops_header} ${coops_url})
for (( i = 0; i < 3; i++ )); do
    if [ ${result}x = '"ALREADY_DOWN"'x ]; then
        echo "${result}"
        break
    else
        result=$(curl -s --max-time 10 -X GET --header 'Accept: application/json' --header ${coops_header} ${coops_url})
        echo "${result}"
        sleep 2
    fi
done
 
sleep 5