/*
 * WebConfig.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.config;

import com.qunhe.diy.tool.project.service.web.interceptor.LoggerInterceptor;
import com.qunhe.diy.tool.project.service.web.interceptor.ToolHeaderInterceptor;
import com.qunhe.instdeco.diy.apiencrypt.EncryptCompressMessageConverter;
import com.qunhe.instdeco.diy.apiencrypt.EncryptMessageConverter;
import com.qunhe.instdeco.diy.apiencrypt.GzipMessageConverter;
import com.qunhe.instdeco.diy.apiencrypt.ResponseEncryptControllerAdvice;
import com.qunhe.interceptors.SessionUserInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@Import(ResponseEncryptControllerAdvice.class)
public class WebConfig extends WebMvcConfigurerAdapter {

    @Override
    public void configureMessageConverters(final List<HttpMessageConverter<?>> converters) {
        converters.add(new EncryptMessageConverter());
        converters.add(new EncryptCompressMessageConverter());
        converters.add(new GzipMessageConverter());
        final StringHttpMessageConverter stringHttpMessageConverter =
                new StringHttpMessageConverter(StandardCharsets.UTF_8);
        stringHttpMessageConverter.setWriteAcceptCharset(false);
        stringHttpMessageConverter.setSupportedMediaTypes(
                Collections.singletonList(MediaType.TEXT_PLAIN));
        converters.add(stringHttpMessageConverter);
        final MappingJackson2HttpMessageConverter jackson2HttpMessageConverter =
                new MappingJackson2HttpMessageConverter();
        jackson2HttpMessageConverter.setSupportedMediaTypes(
                Collections.singletonList(MediaType.APPLICATION_JSON));
        converters.add(jackson2HttpMessageConverter);
        super.configureMessageConverters(converters);
    }

    @Override
    public void addInterceptors(final InterceptorRegistry registry) {
        registry.addInterceptor(new SessionUserInterceptor());
        registry.addInterceptor(new LoggerInterceptor());
        registry.addInterceptor(new ToolHeaderInterceptor());
    }
}
