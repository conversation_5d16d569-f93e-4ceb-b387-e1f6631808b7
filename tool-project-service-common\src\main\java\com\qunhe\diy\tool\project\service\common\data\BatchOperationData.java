/*
 * BatchOperationVO.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 按照userid批量彻底删除/恢复
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchOperationData {
    /**
     * 主键
     */
    private Long id;
    private String recordId;
    private Long userId;
    private String operator;

    /**
     * 0：批量彻底删除； 1:回滚批量删除
     */
    private int rollback;
    private int status;
    private int originCount;
    private int completeCount;
    private String description;
    private String link;
}
