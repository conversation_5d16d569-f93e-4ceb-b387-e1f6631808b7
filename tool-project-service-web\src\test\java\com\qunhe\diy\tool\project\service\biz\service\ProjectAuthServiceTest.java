/*
 * ProjectAuthServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */


package com.qunhe.diy.tool.project.service.biz.service;

import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.ProjectAuthOperationException;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.instdeco.plan.site.constants.SiteConstants;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.data.TransferErrorCode;
import com.qunhe.project.platform.project.auth.data.UserAuthTransferResult;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.project.platform.project.auth.param.AuthCheckParam;
import com.qunhe.project.platform.project.auth.param.UserAuthBatchTransferParam;
import com.qunhe.project.platform.project.auth.param.UserAuthTransferParam;
import com.qunhe.project.platform.project.auth.param.UserAuthUpdateParam;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ProjectAuthServiceTest {
    @Mock
    private ProjectAuthClient projectAuthClient;

    @Mock
    private LongCrypt longCrypt; // 用户要求mock，即使LongCipher.DEFAULT不需要mock，此为实例

    @Mock
    private ToadProperties toadProperties;

    @InjectMocks
    private ProjectAuthService projectAuthService;

    private static final Long TEST_USER_ID = 123L;
    private static final Long TEST_DESIGN_ID = 456L;
    private static final Long TEST_PLAN_ID = 789L;
    private static final String OBS_DESIGN_ID = "obsDesignId";
    private static final String OBS_PLAN_ID = "obsPlanId";
    private static final Long DESIGN_ADMIN_ID = 138238L;


    @Before
    public void setup() {
        // 默认的 client checkAuth 行为，可以在具体测试中覆盖
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(Result.ok(true));
    }

    /**
     * 测试校验用户权限 - 普通用户，有权限
     */
    @Test
    public void testCheckAuth_normalUser_hasAuth() {
        // 中文注释：构建权限检查参数
        final AuthCheckParam authCheckParam = AuthCheckParam.builder()
                .userId(TEST_USER_ID)
                .designId(TEST_DESIGN_ID)
                .authCheckType(AuthCheckType.WRITE)
                .build();
        // 中文注释：模拟projectAuthClient返回true
        when(projectAuthClient.checkAuth(authCheckParam)).thenReturn(Result.ok(true));

        // 中文注释：断言用户有权限
        assertTrue("用户应具有写入权限",
                projectAuthService.checkAuth(TEST_USER_ID, TEST_DESIGN_ID));
    }

    /**
     * 测试校验用户权限 - Kujiale虚拟样板间用户
     */
    @Test
    public void testCheckAuth_specialUser_kujialeVirtualSampleRoom() {
        // 中文注释：断言Kujiale虚拟样板间用户始终有权限
        assertTrue("Kujiale虚拟样板间用户应有权限", projectAuthService.checkAuth(
                SiteConstants.SpecialUserIds.KUJIALE_VIRTUAL_SAMPLE_ROOM, TEST_DESIGN_ID));
        // 中文注释：确认projectAuthClient未被调用
        verify(projectAuthClient, never()).checkAuth(any(AuthCheckParam.class));
    }

    /**
     * 测试校验用户权限 - 设计管理员用户
     */
    @Test
    public void testCheckAuth_specialUser_designAdmin() {
        // 中文注释：断言设计管理员用户始终有权限
        assertTrue("设计管理员用户应有权限",
                projectAuthService.checkAuth(DESIGN_ADMIN_ID, TEST_DESIGN_ID));
        // 中文注释：确认projectAuthClient未被调用
        verify(projectAuthClient, never()).checkAuth(any(AuthCheckParam.class));
    }

    /**
     * 测试校验用户权限 - 普通用户，无权限
     */
    @Test
    public void testCheckAuth_normalUser_noAuth() {
        Long userId = 123L;
        Long designId = 456L;
        final AuthCheckParam authCheckParam = AuthCheckParam.builder()
                .userId(userId)
                .designId(designId)
                .authCheckType(AuthCheckType.WRITE)
                .build();
        // 中文注释：模拟projectAuthClient返回false
        when(projectAuthClient.checkAuth(authCheckParam)).thenReturn(Result.ok(false));

        // 中文注释：断言用户无权限
        assertFalse("用户应无写入权限", projectAuthService.checkAuth(userId, designId));
    }

    /**
     * 测试 checkAuth (带熔断逻辑) - 客户端调用成功且有权限
     */
    @Test
    public void testCheckAuthWithFallback_clientSuccess_hasAuth() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(TEST_USER_ID);
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(Result.ok(true));

        // 中文注释：断言有权限
        Boolean result = projectAuthService.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false,
                null, yunDesign, AuthCheckType.READ);
        assertTrue("应返回true当客户端成功且有权限", result);
    }

    /**
     * 测试 checkAuth (带熔断逻辑) - 客户端调用成功但无权限
     */
    @Test
    public void testCheckAuthWithFallback_clientSuccess_noAuth() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(TEST_USER_ID);
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(Result.ok(false));

        // 中文注释：断言无权限
        Boolean result = projectAuthService.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false,
                null, yunDesign, AuthCheckType.READ);
        assertFalse("应返回false当客户端成功但无权限", result);
    }

    /**
     * 测试 checkAuth (带熔断逻辑) - 客户端调用失败，回退到YunDesign校验，用户匹配
     */
    @Test
    public void testCheckAuthWithFallback_clientFail_fallback_userMatch() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(TEST_USER_ID);
        // 中文注释：模拟客户端调用失败
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(
                Result.error("fail"));

        // 中文注释：断言回退逻辑返回true因为用户匹配
        Boolean result = projectAuthService.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false,
                null, yunDesign, AuthCheckType.READ);
        assertTrue("应返回true当客户端失败且回退用户匹配", result);
    }

    /**
     * 测试 checkAuth (带熔断逻辑) - 客户端调用失败，回退到YunDesign校验，用户不匹配
     */
    @Test
    public void testCheckAuthWithFallback_clientFail_fallback_userMismatch() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(999L); // 不同的用户ID
        // 中文注释：模拟客户端调用失败
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(
                Result.error("fail"));

        // 中文注释：断言回退逻辑返回false因为用户不匹配
        Boolean result = projectAuthService.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false,
                null, yunDesign, AuthCheckType.READ);
        assertFalse("应返回false当客户端失败且回退用户不匹配", result);
    }

    /**
     * 测试 checkAuth (带熔断逻辑) - 客户端调用失败，回退到YunDesign校验，YunDesign为null
     */
    @Test
    public void testCheckAuthWithFallback_clientFail_fallback_yunDesignNull() {
        // 中文注释：模拟客户端调用失败
        when(projectAuthClient.checkAuth(any(AuthCheckParam.class))).thenReturn(
                Result.error("fail"));

        // 中文注释：断言回退逻辑返回false因为YunDesign为null
        Boolean result = projectAuthService.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false,
                null, null, AuthCheckType.READ);
        assertFalse("应返回false当客户端失败且回退YunDesign为null", result);
    }

    /**
     * 测试 checkAuthHandler (熔断处理方法) - 用户匹配
     */
    @Test
    public void testCheckAuthHandler_userMatch() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(TEST_USER_ID);
        // 中文注释：断言有权限因为用户匹配
        Boolean result = projectAuthService.checkAuthHandler(TEST_DESIGN_ID, TEST_USER_ID, false,
                false, null, yunDesign, AuthCheckType.READ, new RuntimeException());
        assertTrue("Handler应返回true当用户匹配", result);
    }

    /**
     * 测试 checkAuthHandler (熔断处理方法) - 用户不匹配
     */
    @Test
    public void testCheckAuthHandler_userMismatch() {
        YunDesign yunDesign = new YunDesign();
        yunDesign.setUserId(999L);
        // 中文注释：断言无权限因为用户不匹配
        Boolean result = projectAuthService.checkAuthHandler(TEST_DESIGN_ID, TEST_USER_ID, false,
                false, null, yunDesign, AuthCheckType.READ, new RuntimeException());
        assertFalse("Handler应返回false当用户不匹配", result);
    }

    /**
     * 测试 checkAuthHandler (熔断处理方法) - YunDesign为null
     */
    @Test
    public void testCheckAuthHandler_yunDesignNull() {
        // 中文注释：断言无权限因为YunDesign为null
        Boolean result = projectAuthService.checkAuthHandler(TEST_DESIGN_ID, TEST_USER_ID, false,
                false, null, null, AuthCheckType.READ, new RuntimeException());
        assertFalse("Handler应返回false当YunDesign为null", result);
    }


    /**
     * 测试保存用户权限 - 客户端返回null结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testSaveUserAuth_nullResult_throwsException() {
        // 中文注释：模拟客户端返回null
        when(projectAuthClient.updateUserAuth(any(UserAuthUpdateParam.class))).thenReturn(null);
        // 中文注释：执行保存操作，期望抛出ProjectAuthOperationException
        projectAuthService.saveUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试保存用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testSaveUserAuth_failureResult_throwsException() {
        // 中文注释：模拟客户端返回失败的Result对象
        when(projectAuthClient.updateUserAuth(any(UserAuthUpdateParam.class))).thenReturn(
                Result.error("client error"));
        // 中文注释：执行保存操作，期望抛出ProjectAuthOperationException
        projectAuthService.saveUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }


    /**
     * 测试批量回收用户权限 - designIds为空列表
     */
    @Test
    public void testBatchRecycleUserAuth_emptyDesignIds() {
        // 中文注释：执行批量回收，传入空列表
        List<UserAuthTransferResult> results = projectAuthService.batchRecycleUserAuth(TEST_USER_ID,
                Collections.emptyList());
        // 中文注释：断言返回空列表
        assertTrue("当designIds为空时应返回空列表", results.isEmpty());
        // 中文注释：验证客户端未被调用
        verify(projectAuthClient, never()).batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class));
    }

    /**
     * 测试批量回收用户权限 - 客户端返回null，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecycleUserAuth_clientReturnsNull_throwsException() {
        // 中文注释：模拟客户端返回null
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(null);
        // 中文注释：执行批量回收，期望抛出异常
        projectAuthService.batchRecycleUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量回收用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecycleUserAuth_clientReturnsFailure_throwsException() {
        // 中文注释：模拟客户端返回失败
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(Result.error("client error"));
        // 中文注释：执行批量回收，期望抛出异常
        projectAuthService.batchRecycleUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量回收用户权限 - 客户端返回空的结果列表，抛出异常 (根据原测试行为调整)
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecycleUserAuth_clientReturnsEmptyList_throwsException() {
        // 中文注释：模拟客户端返回成功但结果列表为空
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Collections.emptyList()));
        // 中文注释：执行批量回收，期望抛出异常
        projectAuthService.batchRecycleUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量回收用户权限 - 成功
     */
    @Test
    public void testBatchRecycleUserAuth_success() {
        List<Long> designIds = Lists.newArrayList(TEST_DESIGN_ID, 777L);
        List<UserAuthTransferResult> expectedResults = designIds.stream()
                .map(id -> new UserAuthTransferResult(
                        UserAuthTransferParam.builder()
                                .designId(id)
                                .srcUserId(TEST_USER_ID)
                                .targetUserId(1L)
                                .build(),
                        TransferErrorCode.NOT_APPLIED))
                .collect(Collectors.toList());
        // 中文注释：模拟客户端成功返回结果列表
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(expectedResults));

        List<UserAuthTransferResult> actualResults = projectAuthService.batchRecycleUserAuth(
                TEST_USER_ID, designIds);
        // 中文注释：断言返回结果与预期一致
        assertEquals("返回的回收结果列表应与预期一致", expectedResults, actualResults);
    }


    /**
     * 测试回收单个用户权限 - 客户端返回null，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecycleUserAuth_clientReturnsNull_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(null);
        projectAuthService.recycleUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试回收单个用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecycleUserAuth_clientReturnsFailure_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(Result.error("client error"));
        projectAuthService.recycleUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试回收单个用户权限 - 客户端返回结果中含EXCEPTION错误码，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecycleUserAuth_errorCodeException_throwsException() {
        UserAuthTransferResult errorResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(TEST_USER_ID)
                        .targetUserId(1L)
                        .build(),
                TransferErrorCode.EXCEPTION);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(errorResult)));
        projectAuthService.recycleUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试回收单个用户权限 - 成功
     */
    @Test
    public void testRecycleUserAuth_success() {
        UserAuthTransferResult successResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(TEST_USER_ID)
                        .targetUserId(1L)
                        .build(),
                TransferErrorCode.NOT_APPLIED);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(successResult)));
        projectAuthService.recycleUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
        // 中文注释：验证客户端被正确调用，参数符合预期
        UserAuthBatchTransferParam expectedParam = new UserAuthBatchTransferParam();
        expectedParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(TEST_DESIGN_ID)
                .srcUserId(TEST_USER_ID)
                .targetUserId(1L) // 回收给ID为1的用户
                .build()));
        verify(projectAuthClient).batchTransferUserAuth(expectedParam);
    }

    /**
     * 测试批量恢复用户权限 - designIds为空列表
     */
    @Test
    public void testBatchRecoverUserAuth_emptyDesignIds() {
        List<UserAuthTransferResult> results = projectAuthService.batchRecoverUserAuth(TEST_USER_ID,
                Collections.emptyList());
        // 中文注释：断言返回空列表
        assertTrue("当designIds为空时应返回空列表", results.isEmpty());
        // 中文注释：验证客户端未被调用
        verify(projectAuthClient, never()).batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class));
    }

    /**
     * 测试批量恢复用户权限 - 客户端返回null，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecoverUserAuth_clientReturnsNull_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(null);
        projectAuthService.batchRecoverUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量恢复用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecoverUserAuth_clientReturnsFailure_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(Result.error("client error"));
        projectAuthService.batchRecoverUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量恢复用户权限 - 客户端返回空的结果列表，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testBatchRecoverUserAuth_clientReturnsEmptyList_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Collections.emptyList()));
        projectAuthService.batchRecoverUserAuth(TEST_USER_ID, Lists.newArrayList(TEST_DESIGN_ID));
    }

    /**
     * 测试批量恢复用户权限 - 成功
     */
    @Test
    public void testBatchRecoverUserAuth_success() {
        List<Long> designIds = Lists.newArrayList(TEST_DESIGN_ID, 777L);
        List<UserAuthTransferResult> expectedResults = designIds.stream()
                .map(id -> new UserAuthTransferResult(
                        UserAuthTransferParam.builder()
                                .designId(id)
                                .srcUserId(1L)
                                .targetUserId(TEST_USER_ID)
                                .build(),
                        TransferErrorCode.NOT_APPLIED))
                .collect(Collectors.toList());
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(expectedResults));

        List<UserAuthTransferResult> actualResults = projectAuthService.batchRecoverUserAuth(
                TEST_USER_ID, designIds);
        // 中文注释：断言返回结果与预期一致
        assertEquals("返回的恢复结果列表应与预期一致", expectedResults, actualResults);
    }

    /**
     * 测试恢复单个用户权限 - 客户端返回null，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecoverUserAuth_clientReturnsNull_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(null);
        projectAuthService.recoverUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试恢复单个用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecoverUserAuth_clientReturnsFailure_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(Result.error("client error"));
        projectAuthService.recoverUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试恢复单个用户权限 - 客户端返回结果中含EXCEPTION错误码，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testRecoverUserAuth_errorCodeException_throwsException() {
        UserAuthTransferResult errorResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(1L)
                        .targetUserId(TEST_USER_ID)
                        .build(),
                TransferErrorCode.EXCEPTION);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(errorResult)));
        projectAuthService.recoverUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
    }

    /**
     * 测试恢复单个用户权限 - 成功
     */
    @Test
    public void testRecoverUserAuth_success() {
        UserAuthTransferResult successResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(1L)
                        .targetUserId(TEST_USER_ID)
                        .build(),
                TransferErrorCode.NOT_APPLIED);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(successResult)));
        projectAuthService.recoverUserAuth(TEST_USER_ID, TEST_DESIGN_ID);
        // 中文注释：验证客户端被正确调用
        UserAuthBatchTransferParam expectedParam = new UserAuthBatchTransferParam();
        expectedParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(TEST_DESIGN_ID)
                .srcUserId(1L) // 从ID为1的用户处恢复
                .targetUserId(TEST_USER_ID)
                .build()));
        verify(projectAuthClient).batchTransferUserAuth(expectedParam);
    }

    /**
     * 测试转移用户权限 - 客户端返回null，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testTransferUserAuth_clientReturnsNull_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(null);
        projectAuthService.transferUserAuth(TEST_USER_ID, 789L, TEST_DESIGN_ID);
    }

    /**
     * 测试转移用户权限 - 客户端返回失败结果，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testTransferUserAuth_clientReturnsFailure_throwsException() {
        when(projectAuthClient.batchTransferUserAuth(
                any(UserAuthBatchTransferParam.class))).thenReturn(Result.error("client error"));
        projectAuthService.transferUserAuth(TEST_USER_ID, 789L, TEST_DESIGN_ID);
    }

    /**
     * 测试转移用户权限 - 客户端返回结果中含EXCEPTION错误码，抛出异常
     */
    @Test(expected = ProjectAuthOperationException.class)
    public void testTransferUserAuth_errorCodeException_throwsException() {
        UserAuthTransferResult errorResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(TEST_USER_ID)
                        .targetUserId(789L)
                        .build(),
                TransferErrorCode.EXCEPTION);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(errorResult)));
        projectAuthService.transferUserAuth(TEST_USER_ID, 789L, TEST_DESIGN_ID);
    }

    /**
     * 测试转移用户权限 - 成功
     */
    @Test
    public void testTransferUserAuth_success() {
        Long targetUserId = 789L;
        UserAuthTransferResult successResult = new UserAuthTransferResult(
                UserAuthTransferParam.builder()
                        .designId(TEST_DESIGN_ID)
                        .srcUserId(TEST_USER_ID)
                        .targetUserId(targetUserId)
                        .build(),
                TransferErrorCode.NOT_APPLIED);
        when(projectAuthClient.batchTransferUserAuth(any(UserAuthBatchTransferParam.class)))
                .thenReturn(Result.ok(Lists.newArrayList(successResult)));
        projectAuthService.transferUserAuth(TEST_USER_ID, targetUserId, TEST_DESIGN_ID);
        // 中文注释：验证客户端被正确调用
        UserAuthBatchTransferParam expectedParam = new UserAuthBatchTransferParam();
        expectedParam.setList(Lists.newArrayList(UserAuthTransferParam.builder()
                .designId(TEST_DESIGN_ID)
                .srcUserId(TEST_USER_ID)
                .targetUserId(targetUserId)
                .build()));
        verify(projectAuthClient).batchTransferUserAuth(expectedParam);
    }

    /**
     * 测试校验删除权限 - UserDb.getUserIdBySession()返回null
     */
    @Test
    public void testHasDeletedAccess_sessionUserNull() {
        try (MockedStatic<UserDb> mockedUserDb = Mockito.mockStatic(UserDb.class)) {
            // 中文注释：模拟session中获取用户ID返回null
            mockedUserDb.when(UserDb::getUserIdBySession).thenReturn(null);
            // 中文注释：断言无删除权限
            assertFalse("当session用户为空时，应无删除权限",
                    projectAuthService.hasDeletedAccess(null, null, null, TEST_DESIGN_ID));
        }
    }

    /**
     * 测试校验删除权限 - 所有ID参数均为null
     */
    @Test
    public void testHasDeletedAccess_allIdsNull() {
        try (MockedStatic<UserDb> mockedUserDb = Mockito.mockStatic(UserDb.class)) {
            mockedUserDb.when(UserDb::getUserIdBySession).thenReturn(TEST_USER_ID);
            // 中文注释：断言当所有ID参数都为null时，无删除权限
            assertFalse("当所有ID参数都为null时，应无删除权限",
                    projectAuthService.hasDeletedAccess(null, null, null, null));
        }
    }



    /**
     * 测试校验删除权限 - 使用明文planId，有权限
     */
    @Test
    public void testHasDeletedAccess_withPlanId_hasAccess() {
        try (MockedStatic<UserDb> mockedUserDb = Mockito.mockStatic(UserDb.class);
                ) {

            mockedUserDb.when(UserDb::getUserIdBySession).thenReturn(TEST_USER_ID);

            AuthCheckParam expectedParam = AuthCheckParam.builder()
                    .userId(TEST_USER_ID)
                    .planId(TEST_PLAN_ID)
                    .authCheckType(AuthCheckType.DELETE)
                    .build();
            // 中文注释：模拟客户端校验权限返回true
            when(projectAuthClient.checkAuth(expectedParam)).thenReturn(Result.ok(true));

            // 中文注释：断言有删除权限
            assertTrue("使用明文planId校验，应有删除权限",
                    projectAuthService.hasDeletedAccess(null, null, TEST_PLAN_ID, null));
            // 中文注释：确认longCrypt未被调用
            verify(longCrypt, never()).decrypt(anyString());
        }
    }


    /**
     * 测试删除权限校验的Fallback方法
     */
    @Test
    public void testHasDeletedAccessFallback() {
        // 中文注释：断言Fallback方法返回true
        assertTrue("Fallback方法应返回true",
                projectAuthService.hasDeletedAccessFallback(OBS_PLAN_ID, OBS_DESIGN_ID,
                        TEST_PLAN_ID, TEST_DESIGN_ID, new RuntimeException("test exception")));
    }
}
