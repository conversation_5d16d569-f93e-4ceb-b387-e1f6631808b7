/*
 * ToolProjectRestApiService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.CoverPicFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.OpenApiUserFacade;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesign;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignLevel;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignStatus;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiErrorCode;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiLevelBatchUpdateRequest;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.diybe.dms.project.data.ProjectCols;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.module.restapi.common.error.Code;
import com.qunhe.diybe.module.restapi.common.error.ErrorDetails;
import com.qunhe.diybe.module.restapi.common.exception.RestApiProcessException;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.data.BatchDesignUserAuthQueryParam;
import com.qunhe.project.platform.project.auth.data.ProjectDesignUserAuth;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Component
@RequiredArgsConstructor
public class ToolProjectRestApiService {

    private static final QHLogger LOG = QHLogger.getLogger(ToolProjectRestApiService.class);

    public static final double REAL_REAL_TO_SOURCE_AREA = 1.25;

    private final ProjectClient projectClient;

    private final ProjectAuthClient projectAuthClient;

    private final CoverPicFacade coverPicFacade;

    private final OpenApiUserFacade openApiUserFacade;

    private final HomeDesignFacadeDb homeDesignFacadeDb;

    private final LevelDesignFacadeDb levelDesignFacadeDb;


    private static final List<String> COLS = ImmutableList.of(
            ProjectCols.DESIGN_ID,
            ProjectCols.PLAN_ID,
            ProjectCols.USER_ID,
            ProjectCols.CREATED,
            ProjectCols.MODIFIED_TIME,
            ProjectCols.NAME,
            ProjectCols.DESC,
            ProjectCols.COMMUNITY_NAME,
            ProjectCols.COMM_ID,
            ProjectCols.DESIGN_DATA_ID,
            ProjectCols.DELETED,
            ProjectCols.AUTHOR_ID,
            ProjectCols.DESIGN_ATTR_PRIVATE_STATUS,
            ProjectCols.SYS_DICT_AREA_FULL_NAME,
            ProjectCols.PICS,
            ProjectCols.VRC
    );

    public List<RestApiDesign> getProjectDesigns(final List<Long> designIds,
            final Long operatorUserId) throws DiyManageServiceException, RestApiProcessException {


        doAuthCheck(designIds, operatorUserId);

        Map<Long, ProjectDesign> projectDesigns =
                projectClient.getProjectDesignsByDesignIds(designIds, COLS).stream()
                        .collect(Collectors.toMap(a -> a.getDesignId(), a -> a, (a, b) -> a));
        if (MapUtils.isEmpty(projectDesigns)) {
            return Collections.emptyList();
        }
        Map<Long, String> coverPicsByPlanIds = coverPicFacade.getCoverPicsByPlanIds(
                projectDesigns.values().stream()
                        .map(a -> a.getPlanId()).collect(Collectors.toList()));

        Map<Long, String> appUidByUserIds = openApiUserFacade.getAppUidByUserIds(
                projectDesigns.values().stream()
                        .flatMap(a -> Lists.newArrayList(a.getUserId(), a.getAuthorId()).stream())
                        .filter(userId -> userId != null && userId != 1)
                        .distinct().collect(Collectors.toList()));

        Map<Long, HomeDesignData> designToHomeDesignData =
                homeDesignFacadeDb.batchGetHomeDesignData(
                        projectDesigns.values().stream().map(d -> d.getDesignId()).collect(
                                Collectors.toList()));

        List<String> levelIds = designToHomeDesignData.values().stream().flatMap(
                d -> d.getLevelInfos().stream()).map(l -> l.getLevelId()).collect(
                Collectors.toList());

        Map<String, LevelDesignData> levelDesignDataMap =
                levelDesignFacadeDb.batchGetLevelDesigns(levelIds);

        List<RestApiDesign> restApiDesigns = Lists.newArrayListWithCapacity(projectDesigns.size());
        for (ProjectDesign design : projectDesigns.values()) {
            HomeDesignData homeDesignData = designToHomeDesignData.get(design.getDesignId());
            if (homeDesignData == null) {
                continue;
            }
            List<RestApiDesignLevel> levels = homeDesignData.getLevelInfos().stream().map(l -> {
                LevelDesignData levelDesignData = levelDesignDataMap.get(l.getLevelId());
                if (levelDesignData == null) {
                    LOG.message("level design data not exist, not match with levelInfo")
                            .with("designId", design.getDesignId())
                            .with("levelId", l.getLevelId())
                            .warn();
                    return null;
                }
                return getRestApiDesignLevel(design, l, levelDesignData);
            }).filter(Objects::nonNull).collect(Collectors.toList());


            RestApiDesign restApiDesign = RestApiDesign.builder()
                    .designId(LongCipher.DEFAULT.encrypt(design.getDesignId()))
                    .planId(LongCipher.DEFAULT.encrypt(design.getPlanId()))
                    .ownerAppUid(appUidByUserIds.get(design.getUserId()))
                    .name(design.getDesignName())
                    .coverPic(coverPicsByPlanIds.get(design.getPlanId()))
                    .designDesc(design.getDesignDesc())
                    .communityName(design.getCommunityName())
                    .distinctFullName(design.getSysdictareaFullname())
                    .status(getDesignStatus(design))
                    .createdTime(Optional.ofNullable(design.getCreated()).map(
                            Timestamp::getTime).orElse(null))
                    .modifiedTime(Optional.ofNullable(design.getModifiedTime()).map(
                            Timestamp::getTime).orElse(null))
                    .levels(levels)
                    .build();
            restApiDesigns.add(restApiDesign);
        }
        return restApiDesigns;
    }

    private void doAuthCheck(final List<Long> designIds, final Long operatorUserId)
            throws RestApiProcessException {
        BatchDesignUserAuthQueryParam batchDesignUserAuthQueryParam =
                new BatchDesignUserAuthQueryParam();
        batchDesignUserAuthQueryParam.setDesignIds(designIds);
        batchDesignUserAuthQueryParam.setUserIds(
                Collections.singletonList(operatorUserId));
        Result<List<ProjectDesignUserAuth>> authResult =
                projectAuthClient.batchQueryUserAuthForDesigns(batchDesignUserAuthQueryParam);
        if (!authResult.success() || CollectionUtils.isEmpty(authResult.getD())) {
            LOG.message("authResult is empty")
                    .with("designIds", designIds)
                    .warn();
            throw new RestApiProcessException(Code.UNAUTHENTICATED,
                    ErrorDetails.build().withReason(RestApiErrorCode.DESIGN_ACCESS_FORBIDDEN.name())
                            .withMessage("do not have auth for design")
                            .withMetaData("designIds",
                                    designIds.stream().map(LongCipher.DEFAULT::
                                            encrypt).collect(Collectors.toList()).toString())
            );
        }
        List<Long> forbiddenDesignIds = authResult.getD().stream()
                .filter(projectDesignUserAuth -> !AuthCheckType.READ.check(
                        projectDesignUserAuth.getAuth()))
                .map(ProjectDesignUserAuth::getDesignId)
                .collect(Collectors.toList());
        List<Long> authIds = authResult.getD().stream().map(ProjectDesignUserAuth::getDesignId)
                .collect(Collectors.toList());
        forbiddenDesignIds.addAll(designIds.stream().filter(designId -> !authIds.contains(designId))
                .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(forbiddenDesignIds)) {
            LOG.message("part of designs are forbidden to access")
                    .with("forbiddenDesignIds", forbiddenDesignIds)
                    .warn();
            throw new RestApiProcessException(Code.UNAUTHENTICATED,
                    ErrorDetails.build().withReason(RestApiErrorCode.DESIGN_ACCESS_FORBIDDEN.name())
                            .withMessage("do not have auth for part of designs")
                            .withMetaData("unauthorizedDesignIds",
                                    forbiddenDesignIds.stream().map(LongCipher.DEFAULT::
                                            encrypt).collect(Collectors.toList()).toString())
            );
        }
    }

    private RestApiDesignStatus getDesignStatus(final ProjectDesign design) {
        if (design.getDeleted() == 1) {
            return RestApiDesignStatus.DELETED;
        }
        if (design.getUserId() == 1) {
            return RestApiDesignStatus.RECYCLED;
        }
        return RestApiDesignStatus.NORMAL;
    }

    private RestApiDesignLevel getRestApiDesignLevel(final ProjectDesign design,
            final LevelInfo levelInfo,
            final LevelDesignData levelDesignData) {
        return RestApiDesignLevel.builder()
                .designId(LongCipher.DEFAULT.encrypt(levelDesignData.getDesignId()))
                .levelId(levelDesignData.getLevelId())
                .index(levelInfo.getIndex())
                .name(levelInfo.getName())
                .realArea(getRealArea(levelDesignData))
                .ibArea(levelDesignData.getIbArea())
                .sourceArea(getSourceArea(design, levelDesignData))
                .outdoorArea(levelDesignData.getOutdoorArea())
                .planPic(StringUtils.defaultString(levelDesignData.getPlanImgs() == null ? null
                        : levelDesignData.getPlanImgs().getStag()))
                .created(levelDesignData.getLastModified().getTime())
                .lastModifiedTime(levelDesignData.getLastModified().getTime())
                .build();

    }

    private double getRealArea(@NotNull final LevelDesignData levelDesignData) {
        if (levelDesignData.getRealArea() != null) {
            return levelDesignData.getRealArea();
        }
        // 没有就用老的 area 兼容
        if (levelDesignData.getArea() != null) {
            return levelDesignData.getArea();
        }
        // 再没有不管了
        return 0;
    }

    private double getSourceArea(ProjectDesign design, final LevelDesignData levelDesignData) {
        val realArea = getRealArea(levelDesignData);
        val sourceArea = levelDesignData.getSourceArea();
        if (isBim(design.getVrc()) && sourceArea != null && sourceArea > realArea) {
            return sourceArea;
        }
        // 4.0 和 5.0 老数据用 realArea * 1.25 计算
        return realArea * REAL_REAL_TO_SOURCE_AREA;
    }

    private boolean isBim(String vrc) {
        return vrc.startsWith("V0150");
    }

    public List<LevelInfo> batchUpdateLevels(final Long designId,
            final RestApiLevelBatchUpdateRequest request)
            throws DiyManageServiceException, RestApiProcessException {

        LevelBatchUpdateResponse levelBatchUpdateResponse =
                homeDesignFacadeDb.batchCreateOrUpdateLevels(designId, request.getBatchRequests());
        if (levelBatchUpdateResponse.getStatus() !=
                LevelBatchUpdateResponse.UpdateStatus.SUCCEED.getStatus()) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT,
                    ErrorDetails.build()
                            .withReason(LevelBatchUpdateResponse.UpdateStatus.fromStatus(
                                    levelBatchUpdateResponse.getStatus()).name())
                            .withMessage("update level failed"));
        }
        return levelBatchUpdateResponse.getHomeDesignData().getLevelInfos();
    }
}
