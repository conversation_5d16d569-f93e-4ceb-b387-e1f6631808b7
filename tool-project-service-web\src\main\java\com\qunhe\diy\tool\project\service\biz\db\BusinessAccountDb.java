/*
 * BusinessAccountDb.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.service.facade.BusinessAccountFacade;
import com.qunhe.instdeco.businessaccount.sdk.data.TobBusinessAccount;
import com.qunhe.instdeco.businessaccount.sdk.fields.BusinessAccountField;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class BusinessAccountDb {

    private static final QHLogger LOG = QHLogger.getLogger(BusinessAccountDb.class);

    public static final List<BusinessAccountField> DEFAULT_FIELDS = Arrays.asList(
            BusinessAccountField.ACCOUNT_ID,
            BusinessAccountField.USER_ID,
            BusinessAccountField.LEVEL,
            BusinessAccountField.PARENT_ACCOUNT_ID,
            BusinessAccountField.NAME,
            BusinessAccountField.EXCLUDE,
            BusinessAccountField.ROOT_ACCOUNT_ID
    );

    /**
     * <a href="http://confluence.qunhequnhe.com/pages/viewpage.action?pageId=***********">businessaccount-sdk 接口文档</a>
     */
    private final BusinessAccountFacade businessAccountFacade;


    public Long getRootAccountForBAndC(final Long userId) {
        final Long res = getRootAccountIdByUserId(userId);
        return res == null ? businessAccountFacade.getRootAccountIdByUserIdForC(userId) : res;
    }

    public Long getRootAccountIdByUserId(final Long userId) {
        final SessionUser user = UserDb.getSessionUserBySession();
        if (user != null && user.getRootAccountId() != null
                && Objects.equals(userId, user.getUserId())) {
            return user.getRootAccountId();
        }
        return businessAccountFacade.getRootAccountIdByUserId(userId);
    }

    public TobBusinessAccount getBusinessAccountById(final Long accountId) {
        return businessAccountFacade.getBusinessAccountById(accountId);
    }

    public Long getAccountId(final Long userId) {
        final SessionUser user = UserDb.getSessionUserBySession();
        if (user != null && user.getAccountId() != null
                && Objects.equals(userId, user.getUserId())) {
            return user.getAccountId();
        }
        return businessAccountFacade.getAccountId(userId);
    }

}
