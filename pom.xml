<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ starter-pom.xml
  ~ Copyright 2018 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.qunhe.diy</groupId>
    <artifactId>tool-project-service-parent</artifactId>
    <version>0.0.18-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tool-project-service</name>
    <description>demo</description>
    <parent>
        <groupId>com.qunhe.middleware</groupId>
        <artifactId>pilot-boot-dependencies</artifactId>
        <version>2.3.2-RELEASE</version>
        <relativePath />
    </parent>
    <properties>
        <log4j.version>2.16.0</log4j.version>
        <java.version>1.8</java.version>
        <soa.version>1.5.1</soa.version>
        <soa-starter.version>1.5.1</soa-starter.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <diyrender.version>1.8.0</diyrender.version>
        <tddl5.version>5.3.5</tddl5.version>
        <mybatis-tddl-starter.version>2.3.4-RELEASE</mybatis-tddl-starter.version>
        <tddl-starter.version>2.3.4-RELEASE</tddl-starter.version>
        <meter.version>1.2.0</meter.version>
        <dms.version>2.3.32</dms.version>
        <hunter.version>4.0.13</hunter.version>
        <hunter-componet.version>2.8.1</hunter-componet.version>
        <restapi-core.version>0.0.11</restapi-core.version>
        <sonar.exclusions>
            **/constant/**,**/data/**,**/enums/**,**/param/**,**/util/**,**/config/**,**/db/**,**/api/**,**/factory/**
        </sonar.exclusions>
    </properties>
    <modules>
        <module>tool-project-service-common</module>
        <module>tool-project-service-client</module>
        <module>tool-project-service-web</module>
    </modules>
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-core</artifactId>
                <version>${hunter.version}</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>3.2.4</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>3.2.4</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>5.1</version>
            </dependency>

            <dependency>
                <groupId>com.qunhe.render</groupId>
                <artifactId>renderengine</artifactId>
                <version>1.18.82</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>tool-project-service-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>tool-project-service-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qunhe.diybe.module</groupId>
                <artifactId>restapi-core</artifactId>
                <version>${restapi-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe.module</groupId>
                <artifactId>restapi-core-common</artifactId>
                <version>${restapi-core.version}</version>
            </dependency>

            <!--这个包仅仅是因为黑名单引入fix-->
            <dependency>
                <groupId>com.qunhe.instdeco.diy</groupId>
                <artifactId>drsapi</artifactId>
                <version>1.8.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commoditycenterapi</artifactId>
                        <groupId>com.qunhe.instdeco</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.diybe.utils</groupId>
                        <artifactId>brep</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>render-idcutil</artifactId>
                        <groupId>com.qunhe.render</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>render-idcutil</artifactId>
                        <groupId>com.qunhe.render</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>diyrenderclient</artifactId>
                        <groupId>com.qunhe.instdeco.diy</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <artifactId>diyrenderclient</artifactId>
                <groupId>com.qunhe.instdeco.diy</groupId>
                <version>1.8.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spore2</artifactId>
                        <groupId>com.qunhe.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>render-idcutil</artifactId>
                        <groupId>com.qunhe.render</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>tool-project-service-web</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- ================================方案中台依赖==================================-->
            <dependency>
                <groupId>com.qunhe.project-platform</groupId>
                <artifactId>project-search-client</artifactId>
                <version>0.2.24</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.middleplatform</groupId>
                <artifactId>project-management-client</artifactId>
                <version>0.1.4</version>
            </dependency>

            <!-- ================================工具依赖==================================-->
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>toad-client</artifactId>
                <version>1.1.17.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco.diy</groupId>
                <artifactId>diyutils</artifactId>
                <version>1.4.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>xerces</groupId>
                        <artifactId>xercesImpl</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hunter-mongojack</artifactId>
                        <groupId>com.qunhe.hunter</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>interceptors</artifactId>
                <version>0.0.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.qunhe.render</groupId>
                        <artifactId>render-idcutil</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.webshot</groupId>
                        <artifactId>meshshared</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.usergrowth</groupId>
                <artifactId>uic-rpc</artifactId>
                <version>3.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe</groupId>
                <artifactId>diymanage-client</artifactId>
                <version>${dms.version}</version>

                <exclusions>
                    <exclusion>
                        <artifactId>aliyun-openservices</artifactId>
                        <groupId>com.aliyun.openservices</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>appconfig</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>im4java</artifactId>
                        <groupId>org.im4java</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-transport-native-unix-common</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>springutil</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-image</artifactId>
                        <groupId>com.twelvemonkeys.common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-io</artifactId>
                        <groupId>com.twelvemonkeys.common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>common-lang</artifactId>
                        <groupId>com.twelvemonkeys.common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>imageio-core</artifactId>
                        <groupId>com.twelvemonkeys.imageio</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>imageio-metadata</artifactId>
                        <groupId>com.twelvemonkeys.imageio</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-transport-native-epoll</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>qunhe-lang</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cloudutil</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe</groupId>
                <artifactId>diymanage-data</artifactId>
                <version>${dms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.utils</groupId>
                <artifactId>math</artifactId>
                <version>1.2.20</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.9.9</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.utils</groupId>
                <artifactId>webstandard</artifactId>
                <version>0.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>integration</artifactId>
                <version>${soa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>toad-pilot-boot-starter</artifactId>
                <version>2.3.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.i18n</groupId>
                <artifactId>localeutils</artifactId>
                <version>1.0.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>toad-client</artifactId>
                        <groupId>com.qunhe.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-native-epoll</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>mybatis-tddl-pilot-boot-starter</artifactId>
                <version>2.3.4-RELEASE</version>
            </dependency>

            <!-- ================================渲染依赖==================================-->
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>renderpicdb</artifactId>
                <version>3.0.27</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>projectdb</artifactId>
                        <groupId>com.qunhe</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>plan-common</artifactId>
                        <groupId>com.qunhe.instdeco</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>drsapidata</artifactId>
                        <groupId>com.qunhe.instdeco.diy</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>springutil</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-math3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections4</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>thumbnailator</artifactId>
                        <groupId>net.coobird</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>qunhe-lang</artifactId>
                        <groupId>com.qunhe.utils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>site-data</artifactId>
                        <groupId>com.qunhe.instdeco</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>drsapi</artifactId>
                        <groupId>com.qunhe.instdeco.diy</groupId>
                    </exclusion>

                    <exclusion>
                        <groupId>com.qunhe.webshot</groupId>
                        <artifactId>meshshared</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.render</groupId>
                        <artifactId>render-idcutil</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.instdeco</groupId>
                        <artifactId>mongoshared</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.hunter</groupId>
                        <artifactId>hunter-httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.qunhe.instdeco.plan</groupId>
                        <artifactId>graylaunchmw</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- ================================业务依赖==================================-->
            <dependency>
                <groupId>com.qunhe.user-growth</groupId>
                <artifactId>floor-plan-cool-client</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>yuncore-api</artifactId>
                <version>0.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>yuncore-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.saas</groupId>
                <artifactId>kjl-commercialization-access-control-client</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.mdw</groupId>
                <artifactId>pilot-boot-starter-has</artifactId>
                <version>2.3.0</version>
            </dependency>

            <!-- ================================三方依赖==================================-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>23.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.5.RELEASE</version>
            </dependency>
            <!-- ================================插件依赖==================================-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.10</version>
                <scope>provided</scope>
            </dependency>
            <!-- ================================mq==================================-->
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>qunhe-rocketmq-client</artifactId>
                <version>1.1.0.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>netty-all</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-spring-web-servlet-filter</artifactId>
                <version>${hunter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-mongojack</artifactId>
                <version>${hunter-componet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-mysql-connector-java</artifactId>
                <version>${hunter-componet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-redis</artifactId>
                <version>${hunter-componet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.hunter</groupId>
                <artifactId>hunter-thrift</artifactId>
                <version>${hunter-componet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.webshot</groupId>
                <artifactId>meshshared</artifactId>
                <version>1.0.31</version>
            </dependency>
            <dependency>
                <artifactId>diyrender-handler</artifactId>
                <groupId>com.qunhe.instdeco.render</groupId>
                <exclusions>
                    <exclusion>
                        <artifactId>spore2</artifactId>
                        <groupId>com.qunhe.middleware</groupId>
                    </exclusion>
                </exclusions>
                <version>${diyrender.version}</version>
            </dependency>
            <!--方案用户权限服务-->
            <dependency>
                <groupId>com.qunhe.project-platform</groupId>
                <artifactId>project-auth-client</artifactId>
                <version>1.0.18</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.project-platform</groupId>
                <artifactId>project-auth-security</artifactId>
                <version>1.0.18</version>
            </dependency>
            <!--协同 -->
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>synergy-service-client</artifactId>
                <version>0.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>designinfoservice-client</artifactId>
                <version>0.0.34</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>site-data</artifactId>
                <version>1.6.7</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.saas</groupId>
                <artifactId>saas-config-client</artifactId>
                <version>0.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>general-api</artifactId>
                <version>0.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>libra-api</artifactId>
                <version>2.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe.module</groupId>
                <artifactId>tool-version</artifactId>
                <version>1.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.logcomplex</groupId>
                <artifactId>userinfo-client</artifactId>
                <version>2.2.6</version>
            </dependency>
            <!--备份热点数据-->
            <dependency>
                <groupId>com.qunhe.diy</groupId>
                <artifactId>project-public-service-client</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe</groupId>
                <artifactId>diyservice-client</artifactId>
                <version>1.1.6</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>hunter-pilot-boot-starter</artifactId>
                <version>${hunter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>commoditycenterapi</artifactId>
                <version>2.1.8</version>
            </dependency>
            <!--内容检测-->
            <dependency>
                <groupId>com.qunhe.mdw</groupId>
                <artifactId>security-detect-service-client</artifactId>
                <version>0.0.1-RELEASE</version>
            </dependency>
            <!--黑包-->
            <dependency>
                <artifactId>spore2</artifactId>
                <groupId>com.qunhe.middleware</groupId>
                <version>0.0.9</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.28.Final</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.render</groupId>
                <artifactId>render-idcutil</artifactId>
                <version>4.13.3</version>
                <exclusions>
                    <exclusion>
                            <groupId>io.projectreactor.netty</groupId>
                            <artifactId>reactor-netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--oplog审计-->
            <dependency>
                <groupId>com.qunhe.saas.sharelib</groupId>
                <artifactId>assembly-share-oplog</artifactId>
                <version>0.1.6.6.3</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.user-growth</groupId>
                <artifactId>uic-passport-user-session-starter</artifactId>
                <version>3.3.0</version>
            </dependency>

            <!--分桶-->
            <dependency>
                <groupId>com.qunhe.mdw</groupId>
                <artifactId>qh-switch-pilot-boot-starter</artifactId>
                <version>1.2.1</version>
            </dependency>
            <!--省市解析-->
            <dependency>
                <groupId>com.qunhe.house-property</groupId>
                <artifactId>floorplan-home-client</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>businessaccount-sdk</artifactId>
                <version>1.0.10</version>
            </dependency>
            <!--秒级监控-->
            <dependency>
                <groupId>com.qunhe.monitor</groupId>
                <artifactId>qunhe-spring-boot-starter-has-metrics</artifactId>
                <version>1.2.2</version>
            </dependency>

            <!--线程池监控埋点-->
            <dependency>
                <groupId>com.qunhe.diybe.module.meter</groupId>
                <artifactId>meter-thread-pool-spring-boot-starter</artifactId>
                <version>${meter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.9</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.9</version>
                <scope>test</scope>
            </dependency>
            <!--接入 terra-->
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>terra-client</artifactId>
                <version>0.9.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>rcs-api</artifactId>
                <version>0.3.40</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.middleware</groupId>
                <artifactId>common-pilot-boot-starter</artifactId>
                <version>2.3.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco</groupId>
                <artifactId>plan-common</artifactId>
                <version>1.5.36</version>
                <exclusions>
                    <exclusion>
                        <artifactId>instdecolights</artifactId>
                        <groupId>com.qunhe.instdeco</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe.module</groupId>
                <artifactId>layoutdesign-common-client</artifactId>
                <version>0.0.41</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.diybe.module</groupId>
                <artifactId>summer-cache</artifactId>
                <version>1.0.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-expression</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>log4j-over-slf4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.saas</groupId>
                <artifactId>saas-design-client</artifactId>
                <version>0.33.0</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.custom</groupId>
                <artifactId>dcs-order-client</artifactId>
                <version>1.2.3-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.qunhe.instdeco</groupId>
                        <artifactId>new-parameter-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunhe.instdeco.diy</groupId>
                <artifactId>design-api</artifactId>
                <version>1.2.34</version>
            </dependency>
            <dependency>
                <groupId>com.qunhe.user-growth</groupId>
                <artifactId>property-center-client</artifactId>
                <version>0.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.qunhe.utils</groupId>
                <artifactId>apiencrypt2</artifactId>
                <version>2.0.1</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <scm>
        <connection>
            scm:git:*************************:kam/kam-backend-developer-group/tool-project-service.git
        </connection>
        <developerConnection>
            scm:git:*************************:kam/kam-backend-developer-group/tool-project-service.git
        </developerConnection>
        <url>https://gitlab.qunhequnhe.com/kam/kam-backend-developer-group/tool-project-service</url>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <repository>
            <id>qunhe-releases</id>
            <name>Qunhe Release Repository</name>
            <url>https://nexus.qunhequnhe.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>qunhe-snapshots</id>
            <name>Qunhe Snapshot Repository</name>
            <url>https://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
