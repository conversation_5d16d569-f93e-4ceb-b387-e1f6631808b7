/*
 * WarmUpController.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.web.config.WarmUpConfig;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectService;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/9/14
 */
@RestController
@RequiredArgsConstructor
@EnableConfigurationProperties(WarmUpConfig.class)
public class WarmUpController {

    private static final QHLogger LOG = QHLogger.getLogger(WarmUpController.class);

    private final ToolProjectService toolProjectService;

    private final WarmUpConfig warmUpConfig;

    @GetMapping("/post-start")
    public void warmUp() {

        LOG.message("grace preStartTask - start up")
                .info();

        StopWatch stopWatch = new StopWatch("grace preStartTask");

        for (int i = 0; i < warmUpConfig.getIterationCount(); i++) {
            stopWatch.start("grace preStartTask - iteration " + i);
            try {
                execute();
            } catch (Exception e) {
                // 忽略异常，继续预热
                LOG.message("grace preStartTask - execute failed", e)
                        .error();
            }
            stopWatch.stop();
            if (stopWatch.getTotalTimeMillis() > warmUpConfig.getTimeoutMills()) {
                LOG.message("grace preStartTask - timeout")
                        .with("time", stopWatch.prettyPrint())
                        .error();
                break;
            }
        }

        LOG.message("grace preStartTask - all connect started successfully")
                .with("time", stopWatch.prettyPrint())
                .info();

    }

    @SuppressWarnings("PMD.SignatureDeclareThrowsException")
    private void execute() throws Exception {

        // 更新方案信息, 打通SOA链接
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId(LongCipher.DEFAULT.encrypt(warmUpConfig.getDesignId()));
        modifyParam.setName("预测-自动生成的方案-change");
        toolProjectService.modifyProjectInfo(warmUpConfig.getUserId(), modifyParam);



    }


}
