/*
 * BatchOperationRecordMapper.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb;

import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface BatchOperationRecordMapper {
    /**
     * 插入批量彻底删除/批量恢复彻底删除请求记录
     * @param batchOperationData
     * @return
     */
    int insertBatchOperationRecord(BatchOperationData batchOperationData);

    /**
     * 更新完成数和请求状态(进行中), 如果es同步超时，可能会出现重复执行complete_count > origin_count
     * @param additionalCount
     * @param status
     * @param recordId
     * @return
     */
    int updateCompleteCountAndStatus(@Param("additionalCount") int additionalCount, @Param(
            "status") int status, @Param("recordId") String recordId);

    /**
     * 更新请求状态
     * @param status
     * @param link
     * @param recordId
     * @return
     */
    int updateBatchOperateFinishInfos(@Param("status") int status, @Param("link") String link,
            @Param("completeCount") int completeCount, @Param("recordId") String recordId);

    int updateOperationRecord(BatchOperationData batchOperationData);

    /**
     * 查询某次请求记录
     * @param recordId
     * @return
     */
    BatchOperationData getBatchOperationRecordByRecordId(String recordId);

    List<String> getMultiOperateLinksByUserId(@Param(value = "userId") long userId, @Param(value
            = "rollback") int rollback);
}
