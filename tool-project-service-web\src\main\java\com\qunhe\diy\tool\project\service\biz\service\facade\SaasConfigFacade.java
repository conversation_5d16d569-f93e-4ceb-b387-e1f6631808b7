/*
 * SaasConfigFacade.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.rpc.proxy.MethodResult;
import com.qunhe.saas.config.client.SaasConfigReadApi;
import com.qunhe.saas.config.client.data.ConfigItemContent;
import com.qunhe.utils.HunterLogger;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SaasConfigFacade {
    private static final HunterLogger LOG = HunterLogger.getLogger(SaasConfigFacade.class);

    private final static String ITEM_CODE = "designBimSwitch";

    private final SaasConfigReadApi saasConfigReadApi;


    public ConfigItemContent getUserConfig(Long userId, String itemCode) {
        MethodResult<Result<List<ConfigItemContent>>> result =
                saasConfigReadApi.getConfigValuesByCodes(userId, Lists.newArrayList(itemCode));

        if (!result.ok() || result.getReturnData() == null || !result.getReturnData().success() ||
                result.getReturnData().getD() == null) {
            LOG.message("getUserConfig fetch failed")
                    .with("userId", userId)
                    .warn();
            throw ErrorCode.USER_CONFIG_FETCH_FAILED.asException();
        }

        for (ConfigItemContent configItemContent : result.getReturnData().getD()) {
            if (StringUtils.equals(configItemContent.getItemCode(), itemCode)) {
                return configItemContent;
            }
        }

        return null;
    }

    @SentinelResource(value = "bimSwitchConfig", fallback = "fallbackBimSwitchConfig")
    public Boolean bimSwitchConfig(final Long userId) {
        final MethodResult<Result<List<ConfigItemContent>>> result =
                saasConfigReadApi.getConfigValuesByCodes(userId,
                        Collections.singletonList(ITEM_CODE));
        if (!result.ok()) {
            return true;
        }

        final List<ConfigItemContent> data = result.getReturnData().getD();
        if (CollectionUtils.isEmpty(data)) {
            return true;
        }

        final ConfigItemContent configItemContent = data.get(0);
        if (!ITEM_CODE.equals(configItemContent.getItemCode())) {
            return true;
        }

        final Map<String, Boolean> itemValue = JsonMapper.parse(configItemContent.getItemValue(),
                new TypeReference<Map<String, Boolean>>() {});
        if (itemValue == null) {
            return true;
        }
        return itemValue.getOrDefault("show", true);
    }

    public Boolean fallbackBimSwitchConfig(final Long userId, final Throwable throwable) {
        LOG.message("fallbackBimSwitchConfig").with(throwable).error();
        return true;
    }

}
