/*
 * BatchOperationStatusEnum.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public enum BatchOperationStatusEnum {
    UNSTART("未开始", 0),
    GOING("进行中", 1),
    DONE("已完成", 2),
    FAILED("失败", 3);

    private final String desc;

    private final int code;

    BatchOperationStatusEnum(final String desc, final int code) {
        this.desc = desc;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static BatchOperationStatusEnum of(int code) {
        return Stream.of(BatchOperationStatusEnum.values())
                .filter(statusEnum -> Objects.equals(statusEnum.getCode(), code)).findFirst()
                .orElse(null);
    }
}
