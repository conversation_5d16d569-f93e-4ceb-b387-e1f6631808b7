/*
 * ToolDataViewController.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.rpc.integration.annotations.RawRestController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@RawRestController
@RequiredArgsConstructor
public class ToolDataViewController {

    private final VrcAppIdDataDb vrcAppIdDataDb;

    @PostMapping("/tps/inner/tdv/vrcAppIdData/save")
    public boolean saveVrcAppIdData(@RequestBody VrcAppIdData vrcAppIdData) {
        return vrcAppIdDataDb.saveVrcAppIdData(vrcAppIdData);
    }

    @PostMapping("/tps/inner/tdv/vrcAppIdData/clear-cache")
    public void saveVrcAppIdData(@RequestParam String stage) {
        vrcAppIdDataDb.clearCache(stage);
    }

    @GetMapping("/tps/inner/tdv/vrcAppIdData/all")
    public List<VrcAppIdData> getAllVrcAppIdData(@RequestParam String appStage) {
        return vrcAppIdDataDb.getAllVrcAppIdList(appStage).getVrcAppIdDataList();
    }

    @GetMapping("/tps/inner/tdv/vrcAppIdData/get")
    public VrcAppIdData getByVTypeAndAppId(@RequestParam String vType,
            @RequestParam Integer appId, @RequestParam String stage) {
        return vrcAppIdDataDb.getByVTypeAndAppId(vType, appId, stage);
    }

    @DeleteMapping("/tps/inner/tdv/vrcAppIdData/delete")
    public boolean deleteVrcAppIdData(@RequestParam String vType, @RequestParam Integer appId,
            @RequestParam String stage) {
        return vrcAppIdDataDb.deleteVrcAppIdData(vType, appId, stage);
    }

    @PostMapping("/tps/inner/tdv/vrcAppIdData/update")
    public boolean updateVrcAppIdData(@RequestBody VrcAppIdData vrcAppIdData) {
        if (vrcAppIdData.getAppKey() == null || vrcAppIdData.getStage() == null) {
            throw new IllegalArgumentException("appKey or stage is null");
        }
        return vrcAppIdDataDb.updateVrcAppIdData(vrcAppIdData);
    }


}
