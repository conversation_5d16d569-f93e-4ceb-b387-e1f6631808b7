/*
 * CoohomCreateDesignSwitch.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoohomCreateDesignSwitch {
    private boolean open = false;

    private List<Long> whiteAccountList;

    private List<Long> whiteUserIdList;
}
