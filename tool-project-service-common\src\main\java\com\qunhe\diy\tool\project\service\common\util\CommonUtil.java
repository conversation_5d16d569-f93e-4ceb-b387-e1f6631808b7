/*
 * CommonUtil.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public class CommonUtil {
    private static final String OUTPUT_DATE_FORMAT = "yyyyMMddHHmmss";

    public static String getCurrentTime() {
        final LocalDateTime currentTime = LocalDateTime.now();
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(OUTPUT_DATE_FORMAT);
        return currentTime.format(formatter);
    }
}
