/*
 * BatchOperationRecordDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.BatchOperationRecordMapper;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class BatchOperationRecordDb {
    private final BatchOperationRecordMapper batchOperationRecordMapper;

    public BatchOperationRecordDb(final BatchOperationRecordMapper batchOperationRecordMapper) {
        this.batchOperationRecordMapper = batchOperationRecordMapper;
    }

    public int insertRecord(final BatchOperationData batchOperationData) {
        if (batchOperationData == null) {
            return 0;
        }
        return batchOperationRecordMapper.insertBatchOperationRecord(batchOperationData);
    }

    public int updateCompleteCountAndStatus(final int additionalCount, final int status,
            final String recordId) {
        return batchOperationRecordMapper.updateCompleteCountAndStatus(additionalCount, status,
                recordId);
    }

    public int updateBatchOperateFinishInfos(final int status, final String link,
            final int completeCount, final String recordId) {
        return batchOperationRecordMapper.updateBatchOperateFinishInfos(status, link,
                completeCount, recordId);
    }

    public int updateOperationRecord(final BatchOperationData batchOperationData) {
        return batchOperationRecordMapper.updateOperationRecord(batchOperationData);
    }

    public BatchOperationData getBatchOperationRecordByRecordId(final String recordId) {
        return batchOperationRecordMapper.getBatchOperationRecordByRecordId(recordId);
    }

    public List<String> getMultiOperateLinksByUserId(final Long userId, final int rollback) {
        return batchOperationRecordMapper.getMultiOperateLinksByUserId(userId, rollback);
    }

}
