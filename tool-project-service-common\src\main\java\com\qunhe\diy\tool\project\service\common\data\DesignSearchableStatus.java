/*
 * DesignSearchableStatus.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Getter
@RequiredArgsConstructor
public enum DesignSearchableStatus {

    DESIGN_NOT_EXIST(-1),

    TAG_CAN_NOT_BE_SEARCHED(0),

    TAG_CAN_BE_SEARCHED(1),

    ;

    final int value;


}
