/*
 * OwnerCheckAdvice.java
 * Copyright 2016 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.service;

import com.google.common.collect.Sets;
import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.DcsOrderAuthFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.HunterLogger;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AuthCheckService {
    private static final HunterLogger LOG = HunterLogger.getLogger(AuthCheckService.class);

    private static final String COOHOM = "coohom";

    public static final String COOHOM_USER_TAG = "coohom_user";

    public static final String COOHOM_USER_APIC_TAG = "coohom_user_apic";

    public static final Set<String> COOHOM_USER_TAGS = Sets.newHashSet(COOHOM_USER_TAG,
            COOHOM_USER_APIC_TAG);



    public static final List<Long> COOHOM_BIM_ACCESS_POINTS = Arrays.asList(527L, 2300L);

    private final ProjectDesignFacadeDb planDb;

    private final ProjectAuthService projectAuthFacade;

    private final DcsOrderAuthFacade dcsOrderAuthFacade;

    private final BusinessAccountDb businessAccountDb;

    private final SaaSConfigService saaSConfigService;

    private final UserInfoFacade userInfoFacade;
    private final ToadProperties toadProperties;


    public boolean checkAuthFromDesignId(final Long userId,
            final Long orderDesignId, Long designId, final boolean cooperate) {
        final YunDesign yunDesign = planDb.getDiyDesignInfo(designId);
        if (yunDesign == null) {
            LOG.message("OwnerCheckAdvice checkAuth failed - design not exist")
                    .with("designId", designId)
                    .with("userId", userId)
                    .warn();
            return false;
        }
        // 回收站方案鉴权失败
        if (yunDesign.getUserId() == 1L) {
            return false;
        }
        designId = yunDesign.getDesignId();
        return checkAuthInternal(cooperate, orderDesignId, designId, yunDesign, userId);
    }

    public boolean checkAuthInternal(final Boolean cooperate, final Long orderDesignId,
            final Long designId, final YunDesign yunDesign, final Long userId) {
        boolean authRes = false;
        boolean needCheckDcs = isCustomVerifyDesign(orderDesignId);
        boolean needCheckCoohom = false;
        if (userId != null) {
            if (needCheckDcs) {
                // 定制订单的请求，调用定制订单接口，进行授权后，再走后续鉴权逻辑
                grantAuthByDcsOrder(orderDesignId, designId, userId);
            }
            needCheckCoohom = Objects.equals(cooperate, Boolean.TRUE);
            authRes = projectAuthFacade.checkAuth(designId, userId, needCheckCoohom,
                    needCheckDcs, orderDesignId, yunDesign, AuthCheckType.WRITE);
            //分别put，为了让后续coohom的老协作逻辑可以简单下线
            if (needCheckCoohom) {
                CooperateHelper.putCooperate(authRes, yunDesign.getUserId());
            }
        }

        if (!authRes) {
            LOG.message("OwnerCheckAdvice checkAuth failed - not authorized")
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("needCheckDcs", needCheckDcs)
                    .with("needCheckCoohom", needCheckCoohom)
                    .with("checkLevel", AuthCheckType.WRITE)
                    .warn();
        }

        return authRes;
    }

    private void grantAuthByDcsOrder(Long orderDesignId, Long designId, Long userId) {
        try {
            if (toadProperties.isDcsOrderGrantAuth()) {
                return;
            }
            LOG.message("start to grant auth by dcs order")
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("orderDesignId", orderDesignId)
                    .info();
            dcsOrderAuthFacade.checkAndUpdateDesignAuth(designId, orderDesignId, userId);
            // catch 异常，做兜底
        } catch (Exception e) {
            LOG.message("grantAuthByDcsOrder error", e)
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("orderDesignId", orderDesignId)
                    .error();
        }
    }

    private boolean isCustomVerifyDesign(final Long dcsOrderDesignId) {
        return dcsOrderDesignId != null || ToolTypeContextHolder.isYunDesignCustom();
    }

    public boolean checkCoohomUser(final HttpServletRequest request, final Long userId) {
        final Set<String> coohomMatchedUserTags = userInfoFacade.matchUserTags(userId,
                COOHOM_USER_TAGS);
        if (coohomMatchedUserTags == null) {
            LOG.message("coohomMatchedUserTags get fail")
                    .with("userId", userId)
                    .warn();
            //用户标签获取异常, 改用域名判断
            return checkCoohomUserByHost(request);
        }
        final Long rootAccountId = businessAccountDb.getRootAccountForBAndC(userId);
        return rootAccountId == null && coohomMatchedUserTags.contains(COOHOM_USER_TAG);
    }

    /**
     * 使用域名判断是否是coohom用户
     * @param request
     * @return
     */
    public boolean checkCoohomUserByHost(final HttpServletRequest request) {
        String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        return StringUtils.isNotEmpty(originalHost) && originalHost.contains(COOHOM);
    }

    /**
     * coohom 5.0权限点校验
     * @return
     */
    public boolean checkCoohomBimAuthPoint(final Long userId) throws AccessAuthenticatorException {
        return saaSConfigService.checkBusinessAccessPoint(userId, null, null,
                COOHOM_BIM_ACCESS_POINTS);
    }

    /**
     * 使用userId判断是否是coohom用户
     * @param userId
     * @return true: 是coohom用户
     */
    public boolean checkCoohomUserByUserId(final Long userId) {
        final Set<String> coohomMatchedUserTags = userInfoFacade.matchUserTags(userId,
                COOHOM_USER_TAGS);
        return CollectionUtils.isNotEmpty(coohomMatchedUserTags);
    }
}
