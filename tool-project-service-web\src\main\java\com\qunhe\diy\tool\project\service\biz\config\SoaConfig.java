/*
 * SoaConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.diy.designinfoservice.client.clients.DesignInfoClient;
import com.qunhe.diy.designinfoservice.client.clients.DesignInfoServiceApiClientFactory;
import com.qunhe.diybe.diyservice.client.DiyServiceApiFactory;
import com.qunhe.diybe.diyservice.client.SessionClient;
import com.qunhe.house.property.floorplan.home.client.FphApiFactory;
import com.qunhe.house.property.floorplan.home.client.FphFloorplanClient;
import com.qunhe.instdeco.designapi.clients.DesignServiceApiClientFactory;
import com.qunhe.instdeco.designapi.clients.YunDesignClient;
import com.qunhe.instdeco.drsapi.clients.base.DrsApiFactory;
import com.qunhe.instdeco.libra.client.AbTestClient;
import com.qunhe.instdeco.plan.generalapi.client.BusinessConfigClient;
import com.qunhe.instdeco.plan.graylaunchmw.db.GrayLaunchDb;
import com.qunhe.instdeco.plan.openapi.client.OpenApiAuthClient;
import com.qunhe.instdeco.plan.openapi.client.PartnerUserMappingClient;
import com.qunhe.instdeco.plan.siteapicommon.base.SiteApiFactory;
import com.qunhe.instdeco.plan.yun.core.client.apis.DecoProjectApi;
import com.qunhe.kam.client.layoutdesigncommon.LayoutDesignCommonApiFactory;
import com.qunhe.kam.client.layoutdesigncommon.LayoutMultiDesignClient;
import com.qunhe.logcomplex.userinformation.client.UserInfoClientProxy;
import com.qunhe.rpc.route.ApiRegistry;
import com.qunhe.saas.config.client.SaasConfigReadApi;
import com.qunhe.saas.design.client.DesignBimSwitchClient;
import com.qunhe.user.growth.design.excellent.client.client.UserDesignClient;
import com.qunhe.user.growth.property.center.client.api.KubiSoaApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class SoaConfig {
    @Bean
    public SiteApiFactory siteApiFactory(final ApiRegistry apiRegistry) {
        return new SiteApiFactory(apiRegistry);
    }

    @Bean
    public FphFloorplanClient fphFloorplanClient(final ApiRegistry apiRegistry) {
        return new FphFloorplanClient(new FphApiFactory(apiRegistry));
    }

    @Bean
    public DecoProjectApi decoProjectApi(final ApiRegistry apiRegistry) {
        return siteApiFactory(apiRegistry).getApiInstance(DecoProjectApi.class);
    }

    @Bean
    public PartnerUserMappingClient partnerUserMappingClient(final SiteApiFactory siteApiFactory) {
        return new PartnerUserMappingClient(siteApiFactory);
    }

    @Bean
    public UserDesignClient userDesignClient(final SiteApiFactory siteApiFactory) {
        return new UserDesignClient(siteApiFactory);
    }

    @Bean
    public LayoutMultiDesignClient layoutMultiDesignClient(final ApiRegistry apiRegistry) {
        return new LayoutMultiDesignClient(new LayoutDesignCommonApiFactory(apiRegistry));
    }

    @Bean
    public DesignInfoClient designInfoClient(final ApiRegistry apiRegistry) {
        return new DesignInfoClient(new DesignInfoServiceApiClientFactory(apiRegistry));
    }

    @Bean
    public SaasConfigReadApi saasConfigReadApi(final SiteApiFactory siteApiFactory) {
        return siteApiFactory.getApiInstance(SaasConfigReadApi.class);
    }

    @Bean
    public BusinessConfigClient businessConfigClient(final SiteApiFactory siteApiFactory) {
        return new BusinessConfigClient(siteApiFactory);
    }

    @Bean
    public AbTestClient abTestClient(final SiteApiFactory siteApiFactory) {
        return new AbTestClient(siteApiFactory);
    }

    @Bean
    public UserInfoClientProxy userInfoClientProxy(final ApiRegistry apiRegistry) {
        return new UserInfoClientProxy(apiRegistry, "1eda55a414575b5259ca083a2a93bf58");
    }

    @Bean
    public SessionClient sessionClient(final ApiRegistry apiRegistry) {
        return new SessionClient(new DiyServiceApiFactory(apiRegistry));
    }

    @Bean
    public OpenApiAuthClient openApiAuthClient(final SiteApiFactory siteApiFactory) {
        return new OpenApiAuthClient(siteApiFactory);
    }

    @Bean
    public DesignBimSwitchClient designBimSwitchClient(final SiteApiFactory siteApiFactory) {
        return new DesignBimSwitchClient(siteApiFactory);
    }

    @Bean
    public YunDesignClient yunDesignClient(final ApiRegistry apiRegistry) {
        return new YunDesignClient(new DesignServiceApiClientFactory(apiRegistry));
    }

    @Bean
    public DrsApiFactory drsApiFactory(final ApiRegistry apiRegistry,
            final GrayLaunchDb grayLaunchDb) {
        return new DrsApiFactory(apiRegistry, grayLaunchDb);
    }

    @Bean
    public KubiSoaApi kubiSoaApi(final SiteApiFactory siteApiFactory) {
        return siteApiFactory.getApiInstance(KubiSoaApi.class);
    }

}
