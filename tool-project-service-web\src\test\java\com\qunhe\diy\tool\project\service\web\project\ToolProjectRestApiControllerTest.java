/*
 * ToolProjectRestApiControllerTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.fasterxml.jackson.core.type.TypeReference;
import com.qunhe.diy.bucket4j.RateLimitExceptionHandler;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesign;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignBatchGetRequest;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignLevel;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignStatus;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiLevelBatchUpdateRequest;
import com.qunhe.diy.tool.project.service.web.config.WebConfig;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.module.restapi.common.data.response.RestApiBatchResponse;
import com.qunhe.diybe.module.restapi.common.operation.Operation;
import com.qunhe.diybe.module.restapi.core.config.RestApiExceptionHandler;
import com.qunhe.diybe.module.restapi.core.config.RestApiMvcConfig;
import com.qunhe.interceptors.SessionUserContext;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.BATCH_GET_DESIGN_RESOURCE_PATH;
import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.DESIGN_RESOURCE_PATH;
import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.LEVEL_BATCH_UPDATE_RESOURCE_PATH;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(ToolProjectRestApiController.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = { WebConfig.class, ToolProjectRestApiController.class,
        RestApiExceptionHandler.class, RestApiMvcConfig.class })
public class ToolProjectRestApiControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ToolProjectRestApiService mockToolProjectRestApiService;

    @MockBean
    private ProjectClient mockProjectClient;

    @MockBean
    private RateLimitExceptionHandler rateLimitExceptionHandler;

    private static final Long USER_ID = 123456L;
    private static final Long DESIGN_ID = 1001L;
    private static final Long PLAN_ID = 2001L;
    private static final String ENCRYPTED_DESIGN_ID = LongCipher.DEFAULT.encrypt(DESIGN_ID);
    private static final String ENCRYPTED_PLAN_ID = LongCipher.DEFAULT.encrypt(PLAN_ID);

    @Test
    public void testGetProjectDesign_WithDesignIdType_Success() throws Exception {
        // 准备模拟数据
        RestApiDesign design = createMockDesign(DESIGN_ID);
        when(mockToolProjectRestApiService.getProjectDesigns(
                Collections.singletonList(DESIGN_ID), USER_ID))
                .thenReturn(Collections.singletonList(design));

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        get(DESIGN_RESOURCE_PATH, ENCRYPTED_DESIGN_ID)
                                .param("idType", "DESIGN")
                                .header("x-qh-id", USER_ID)
                                .accept(MediaType.APPLICATION_JSON))

                .andReturn().getResponse();

        // 验证结果
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        RestApiDesign result = JsonMapper.parse(response.getContentAsString(), RestApiDesign.class);
        assertThat(result).isNotNull();
        assertThat(result.getDesignId()).isEqualTo(ENCRYPTED_DESIGN_ID);
    }

    @Test
    public void testGetProjectDesign_WithPlanIdType_Success() throws Exception {
        // 准备模拟数据
        RestApiDesign design = createMockDesign(DESIGN_ID);
        when(mockProjectClient.getDesignIdByPlanId(PLAN_ID)).thenReturn(DESIGN_ID);
        when(mockToolProjectRestApiService.getProjectDesigns(
                Collections.singletonList(DESIGN_ID), USER_ID))
                .thenReturn(Collections.singletonList(design));

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        get(DESIGN_RESOURCE_PATH, ENCRYPTED_PLAN_ID)
                                .param("idType", "PLAN")
                                .header("x-qh-id", USER_ID)
                                .accept(MediaType.APPLICATION_JSON))

                .andReturn().getResponse();

        // 验证结果
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        RestApiDesign result = JsonMapper.parse(response.getContentAsString(), RestApiDesign.class);
        assertThat(result).isNotNull();
        assertThat(result.getDesignId()).isEqualTo(ENCRYPTED_DESIGN_ID);
    }

    @Test
    public void testGetProjectDesign_InvalidIdType() throws Exception {
        // 执行请求，使用无效的idType
        MockHttpServletResponse response = mockMvc.perform(
                        get(DESIGN_RESOURCE_PATH, ENCRYPTED_DESIGN_ID)
                                .param("idType", "INVALID_TYPE")
                                .header("x-qh-id", USER_ID)
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证返回错误
        assertThat(response.getStatus()).isEqualTo(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void testGetProjectDesign_PlanNotExist() throws Exception {
        // 模拟plan不存在情况
        when(mockProjectClient.getDesignIdByPlanId(PLAN_ID)).thenReturn(null);

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        get(DESIGN_RESOURCE_PATH, ENCRYPTED_PLAN_ID)
                                .param("idType", "PLAN")
                                .header("x-qh-id", USER_ID)
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证返回错误
        assertThat(response.getStatus()).isEqualTo(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void testGetProjectDesign_DesignNotFound() throws Exception {
        // 模拟设计不存在情况
        when(mockToolProjectRestApiService.getProjectDesigns(
                Collections.singletonList(DESIGN_ID), USER_ID))
                .thenReturn(Collections.emptyList());

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        get(DESIGN_RESOURCE_PATH, ENCRYPTED_DESIGN_ID)
                                .param("idType", "DESIGN")
                                .header("x-qh-id", USER_ID)
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证返回null
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getContentAsString()).isEqualTo("");
    }

    @Test
    public void testBatchGetProjectDesign_WithDesignIdType_Success() throws Exception {
        // 准备模拟数据
        List<RestApiDesign> designs = Arrays.asList(
                createMockDesign(1001L),
                createMockDesign(1002L)
        );

        List<Long> designIds = Arrays.asList(1001L, 1002L);
        when(mockToolProjectRestApiService.getProjectDesigns(designIds, USER_ID))
                .thenReturn(designs);

        // 构建请求数据
        RestApiDesignBatchGetRequest request = new RestApiDesignBatchGetRequest();
        request.setIdType("DESIGN");
        request.setIds(Arrays.asList(
                LongCipher.DEFAULT.encrypt(1001L),
                LongCipher.DEFAULT.encrypt(1002L)
        ));

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        post(BATCH_GET_DESIGN_RESOURCE_PATH)
                                .contentType(MediaType.APPLICATION_JSON)
                                .header("x-qh-id", USER_ID)
                                .content(JsonMapper.writeValueAsString(request))
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证结果
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        RestApiBatchResponse<RestApiDesign> result = JsonMapper.parse(
                response.getContentAsString(),
                new TypeReference<RestApiBatchResponse<RestApiDesign>>() {
                }
        );
        assertThat(result.getElements()).hasSize(2);
    }

    @Test
    public void testBatchGetProjectDesign_WithPlanIdType_Success() throws Exception {
        // 准备模拟数据
        List<RestApiDesign> designs = Arrays.asList(
                createMockDesign(1001L),
                createMockDesign(1002L)
        );

        List<Long> planIds = Arrays.asList(2001L, 2002L);
        List<Long> designIds = Arrays.asList(1001L, 1002L);

        when(mockProjectClient.getDesignIdsByFloorPlanIds(planIds))
                .thenReturn(designIds);
        when(mockToolProjectRestApiService.getProjectDesigns(designIds, USER_ID))
                .thenReturn(designs);

        // 构建请求数据
        RestApiDesignBatchGetRequest request = new RestApiDesignBatchGetRequest();
        request.setIdType("PLAN");
        request.setIds(Arrays.asList(
                LongCipher.DEFAULT.encrypt(2001L),
                LongCipher.DEFAULT.encrypt(2002L)
        ));

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        post(BATCH_GET_DESIGN_RESOURCE_PATH)
                                .contentType(MediaType.APPLICATION_JSON)
                                .header("x-qh-id", USER_ID)
                                .content(JsonMapper.writeValueAsString(request))
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证结果
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        RestApiBatchResponse<RestApiDesign> result = JsonMapper.parse(
                response.getContentAsString(),
                new TypeReference<RestApiBatchResponse<RestApiDesign>>() {
                }
        );
        assertThat(result.getElements()).hasSize(2);
    }

    @Test
    public void testBatchGetProjectDesign_InvalidIdType() throws Exception {
        // 构建请求数据
        RestApiDesignBatchGetRequest request = new RestApiDesignBatchGetRequest();
        request.setIdType("INVALID_TYPE");
        request.setIds(Arrays.asList(
                LongCipher.DEFAULT.encrypt(1001L),
                LongCipher.DEFAULT.encrypt(1002L)
        ));

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        post(BATCH_GET_DESIGN_RESOURCE_PATH)
                                .contentType(MediaType.APPLICATION_JSON)
                                .header("x-qh-id", USER_ID)
                                .content(JsonMapper.writeValueAsString(request))
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证返回错误
        assertThat(response.getStatus()).isEqualTo(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void testBatchGetProjectDesign_TooManyIds() throws Exception {
        // 构建请求数据，超过20个ID
        RestApiDesignBatchGetRequest request = new RestApiDesignBatchGetRequest();
        request.setIdType("DESIGN");
        List<String> ids = new ArrayList<>();
        for (int i = 0; i < 21; i++) {
            ids.add(LongCipher.DEFAULT.encrypt(1000L + i));
        }
        request.setIds(ids);

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        post(BATCH_GET_DESIGN_RESOURCE_PATH)
                                .contentType(MediaType.APPLICATION_JSON)
                                .header("x-qh-id", USER_ID)
                                .content(JsonMapper.writeValueAsString(request))
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证返回错误
        assertThat(response.getStatus()).isEqualTo(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void testBatchUpdateLevels_Success() throws Exception {
        // 准备模拟数据
        List<LevelInfo> levelInfos = Collections.singletonList(new LevelInfo());
        RestApiLevelBatchUpdateRequest request = new RestApiLevelBatchUpdateRequest();

        when(mockToolProjectRestApiService.batchUpdateLevels(eq(DESIGN_ID),
                any(RestApiLevelBatchUpdateRequest.class)))
                .thenReturn(levelInfos);

        // 执行请求
        MockHttpServletResponse response = mockMvc.perform(
                        post(LEVEL_BATCH_UPDATE_RESOURCE_PATH, ENCRYPTED_DESIGN_ID)
                                .contentType(MediaType.APPLICATION_JSON)
                                .header("x-qh-id", USER_ID)
                                .content(JsonMapper.writeValueAsString(request))
                                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // 验证结果
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        Operation<RestApiBatchResponse<LevelInfo>> result = JsonMapper.parse(
                response.getContentAsString(),
                new TypeReference<Operation<RestApiBatchResponse<LevelInfo>>>() {
                }
        );
        assertThat(result.isDone()).isTrue();
        assertThat(result.getResult().getElements()).hasSize(1);
    }

    private RestApiDesign createMockDesign(Long id) {
        String encryptedId = LongCipher.DEFAULT.encrypt(id);

        RestApiDesignLevel level = RestApiDesignLevel.builder()
                .levelId(LongCipher.DEFAULT.encrypt(5001L))
                .name("一层")
                .index(1)
                .build();

        return RestApiDesign.builder()
                .designId(encryptedId)
                .planId(LongCipher.DEFAULT.encrypt(id + 1000))
                .ownerAppUid("user" + id)
                .name("设计" + id)
                .coverPic("http://example.com/cover.jpg")
                .designDesc("这是一个测试设计")
                .communityName("测试小区")
                .distinctFullName("浙江省杭州市西湖区")
                .status(RestApiDesignStatus.NORMAL)
                .createdTime(System.currentTimeMillis())
                .modifiedTime(System.currentTimeMillis())
                .levels(Collections.singletonList(level))
                .build();
    }
}