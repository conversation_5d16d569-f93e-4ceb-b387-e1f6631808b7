/*
 * SaaSConfigServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qunhe.assembly.oplog.utils.JsonUtils;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.instdeco.libra.client.AbTestClient;
import com.qunhe.instdeco.libra.service.share.dto.AbTestExperimentConfigDto;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap;
import com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient;
import com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.saas.config.client.data.ConfigItemContent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE;
import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.COMMON_CONFIG_FAVOR_ICON;
import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.PROJECT_COUNT_POINT;
import static com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade.COOHOM_USER_TAG;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * SaaSConfigService 的单元测试类
 * <AUTHOR>
 */
public class SaaSConfigServiceTest {

    @Mock
    private AccessPointClient accessPointClient;
            // 注意，SaaSConfigService 中有两个 AccessPointClient，这里 mock 的是第一个
    @Mock
    private com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient
            commercialAccessPointClient; // Mock 第二个 AccessPointClient
    @Mock
    private SaasConfigFacade saasConfigFacade;
    @Mock
    private AbTestClient abTestClient;
    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private BusinessAccountDb businessAccountDb;

    private SaaSConfigService saaSConfigService;

    private static final Long TEST_USER_ID = 12345L;
    private static final Long TEST_ROOT_ACCOUNT_ID = 67890L;
    private static final Long TEST_ACCOUNT_ID = 54321L;
    private static final String TEST_ITEM_CODE = "testItemCode";
    private static final String TEST_APP_KEY = "testAppKey";
    private static final String TEST_COOKIE_ID = "testCookieId";
    private static final String TEST_IP = "127.0.0.1";


    @Before
    public void setUp() {
        // 初始化 Mockito 注解
        MockitoAnnotations.openMocks(this);
        // 手动创建 SaaSConfigService 实例，因为 @InjectMocks 无法处理 final 字段
        saaSConfigService = new SaaSConfigService(
                accessPointClient,
                saasConfigFacade,
                abTestClient,
                commercialAccessPointClient,
                userInfoFacade,
                businessAccountDb
        );
    }

    /**
     * 测试用户拥有项目数量权限点的情况
     */
    @Test
    public void testCheckProjectCountPointAccess_HasAccess() throws Exception {
        // 准备数据
        List<Long> accessiblePoints = new LinkedList<>();
        accessiblePoints.add(PROJECT_COUNT_POINT);
        // mock行为：当调用 accessPointClient.filterAccessPoint 时返回包含权限点的列表
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                accessiblePoints);

        // 调用被测方法
        Boolean result = saaSConfigService.checkProjectCountPointAccess(TEST_USER_ID);

        // 断言结果：用户拥有权限点，应返回true
        Assert.assertTrue("用户拥有项目数量权限点，结果应为true", result);
        // 验证 accessPointClient.filterAccessPoint 被正确调用
        verify(accessPointClient, times(1)).filterAccessPoint(
                any(CheckAccessibleRequestBody.class));
    }

    /**
     * 测试用户不拥有项目数量权限点的情况（返回空列表）
     */
    @Test
    public void testCheckProjectCountPointAccess_NoAccess_EmptyList() throws Exception {
        // mock行为：当调用 accessPointClient.filterAccessPoint 时返回空列表
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                Collections.emptyList());

        // 调用被测方法
        Boolean result = saaSConfigService.checkProjectCountPointAccess(TEST_USER_ID);

        // 断言结果：用户不拥有权限点，应返回false
        Assert.assertFalse("用户不拥有项目数量权限点（空列表），结果应为false", result);
    }

    /**
     * 测试用户不拥有项目数量权限点的情况（列表不包含目标权限点）
     */
    @Test
    public void testCheckProjectCountPointAccess_NoAccess_ListNotContainsPoint() throws Exception {
        // 准备数据
        List<Long> accessiblePoints = new LinkedList<>();
        accessiblePoints.add(99999L); // 一个无关的权限点
        // mock行为：当调用 accessPointClient.filterAccessPoint 时返回不包含目标权限点的列表
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                accessiblePoints);

        // 调用被测方法
        Boolean result = saaSConfigService.checkProjectCountPointAccess(TEST_USER_ID);

        // 断言结果：用户不拥有权限点，应返回false
        Assert.assertFalse("用户不拥有项目数量权限点（列表不含目标），结果应为false", result);
    }



    /**
     * 测试 `checkProjectCountPointAccess` 方法已存在的用例 (与原用例合并和增强)
     * 原始用例：testCheckProjectCountPointAccess
     */
    @Test
    public void testCheckProjectCountPointAccess_OriginalAndEnhanced() throws Exception {
        // 准备数据: 拥有权限点
        List<Long> list = new LinkedList<>();
        list.add(10716L); // PROJECT_COUNT_POINT
        // mock行为: 当调用 accessPointClient.filterAccessPoint 时返回包含权限点的列表
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                list);

        // 调用被测方法
        Boolean result = saaSConfigService.checkProjectCountPointAccess(Long.valueOf(1));
        // 断言结果: 用户拥有权限点，应返回true
        Assert.assertTrue("用户拥有权限点，结果应为true", result);

        // 准备数据: 不拥有权限点 (返回空列表)
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                Collections.emptyList());
        result = saaSConfigService.checkProjectCountPointAccess(Long.valueOf(2));
        // 断言结果: 用户不拥有权限点，应返回false
        Assert.assertFalse("用户不拥有权限点 (空列表)，结果应为false", result);

        // 准备数据: 不拥有权限点 (列表不包含目标权限点)
        List<Long> otherPoints = Lists.newArrayList(999L);
        when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(
                otherPoints);
        result = saaSConfigService.checkProjectCountPointAccess(Long.valueOf(3));
        // 断言结果: 用户不拥有权限点，应返回false
        Assert.assertFalse("用户不拥有权限点 (列表不含目标权限点)，结果应为false", result);
    }


    /**
     * 测试 `checkProjectCountPointAccessHandler` 方法
     * 原始用例：testCheckProjectCountPointAccessHandler
     */
    @Test
    public void testCheckProjectCountPointAccessHandler_Original() throws Exception {
        // 调用被测方法: 模拟 fallback 场景
        Boolean result = saaSConfigService.checkProjectCountPointAccessHandler(1111251217L,
                new RuntimeException("Test Fallback"));
        // 断言结果: fallback 应返回 false
        Assert.assertFalse("Fallback handler 应返回 false", result);
    }

    /**
     * 测试获取业务配置，当 SaasConfigFacade 返回 null
     */
    @Test
    public void testGetBusinessConfigMap_SaasConfigReturnsNull() {
        // mock行为：当调用 saasConfigFacade.getUserConfig 时返回 null
        when(saasConfigFacade.getUserConfig(TEST_USER_ID, TEST_ITEM_CODE)).thenReturn(null);

        // 调用被测方法
        BusinessConfigMap result = saaSConfigService.getBusinessConfigMap(TEST_USER_ID,
                TEST_ITEM_CODE);

        // 断言结果：应返回空的 BusinessConfigMap
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("Model应为空", result.getModel() == null || result.getModel().isEmpty());
    }

    /**
     * 测试获取业务配置，当 ConfigItemContent 的 itemValue 为 null
     */
    @Test
    public void testGetBusinessConfigMap_ItemValueIsNull() {
        // 准备数据
        ConfigItemContent configItemContent = new ConfigItemContent();
        configItemContent.setItemValue(null); // itemValue 为 null
        // mock行为：当调用 saasConfigFacade.getUserConfig 时返回准备好的数据
        when(saasConfigFacade.getUserConfig(TEST_USER_ID, TEST_ITEM_CODE)).thenReturn(
                configItemContent);

        // 调用被测方法
        BusinessConfigMap result = saaSConfigService.getBusinessConfigMap(TEST_USER_ID,
                TEST_ITEM_CODE);

        // 断言结果：应返回空的 BusinessConfigMap
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("Model应为空", result.getModel() == null || result.getModel().isEmpty());
    }

    /**
     * 测试获取业务配置，成功获取并解析
     */
    @Test
    public void testGetBusinessConfigMap_Success() {
        // mock static JsonUtils.parseString
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            // 准备数据
            ConfigItemContent configItemContent = new ConfigItemContent();
            String jsonValue = "{\"show\":true,\"picUrl\":\"test.ico\"}";
            configItemContent.setItemValue(jsonValue);

            BusinessConfigElement expectedElement = new BusinessConfigElement();
            expectedElement.setShow(true);
            expectedElement.setPicUrl("test.ico");

            // mock行为：saasConfigFacade.getUserConfig 返回数据
            when(saasConfigFacade.getUserConfig(TEST_USER_ID, TEST_ITEM_CODE)).thenReturn(
                    configItemContent);
            // mock行为：JsonUtils.parseString 返回解析后的对象
            mockedJsonUtils.when(
                            () -> JsonUtils.parseString(jsonValue, BusinessConfigElement.class))
                    .thenReturn(expectedElement);

            // 调用被测方法
            BusinessConfigMap result = saaSConfigService.getBusinessConfigMap(TEST_USER_ID,
                    TEST_ITEM_CODE);

            // 断言结果：应返回包含解析后元素的 BusinessConfigMap
            Assert.assertNotNull("结果不应为null", result);
            Assert.assertNotNull("Model不应为null", result.getModel());
            Assert.assertEquals("Model中应包含一个元素", 1, result.getModel().size());
            Assert.assertEquals("解析后的元素应与预期一致", expectedElement,
                    result.getModel().get(TEST_ITEM_CODE));
        }
    }

    /**
     * 测试 `getBusinessConfigMapFallBack` 方法，针对 COMMON_CONFIG_FAVOR_ICON
     */
    @Test
    public void testGetBusinessConfigMapFallBack_FavorIcon() {
        // 调用被测方法
        BusinessConfigMap result = saaSConfigService.getBusinessConfigMapFallBack(TEST_USER_ID,
                COMMON_CONFIG_FAVOR_ICON);

        // 断言结果：应返回 COMMON_CONFIG_FAVOR_ICON 的预设配置
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertNotNull("Model不应为null", result.getModel());
        Assert.assertTrue("Model中应包含预设的favorIcon配置",
                result.getModel().containsKey(COMMON_CONFIG_FAVOR_ICON));
        BusinessConfigElement element = result.getModel().get(COMMON_CONFIG_FAVOR_ICON);
        Assert.assertTrue("favorIcon应显示", element.getShow());
        Assert.assertEquals("favorIcon的picUrl应正确",
                "//www.kujiale.com/static/images/favicon.ico", element.getPicUrl());
    }

    /**
     * 测试 `getBusinessConfigMapFallBack` 方法，针对 ALL_TOOL_CONFIG_LANGUAGE_TYPE
     */
    @Test
    public void testGetBusinessConfigMapFallBack_LanguageType() {
        // 调用被测方法
        BusinessConfigMap result = saaSConfigService.getBusinessConfigMapFallBack(TEST_USER_ID,
                ALL_TOOL_CONFIG_LANGUAGE_TYPE);

        // 断言结果：应返回 ALL_TOOL_CONFIG_LANGUAGE_TYPE 的预设配置
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertNotNull("Model不应为null", result.getModel());
        Assert.assertTrue("Model中应包含预设的languageType配置",
                result.getModel().containsKey(ALL_TOOL_CONFIG_LANGUAGE_TYPE));
        BusinessConfigElement element = result.getModel().get(ALL_TOOL_CONFIG_LANGUAGE_TYPE);
        Assert.assertTrue("languageType应显示", element.getShow());
        Assert.assertEquals("languageType的status应为0", Integer.valueOf(0), element.getStatus());
    }

    /**
     * 测试 `getBusinessConfigMapFallBack` 方法，针对未预设的 itemCode
     */
    @Test
    public void testGetBusinessConfigMapFallBack_UnknownItemCode() {
        String unknownItemCode = "unknownItemCode";
        // 调用被测方法
        BusinessConfigMap result = saaSConfigService.getBusinessConfigMapFallBack(TEST_USER_ID,
                unknownItemCode);

        // 断言结果：Model应为null或空，因为 BUSINESS_CONFIG 不包含此 itemCode
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("Model应为null或空，因为itemCode未知",
                result.getModel() == null || result.getModel().isEmpty());
    }


    /**
     * 测试检查业务访问点，有权限的情况
     */
    @Test
    public void testCheckBusinessAccessPoint_HasAccess() throws Exception {
        List<Long> accessPoints = Collections.singletonList(1L);
        // mock行为：commercialAccessPointClient.checkAccessible 返回 true
        when(commercialAccessPointClient.checkAccessible(
                any(CheckAccessibleRequestBody.class))).thenReturn(true);

        // 调用被测方法
        Boolean result = saaSConfigService.checkBusinessAccessPoint(TEST_USER_ID,
                TEST_ROOT_ACCOUNT_ID, TEST_ACCOUNT_ID, accessPoints);

        // 断言结果：应返回 true
        Assert.assertTrue("有权限时应返回true", result);
    }

    /**
     * 测试检查业务访问点，无权限的情况
     */
    @Test
    public void testCheckBusinessAccessPoint_NoAccess() throws Exception {
        List<Long> accessPoints = Collections.singletonList(1L);
        // mock行为：commercialAccessPointClient.checkAccessible 返回 false
        when(commercialAccessPointClient.checkAccessible(
                any(CheckAccessibleRequestBody.class))).thenReturn(false);

        // 调用被测方法
        Boolean result = saaSConfigService.checkBusinessAccessPoint(TEST_USER_ID,
                TEST_ROOT_ACCOUNT_ID, TEST_ACCOUNT_ID, accessPoints);

        // 断言结果：应返回 false
        Assert.assertFalse("无权限时应返回false", result);
    }

    /**
     * 测试检查业务访问点，抛出 AccessAuthenticatorException 的情况
     */
    @Test(expected = AccessAuthenticatorException.class)
    public void testCheckBusinessAccessPoint_ThrowsException() throws Exception {
        List<Long> accessPoints = Collections.singletonList(1L);
        // mock行为：commercialAccessPointClient.checkAccessible 抛出异常
        when(commercialAccessPointClient.checkAccessible(any(CheckAccessibleRequestBody.class)))
                .thenThrow(new AccessAuthenticatorException("Test Exception"));

        // 调用被测方法，期望异常
        saaSConfigService.checkBusinessAccessPoint(TEST_USER_ID, TEST_ROOT_ACCOUNT_ID,
                TEST_ACCOUNT_ID, accessPoints);
    }

    /**
     * 测试 `checkBusinessAccessPointFallback` 方法
     */
    @Test
    public void testCheckBusinessAccessPointFallback() {
        List<Long> accessPoints = Collections.singletonList(1L);
        // 调用被测方法
        Boolean result = saaSConfigService.checkBusinessAccessPointFallback(TEST_USER_ID,
                TEST_ROOT_ACCOUNT_ID, TEST_ACCOUNT_ID, accessPoints,
                new RuntimeException("Test Fallback"));

        // 断言结果：应返回 false
        Assert.assertFalse("Fallback应返回false", result);
    }

    /**
     * 测试获取AB测试结果，当 abTestClient 返回 null
     */
    @Test
    public void testGetAbTestResult_ClientReturnsNull() {
        // mock行为：abTestClient.getAbTestExperimentConfig 返回 null
        when(abTestClient.getAbTestExperimentConfig(TEST_APP_KEY, TEST_COOKIE_ID, TEST_USER_ID,
                TEST_IP)).thenReturn(null);

        // 调用被测方法
        Map<String, String> result = saaSConfigService.getAbTestResult(TEST_APP_KEY, TEST_COOKIE_ID,
                TEST_USER_ID, TEST_IP);

        // 断言结果：应返回空 Map
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("结果应为空Map", result.isEmpty());
    }

    /**
     * 测试获取AB测试结果，当 AbTestExperimentConfigDto 或 optimized 字段为 null
     */
    @Test
    public void testGetAbTestResult_DtoOrOptimizedIsNull() {
        Map<String, AbTestExperimentConfigDto> apiResult = new HashMap<>();
        apiResult.put("exp1", null); // experiment 1 is null

        AbTestExperimentConfigDto exp2Dto = new AbTestExperimentConfigDto();
        exp2Dto.setOptimized(null); // experiment 2 optimized is null
        apiResult.put("exp2", exp2Dto);

        AbTestExperimentConfigDto exp3Dto = new AbTestExperimentConfigDto();
        exp3Dto.setOptimized(true);
        apiResult.put("exp3", exp3Dto);


        // mock行为：abTestClient.getAbTestExperimentConfig 返回准备的数据
        when(abTestClient.getAbTestExperimentConfig(TEST_APP_KEY, TEST_COOKIE_ID, TEST_USER_ID,
                TEST_IP)).thenReturn(apiResult);

        // 调用被测方法
        Map<String, String> result = saaSConfigService.getAbTestResult(TEST_APP_KEY, TEST_COOKIE_ID,
                TEST_USER_ID, TEST_IP);

        // 断言结果
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果Map大小应为3", 3, result.size());
        // 断言：exp1 对应的值为 "null"
        Assert.assertEquals("exp1的值应为'null'", "null", result.get("exp1"));
        // 断言：exp2 对应的值为 "null"
        Assert.assertEquals("exp2的值应为'null'", "null", result.get("exp2"));
        // 断言：exp3 对应的值为 "true"
        Assert.assertEquals("exp3的值应为'true'", "true", result.get("exp3"));
    }

    /**
     * 测试获取AB测试结果，成功获取并转换
     */
    @Test
    public void testGetAbTestResult_Success() {
        Map<String, AbTestExperimentConfigDto> apiResult = new HashMap<>();
        AbTestExperimentConfigDto dto1 = new AbTestExperimentConfigDto();
        dto1.setOptimized(true);
        apiResult.put("experimentA", dto1);

        AbTestExperimentConfigDto dto2 = new AbTestExperimentConfigDto();
        dto2.setOptimized(false);
        apiResult.put("experimentB", dto2);

        // mock行为：abTestClient.getAbTestExperimentConfig 返回准备的数据
        when(abTestClient.getAbTestExperimentConfig(TEST_APP_KEY, TEST_COOKIE_ID, TEST_USER_ID,
                TEST_IP)).thenReturn(apiResult);

        // 调用被测方法
        Map<String, String> result = saaSConfigService.getAbTestResult(TEST_APP_KEY, TEST_COOKIE_ID,
                TEST_USER_ID, TEST_IP);

        // 断言结果
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertEquals("结果Map大小应为2", 2, result.size());
        // 断言：experimentA 对应的值为 "true"
        Assert.assertEquals("experimentA的值应为'true'", "true", result.get("experimentA"));
        // 断言：experimentB 对应的值为 "false"
        Assert.assertEquals("experimentB的值应为'false'", "false", result.get("experimentB"));
    }

    /**
     * 测试 `getAbTestResultFallback` 方法
     */
    @Test
    public void testGetAbTestResultFallback() {
        // 调用被测方法
        Map<String, Boolean> result = saaSConfigService.getAbTestResultFallback(TEST_APP_KEY,
                TEST_COOKIE_ID, TEST_USER_ID, TEST_IP, new DegradeException("Test Block"));

        // 断言结果：应返回空 Map
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("Fallback结果应为空Map", result.isEmpty());
    }

    /**
     * 测试回收DIY权限：rootAccountId 为 null, userInfoFacade.matchUserTags 包含 COOHOM_USER_TAG
     * 预期：不回收 (因为 `isEmpty` 为 false, `!isEmpty` 为 false, 即返回 false)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNull_MatchCoohomTag() {
        Map<String, String> abConfig = Collections.emptyMap();
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(null);
        // mock行为：userInfoFacade.matchUserTags 返回包含 COOHOM_USER_TAG 的集合
        when(userInfoFacade.matchUserTags(TEST_USER_ID, Sets.newHashSet(COOHOM_USER_TAG)))
                .thenReturn(Sets.newHashSet(COOHOM_USER_TAG)); // 不为空

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 false (不回收)
        Assert.assertFalse("rootAccountId为null且匹配COOHOM_USER_TAG，应不回收(返回false)", result);
    }

    /**
     * 测试回收DIY权限：rootAccountId 为 null, userInfoFacade.matchUserTags 不包含 COOHOM_USER_TAG
     * 预期：回收 (因为 `isEmpty` 为 true, `!isEmpty` 为 true, 即返回 true)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNull_NoMatchCoohomTag() {
        Map<String, String> abConfig = Collections.emptyMap();
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(null);
        // mock行为：userInfoFacade.matchUserTags 返回空集合
        when(userInfoFacade.matchUserTags(TEST_USER_ID, Sets.newHashSet(COOHOM_USER_TAG)))
                .thenReturn(Collections.emptySet()); // 为空

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 true (回收)
        Assert.assertTrue("rootAccountId为null且不匹配COOHOM_USER_TAG，应回收(返回true)", result);
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, userInfoFacade.isCoohomUser 为 true
     * 预期：不回收 (返回 false)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_IsCoohomUser() {
        Map<String, String> abConfig = Collections.emptyMap();
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 true
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(true);

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 false (不回收)
        Assert.assertFalse("rootAccountId非null且是Coohom用户，应不回收(返回false)", result);
        // 验证 checkBusinessRecycleFourBucketAuthWithConfig 未被调用
        verify(abTestClient, never()).getAbTestExperimentConfig(anyString(), anyString(), anyLong(),
                anyString());
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, isCoohomUser 为 false, ABConfig
     * RECOVERY_DIY_PERMISSION_NEW_B 为 "true"
     * 预期：回收 (返回 true)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_NotCoohomUser_NewBTrue() {
        Map<String, String> abConfig = ImmutableMap.of("recoveryFourPermission_new_B", "true",
                "recoveryFourPermission_B", "false");
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 false
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 true (回收)
        Assert.assertTrue("recoveryFourPermission_new_B为true，应回收(返回true)", result);
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, isCoohomUser 为 false, ABConfig RECOVERY_DIY_PERMISSION_B 为
     * "true"
     * 预期：回收 (返回 true)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_NotCoohomUser_BTrue() {
        Map<String, String> abConfig = ImmutableMap.of("recoveryFourPermission_new_B", "false",
                "recoveryFourPermission_B", "true");
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 false
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 true (回收)
        Assert.assertTrue("recoveryFourPermission_B为true，应回收(返回true)", result);
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, isCoohomUser 为 false, ABConfig 两者都为 "false"
     * 预期：不回收 (返回 false)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_NotCoohomUser_BothFalse() {
        Map<String, String> abConfig = ImmutableMap.of("recoveryFourPermission_new_B", "false",
                "recoveryFourPermission_B", "false");
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 false
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 false (不回收)
        Assert.assertFalse("AB配置均为false，应不回收(返回false)", result);
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, isCoohomUser 为 false, ABConfig key 不存在或值为 "null"
     * 预期：不回收 (返回 false)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_NotCoohomUser_AbConfigKeyMissingOrNull() {
        Map<String, String> abConfigMissing = Collections.emptyMap();
        Map<String, String> abConfigNull = new HashMap<>();
        abConfigNull.put("recoveryFourPermission_new_B", null);
        abConfigNull.put("recoveryFourPermission_B", null);

        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 false
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);

        // 调用被测方法 - key missing
        boolean resultMissing = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID,
                abConfigMissing);
        // 断言结果：应返回 false (不回收)
        Assert.assertFalse("AB配置key缺失，应不回收(返回false)", resultMissing);

        // 调用被测方法 - value null
        boolean resultNull = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID,
                abConfigNull);
        // 断言结果：应返回 false (不回收)
        Assert.assertFalse("AB配置value为null，应不回收(返回false)", resultNull);
    }

    /**
     * 测试回收DIY权限：rootAccountId 非 null, isCoohomUser 为 false, ABConfig
     * RECOVERY_DIY_PERMISSION_NEW_B 为 "anyOtherString"
     * 预期：不回收 (返回 false)
     */
    @Test
    public void testCheckRecycleDiyAuth_RootNotNull_NotCoohomUser_NewBNonBooleanString() {
        Map<String, String> abConfig = ImmutableMap.of("recoveryFourPermission_new_B",
                "anyOtherString", "recoveryFourPermission_B", "false");
        // mock行为：businessAccountDb.getRootAccountIdByUserId 返回非 null
        when(businessAccountDb.getRootAccountIdByUserId(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);
        // mock行为：userInfoFacade.isCoohomUser 返回 false
        when(userInfoFacade.isCoohomUser(TEST_USER_ID)).thenReturn(false);

        // 调用被测方法
        boolean result = saaSConfigService.checkRecycleDiyAuthWithAbConfig(TEST_USER_ID, abConfig);

        // 断言结果：应返回 false (不回收, 因为 "anyOtherString" 解析为 false)
        Assert.assertFalse("recoveryFourPermission_new_B为非'true'字符串，应不回收(返回false)",
                result);
    }
}