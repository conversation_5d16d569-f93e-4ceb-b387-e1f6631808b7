/*
 * SecurityDetectServiceHasTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.qunhe.mdw.security.detect.service.client.api.DetectApi;
import com.qunhe.mdw.security.detect.service.common.enums.FinalDecisionEnum;
import com.qunhe.mdw.security.detect.service.common.model.DetectRequest;
import com.qunhe.mdw.security.detect.service.common.model.DetectResult;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasExceptionCountDegradeResource;
import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasRtDegradeResource;
import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
@RunWith(SpringRunner.class)
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@Import({ SecurityDetectService.class, SentinelResourceAspect.class })
public class SecurityDetectServiceHasTest {

    @MockBean
    private LongCrypt longCrypt;

    @MockBean
    private DetectApi detectApi;

    @Autowired
    private SecurityDetectService detectService;


    @Test
    public void sensitiveWordCheck_fallback_success() {
        // 构造 has 降级规则，模拟异常数降级
        setHasExceptionCountDegradeResource("sensitiveWordCheck");

        // 模拟业务代码抛出异常
        Mockito.when(detectApi.detect(any(DetectRequest.class)))
                .thenThrow(new RuntimeException("mock detectService exception"));

        // 走到fallback 逻辑，返回true
        Assert.assertTrue("fallback to true", detectService.sensitiveWordCheck(2L, "content"));

    }

    @Test
    public void sensitiveWordCheck_rt_degrade() {
        // 构造 has 降级规则，模拟rt超时降级
        setHasRtDegradeResource("sensitiveWordCheck");

        // 模拟业务代码
        DetectResult result = new DetectResult();
        result.setSuccess(true);
        result.setFinalDecision(FinalDecisionEnum.REJECT.getDecision());
        Result<DetectResult> finalResult = new Result<>();
        finalResult.setC("0");
        finalResult.setD(result);
        Mockito.when(detectApi.detect(any(DetectRequest.class))).thenReturn(finalResult);

        Assert.assertFalse("first time return false",
                detectService.sensitiveWordCheck(2L, "content"));

        // 走到rt fallback 逻辑，返回true
        Assert.assertTrue("fallback to true", detectService.sensitiveWordCheck(2L, "content"));

    }



}