/*
 * SecurityDetectService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.log.QHLogger;
import com.qunhe.mdw.security.detect.service.client.api.DetectApi;
import com.qunhe.mdw.security.detect.service.common.enums.FileTypeEnum;
import com.qunhe.mdw.security.detect.service.common.enums.FinalDecisionEnum;
import com.qunhe.mdw.security.detect.service.common.model.DetectRequest;
import com.qunhe.mdw.security.detect.service.common.model.DetectResult;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class SecurityDetectService {

    private static final QHLogger LOG = QHLogger.getLogger(SecurityDetectService.class);

    private static final String REASON_CODE = "reasonCode";

    private static final String GUID = "tool-project-service";

    @Autowired
    private DetectApi detectApi;

    @Autowired
    private LongCrypt longCrypt;

    /**
     * 敏感词检查
     *
     * @param userId
     * @param content
     * @return false表示内容包含不文明用语
     */
    @SentinelResource(value = "sensitiveWordCheck", fallback = "sensitiveWordCheckFallback")
    public boolean sensitiveWordCheck(Long userId, String content) {
        if (StringUtils.isBlank(content)) {
            return true;
        }
        DetectRequest detectRequest = new DetectRequest();
        detectRequest.setContent(content);
        detectRequest.setGuid(GUID);
        detectRequest.setType(FileTypeEnum.TEXT.getName());
        final Result<DetectResult> detectResult = detectApi.detect(detectRequest);
        if (detectResult.success() && Objects.nonNull(detectResult.getD())) {
            return !FinalDecisionEnum.REJECT.getDecision().equals(
                    detectResult.getD().getFinalDecision());
        }
        LOG.message("textDetect - call api failed")
                .with(REASON_CODE, Objects.isNull(detectResult.getD()) ? null :
                        detectResult.getD().getReasonCode())
                .with("msg", detectResult.getM())
                .warn();
        return true;
    }

    public boolean sensitiveWordCheckFallback(Long userId, String content, Throwable e) {
        LOG.message("sensitiveWordCheckFallback - fallback to true", e)
                .with("userId", userId)
                .with("content", content)
                .error();
        return true;
    }

}
