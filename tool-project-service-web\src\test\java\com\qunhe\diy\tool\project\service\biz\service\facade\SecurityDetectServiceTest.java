/*
 * SecurityDetectServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.qunhe.mdw.security.detect.service.client.api.DetectApi;
import com.qunhe.mdw.security.detect.service.common.enums.FinalDecisionEnum;
import com.qunhe.mdw.security.detect.service.common.model.DetectRequest;
import com.qunhe.mdw.security.detect.service.common.model.DetectResult;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;

@RunWith(MockitoJUnitRunner.class)
public class SecurityDetectServiceTest {

    @Mock
    private DetectApi mockDetectApi;
    @Mock
    private LongCrypt mockLongCrypt;

    @InjectMocks
    private SecurityDetectService securityDetectServiceUnderTest;

    @Test
    public void testSensitiveWordCheck_EmptyContent() {
        // Test with empty content
        assertTrue("should be true", securityDetectServiceUnderTest.sensitiveWordCheck(1L, ""));
        assertTrue("should be true", securityDetectServiceUnderTest.sensitiveWordCheck(1L, null));
        assertTrue("should be true", securityDetectServiceUnderTest.sensitiveWordCheck(1L, "   "));
    }

    @Test
    public void testSensitiveWordCheck_ApiSuccess_NoSensitiveWords() {
        // Setup
        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());
        Result<DetectResult> apiResult = new Result<>("0", "success", detectResult);
        
        when(mockDetectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Run the test
        boolean result = securityDetectServiceUnderTest.sensitiveWordCheck(1L, "normal content");

        // Verify
        assertTrue("should be true", result);
        verify(mockDetectApi).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_ApiSuccess_HasSensitiveWords() {
        // Setup
        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.REJECT.getDecision());
        detectResult.setReasonCode(100);
        Result<DetectResult> apiResult = new Result<>("0", "success", detectResult);
        
        when(mockDetectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Run the test
        boolean result = securityDetectServiceUnderTest.sensitiveWordCheck(1L, "sensitive content");

        // Verify
        assertThat(result).isFalse();
        verify(mockDetectApi).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_ApiFailure() {
        // Setup - API returns failure
        Result<DetectResult> apiResult = new Result<>("500", "API Error", null);
        when(mockDetectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Run the test
        boolean result = securityDetectServiceUnderTest.sensitiveWordCheck(1L, "test content");

        // Verify - should return true on API failure
        assertTrue("should be true", result);
        verify(mockDetectApi).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_ApiException() {
        // Setup - API throws exception

        // Run the test
        boolean result = securityDetectServiceUnderTest.sensitiveWordCheckFallback(1L, "test content", new RuntimeException("API Error"));

        // Verify - should return true on exception
        assertTrue("should be true", result);
    }

    @Test
    public void testSensitiveWordCheck_VerifyRequestParams() {
        // Setup
        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());
        Result<DetectResult> apiResult = new Result<>("0", "success", detectResult);
        
        when(mockDetectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Run the test
        securityDetectServiceUnderTest.sensitiveWordCheck(1L, "test content");

        // Verify request parameters
        verify(mockDetectApi).detect(any(DetectRequest.class));
    }
}
