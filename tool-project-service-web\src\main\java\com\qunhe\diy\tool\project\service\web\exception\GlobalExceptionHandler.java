/*
 *  GlobalExceptionHandler.java
 *  Copyright 2022 Qunhe Tech, all rights reserved.
 *  Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.exception;

import com.qunhe.diy.tool.project.service.biz.exception.NotLoggedInException;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.log.QHLogger;
import com.qunhe.web.standard.data.Result;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestControllerAdvice
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException"})
public class GlobalExceptionHandler {

    private static final QHLogger LOG = QHLogger.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = ToolProjectCreateException.class)
    public Result<?> handleToolProjectException(HttpServletRequest req,
            ToolProjectCreateException e) {
        /*
         * 系统内部异常，打印异常栈
         */
        LOG.message("global_exception")
                .with("request_uri", req.getRequestURI())
                .with("trace_id", SpanContextHolder.getTraceId())
                .with("exception", e)
                .error();
        return e.getError();
    }

    @ExceptionHandler(NotLoggedInException.class)
    @ResponseBody
    public ResponseEntity<Result<Void>> handleNotLoggedInException(
            HttpServletRequest req,
            final NotLoggedInException e, HttpServletResponse httpServletResponse) {
        final Result<Void> result = Result.error(ErrorCode.NOT_LOGIN);
        LOG.message("not login exception")
                .with("request_uri", req.getRequestURI())
                .with("trace_id", SpanContextHolder.getTraceId())
                .with("exception", e)
                .warn();
        return ResponseEntity.status(HttpServletResponse.SC_UNAUTHORIZED).body(result);
    }

    /**
     * <p>
     * 自定义 REST 业务异常
     * <p>
     *
     * @param e 异常类型
     */
    @ExceptionHandler(value = Exception.class)
    public Result<Object> handleBadRequest(HttpServletRequest req, Exception e) throws Exception {

        /*
         * 参数校验异常
         */
        if (e instanceof BindException) {
            BindingResult bindingResult = ((BindException) e).getBindingResult();
            if (null != bindingResult && bindingResult.hasErrors()) {
                return convertBindingResult(bindingResult);
            }
        }
        if (e instanceof MethodArgumentNotValidException) {
            BindingResult bindingResult = ((MethodArgumentNotValidException) e).getBindingResult();
            if (null != bindingResult && bindingResult.hasErrors()) {
                return convertBindingResult(bindingResult);
            }
        }
        //参数异常用result返回
        if (e instanceof IllegalArgumentException) {
            LOG.message("IllegalArgumentException", e).warn();
            return Result.error(ErrorCode.getErrorCodeByCode(e.getMessage()));
        }
        /*
         * 系统内部异常，打印异常栈
         */
        LOG.message("global_exception")
                .with("request_uri", req.getRequestURI())
                .with("trace_id", SpanContextHolder.getTraceId())
                .with("exception", e)
                .error();

        throw e;
    }

    private Result<Object> convertBindingResult(BindingResult bindingResult) {
        List<Object> jsonList = bindingResult.getFieldErrors().stream()
                .map(fieldError -> {
                    Map<String, Object> jsonObject = new HashMap<>(2);
                    jsonObject.put("name", fieldError.getField());
                    jsonObject.put("msg", fieldError.getDefaultMessage());
                    return jsonObject;
                }).collect(Collectors.toList());
        return Result.error(String.format("参数校验异常:[%s]", jsonList.toString()));
    }

}
