/*
 * VrcRewriteProperties.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.qunhe.diy.synergy.service.common.utils.JsonMapper;
import com.qunhe.log.QHLogger;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Component
public class VrcRewriteProperties {

    private static final QHLogger LOG = QHLogger.getLogger(VrcRewriteProperties.class);

    /**
     * 首次打开需要被修改的vrc列表
     */
    private static final String REWRITE_VRC_LIST_KEY = "kam.page.rewriteVrcList";

    private final Environment env;

    private Map<String, RewriteVrcInfo> vrcToRewriteInfoMap;

    public VrcRewriteProperties(Environment env) {
        this.env = env;
        this.vrcToRewriteInfoMap = parseRewriteVrcInfo();
    }

    private Map<String, RewriteVrcInfo> parseRewriteVrcInfo() {
        String rewriteVrcList = env.getProperty(REWRITE_VRC_LIST_KEY);
        if (StringUtils.isBlank(rewriteVrcList)) {
            LOG.message("kam.page.rewriteVrcList is empty").error();
            return Maps.newHashMap();
        }
        try {
            List<RewriteVrcInfo> vrcInfoList = JsonMapper.readValue(rewriteVrcList,
                    new TypeReference<List<RewriteVrcInfo>>() {
                    });
            return vrcInfoList.stream()
                    .collect(Collectors.toMap(RewriteVrcInfo::getVrc, v -> v, (a, b) -> a));
        } catch (Exception e) {
            // catch 异常，避免启动报错
            LOG.message("fail to parse kam.page.rewriteVrcList json", e)
                    .with("data", rewriteVrcList)
                    .error();
            return Maps.newHashMap();
        }
    }

    public RewriteVrcInfo getRewriteInfo(String vrc) {
        return vrcToRewriteInfoMap.get(vrc);
    }

    @EventListener
    public void handleContextRefreshEvent(EnvironmentChangeEvent event) {
        if (event.getKeys().contains(REWRITE_VRC_LIST_KEY)) {
            LOG.message("received rewrite vrc info list change event")
                    .with("keys", event.getKeys())
                    .info();
            this.vrcToRewriteInfoMap = parseRewriteVrcInfo();
        }
    }

    @Getter
    @Setter
    public static class RewriteVrcInfo {
        private String vrc;
        private String appId;
        private boolean needAppIdMatch = true;
    }
}
