/*
 * BatchOperationService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.config.OssConfig;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.CosDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.BatchRemoveOrRestoreService;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationTypeEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.search.common.enums.ColDict;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@SuppressWarnings("PMD.MethodTooLongRule")
public class BatchOperationService {
    private static final QHLogger LOG = QHLogger.getLogger(BatchOperationService.class);

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final GeneralProperties generalProperties;

    private final BatchOperationRecordDb batchOperationRecordDb;

    private final CosDb cosDb;

    private final BatchRemoveOrRestoreService batchRemoveOrRestoreService;

    @Autowired
    public BatchOperationService(
            final ProjectDesignFacadeDb projectDesignFacadeDb,
            final GeneralProperties generalProperties,
            final BatchOperationRecordDb batchOperationRecordDb,
            final CosDb cosDb,
            final BatchRemoveOrRestoreService batchRemoveOrRestoreService) {
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.generalProperties = generalProperties;
        this.batchOperationRecordDb = batchOperationRecordDb;
        this.cosDb = cosDb;
        this.batchRemoveOrRestoreService = batchRemoveOrRestoreService;
    }

    @Async("batchOperationExecutor")
    public void batchRemoveProjects(final String recordId, final Long tarUserId,
            final BatchOperationData batchOperationData) {

        LOG.message("batchRemoveProjects start")
                .with("recordId", recordId)
                .with("被操作人uid", tarUserId)
                .with("待处理总量", batchOperationData.getOriginCount())
                .info();

        boolean remainFlag = true;
        int batchTimes = 0;
        long start = System.currentTimeMillis();
        long totalTimeCost;
        List<Long> totalDeletedIdList = new ArrayList<>(batchOperationData.getOriginCount());
        try {
            while (remainFlag) {
                long batchStart = 0L;
                batchTimes++;
                //一次最多处理500条方案
                final List<ProjectDesign> selectTopDesignsByUserIdIgnoreDeleted =
                        projectDesignFacadeDb.searchProjectsByUserIdIgnoreDelete(0,
                                generalProperties.getSingleQuerySizeForBatchOperate(), tarUserId,
                                Collections.singletonList(ColDict.DESIGN_ID.getEsField()));
                if (CollectionUtils.isEmpty(selectTopDesignsByUserIdIgnoreDeleted)) {
                    break;
                }
                final List<Long> validDesignIds =
                        selectTopDesignsByUserIdIgnoreDeleted.stream().map(
                                        ProjectDesign::getDesignId).filter(
                                        Objects::nonNull)
                                .collect(Collectors.toList());

                batchRemoveOrRestoreService.singleBatchOperateProjects(recordId,
                        batchOperationData, validDesignIds,
                        totalDeletedIdList);

                //等待deleted字段同步到es
                Thread.sleep(generalProperties.getMaxGapTimeForBatchOperate());
                //检查是否还有剩余待处理方案(es延迟问题会导致同一批id重复执行一遍)
                final int resCount = projectDesignFacadeDb.countDesignsByUserIdIgnoreRecycle(
                        tarUserId);
                remainFlag = resCount > 0;

                //记录中间过程,埋点
                LOG.message("batch_remove_projects_dealing")
                        .with("recordid", recordId)
                        .with("tar_uid", tarUserId)
                        .with("origin_count", batchOperationData.getOriginCount())
                        .with("batch_times", batchTimes)
                        .with("current_batch_size", validDesignIds.size())
                        .with("current_batch_time_cost",
                                System.currentTimeMillis() - batchStart)
                        .with("total_time_cost_til_now", System.currentTimeMillis() - start)
                        .with("total_deal_size_til_now", totalDeletedIdList.size())
                        .with("remain_deal_size", resCount)
                        .analyze();
            }
            totalTimeCost = System.currentTimeMillis() - start;
            batchOperationData.setStatus(BatchOperationStatusEnum.DONE.getCode());
            totalDeletedIdList = totalDeletedIdList.stream().distinct().collect(
                    Collectors.toList());
            LOG.message("batchRemoveProjects circle-deal finish")
                    .with("recordid", recordId)
                    .with("target_userid", tarUserId)
                    .with("total_time_cost", totalTimeCost)
                    .with("total_delete_count", totalDeletedIdList.size())
                    .info();
        } catch (Exception e) {
            //循环时内部异常处理
            LOG.message("batchRemoveProjects error", e)
                    .with("recordId", recordId)
                    .with("tarUserId", tarUserId)
                    .error();
            batchOperationData.setStatus(BatchOperationStatusEnum.FAILED.getCode());
        }

        //将成功删除方案存入cos
        final String link = cosDb.uploadSuccessDealDesignIds(totalDeletedIdList, recordId,
                BatchOperationTypeEnum.BATCH_DELETE);
        batchOperationData.setLink(link);
        batchOperationRecordDb.updateBatchOperateFinishInfos(batchOperationData.getStatus(), link
                , totalDeletedIdList.size(), batchOperationData.getRecordId());
        LOG.message("batchRemoveProjects done")
                .with("recordid", recordId)
                .with("target_userid", tarUserId)
                .info();
    }

    @Async("batchOperationExecutor")
    public void batchRollBackProjects(final Long tarUserId, final String recordId,
            final BatchOperationData batchOperationData, final List<Long> distinctDesignIds) {

        LOG.message("batchRollBackProjects start")
                .with("recordId", recordId)
                .with("被操作人uid", tarUserId)
                .with("待处理总量", batchOperationData.getOriginCount())
                .info();

        final List<List<Long>> partitionDesignIdsByMaxSingleSize =
                Lists.partition(distinctDesignIds,
                        generalProperties.getSingleQuerySizeForBatchOperate());
        List<Long> allTotalDealIdList = new ArrayList<>(distinctDesignIds.size());
        try {
            long totalTimeCost;
            long start = System.currentTimeMillis();
            for (List<Long> onceMaxDealDesignIds : partitionDesignIdsByMaxSingleSize) {
                int batchTimes = 0;
                long batchStart = 0L;
                boolean remainFlag = true;
                final List<Long> totalDealIdList = new ArrayList<>(onceMaxDealDesignIds.size());
                while (remainFlag) {
                    batchTimes++;
                    batchRemoveOrRestoreService.singleBatchOperateProjects(recordId,
                            batchOperationData,
                            onceMaxDealDesignIds,
                            totalDealIdList);

                    //检查单次最大批量操作集合下是否还有剩余待处理方案
                    remainFlag = totalDealIdList.size() != onceMaxDealDesignIds.size();

                    //记录中间过程,埋点
                    LOG.message("batch_rollback_projects_dealing")
                            .with("recordid", recordId)
                            .with("tarUid", tarUserId)
                            .with("origin_count", distinctDesignIds.size())
                            .with("batch_times", batchTimes)
                            .with("current_batch_size", onceMaxDealDesignIds.size())
                            .with("current_batch_time_cost",
                                    System.currentTimeMillis() - batchStart)
                            .with("total_time_cost_til_now", System.currentTimeMillis() - start)
                            .with("total_deal_size_til_now", totalDealIdList.size())
                            .with("remain_deal_size",
                                    distinctDesignIds.size() - totalDealIdList.size())
                            .analyze();
                }
                allTotalDealIdList.addAll(totalDealIdList);
            }
            totalTimeCost = System.currentTimeMillis() - start;
            batchOperationData.setStatus(BatchOperationStatusEnum.DONE.getCode());
            allTotalDealIdList = allTotalDealIdList.stream().distinct().collect(
                    Collectors.toList());
            LOG.message("batchRollBackProjects circle-deal finish")
                    .with("recordid", recordId)
                    .with("target_userid", tarUserId)
                    .with("total_time_cost", totalTimeCost)
                    .with("total_rollback_count", allTotalDealIdList.size())
                    .info();
        } catch (Exception e) {
            //内部异常时，标记状态为失败
            LOG.message("batchRollBackProjects error", e)
                    .with("recordid", recordId)
                    .with("target_userid", tarUserId)
                    .error();
            batchOperationData.setStatus(BatchOperationStatusEnum.FAILED.getCode());
        }

        //将成功回滚的方案存入cos
        final String link = cosDb.uploadSuccessDealDesignIds(allTotalDealIdList, recordId,
                BatchOperationTypeEnum.BATCH_ROLLBACK);
        batchOperationRecordDb.updateBatchOperateFinishInfos(batchOperationData.getStatus(), link
                , allTotalDealIdList.size(), batchOperationData.getRecordId());
        LOG.message("batchRollBackProjects done")
                .with("recordid", recordId)
                .with("target_userid", tarUserId)
                .info();
    }

    public List<Long> getBatchDealDesignIdsFromLinks(final long tarUserId) {
        //收集批量删除请求的cos集合
        final List<String> multiLinksByUserId =
                batchOperationRecordDb.getMultiOperateLinksByUserId(tarUserId,
                        BatchOperationTypeEnum.BATCH_DELETE.getCode());
        if (CollectionUtils.isEmpty(multiLinksByUserId)) {
            LOG.message("batchRollBackProject - no valid links to roll back")
                    .with("targetUserId", tarUserId)
                    .error();
            return Collections.emptyList();
        }
        final List<String> validLinks = multiLinksByUserId.stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<Long> totalDesignIds = new ArrayList<>();
        for (String link : validLinks) {
            final String key = OssConfig.extractKeyFromCdnUrl(OssConfig.QHTBD_CDN, link);
            final List<Long> rollBackDesignIdsFromCos =
                    cosDb.getBatchDealDesignIdsFromCosNoThrow(key);
            totalDesignIds.addAll(rollBackDesignIdsFromCos);
        }
        return totalDesignIds.stream().filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取某次批量操作请求的状态
     * @param recordId
     * @return
     */
    public BatchOperationStatusEnum getBatchOperateStatus(final String recordId) {
        final BatchOperationData batchOperationRecord =
                batchOperationRecordDb.getBatchOperationRecordByRecordId(recordId);
        return batchOperationRecord == null ? BatchOperationStatusEnum.UNSTART :
                BatchOperationStatusEnum.of(batchOperationRecord.getStatus());
    }
}
