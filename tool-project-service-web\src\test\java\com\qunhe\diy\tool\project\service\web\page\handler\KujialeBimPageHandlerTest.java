/*
 * KujialeBimPageHandlerTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.designinfoservice.common.data.user.DesignOwnerStatusResult;
import com.qunhe.diy.designinfoservice.common.data.user.UserStatus;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.VrcMappingNotFoundException;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SynergyFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.diy.tool.project.service.web.context.ToolType;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diy.tool.project.service.web.page.DesignService;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.VrcRewriteService;
import com.qunhe.diy.tool.project.service.web.page.VrcService;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.FavorIcon;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.data.User;
import com.qunhe.diy.tool.project.service.web.utils.VrcUtils;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfig;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE;
import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.COMMON_CONFIG_FAVOR_ICON;
import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.OBS_SER_PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 针对 {@link KujialeBimPageHandler} 的单元测试类。
 */
@RunWith(MockitoJUnitRunner.class)
public class KujialeBimPageHandlerTest {

    private static final Long USER_ID = 123L;
    private static final Long ORDER_DESIGN_ID = 111L;
    private static final Long DESIGN_ID_LONG = 789L;
    private static final String OBS_DESIGN_ID_VALID = LongCipher.DEFAULT.encrypt(DESIGN_ID_LONG);
    private static final Long CHILD_DESIGN_ID_LONG = DESIGN_ID_LONG + 1;
    private static final String OBS_DESIGN_ID_CHILD = LongCipher.DEFAULT.encrypt(
            CHILD_DESIGN_ID_LONG);
    private static final Long MAIN_DESIGN_ID_LONG = 600L;
    private static final String OBS_MAIN_DESIGN_ID = LongCipher.DEFAULT.encrypt(
            MAIN_DESIGN_ID_LONG);

    private static final String DECRYPTED_QS_DESIGN_ID_STR = "3Fabcdefghij";
    private static final Long DECRYPTED_QS_DESIGN_ID_LONG = 12345L;

    private static final String LEVEL_ID_STR = "level_1";
    private static final String OBS_APP_ID_VALID = "validObsAppId";
    private static final String KUJIALE_HOST = "test.kujiale.com";
    private static final String KUJIALE_BIM_PATH_CONST = "/cloud/tool/h5/bim";
    private static final Long PLAN_ID_FROM_DESIGN_INFO = 999L;
    private static final String OBS_PLAN_ID_FROM_DESIGN_INFO = LongCipher.DEFAULT.encrypt(
            PLAN_ID_FROM_DESIGN_INFO);
    private static final Long PLAN_ID_FROM_FLOOR_PLAN = 888L;
    private static final String OBS_PLAN_ID_FROM_FLOOR_PLAN = LongCipher.DEFAULT.encrypt(
            PLAN_ID_FROM_FLOOR_PLAN);
    private static final String DESIGN_NAME_FROM_FLOOR_PLAN = "Floor Plan Name";

    private MockedStatic<VrcUtils> vrcUtilsMockedStatic;
    private MockedStatic<ToolTypeContextHolder> toolTypeContextHolderMockedStatic;

    @Mock
    private BusinessAccountDb mockBusinessAccountDb;
    @Mock
    private DesignService mockDesignService;
    @Mock
    private UserDb mockUserDb;
    @Mock
    private ProjectDesignFacadeDb mockProjectDesignFacadeDb;
    @Mock
    private SynergyFacade mockSynergyFacade;
    @Mock
    private VrcRewriteService mockVrcRewriteService;
    @Mock
    private SessionConflictHandler mockSessionConflictHandler;
    @Mock
    private ToadProperties mockToadProperties;
    @Mock
    private SaaSConfigService mockSaaSConfigService;
    @Mock
    private ToolLinkConfigCache mockToolLinkConfigCache;
    @Mock
    private UserInfoFacade mockUserInfoFacade;
    @Mock
    private BusinessAccessService mockBusinessAccessService;
    @Mock
    private VrcService mockVrcService;
    @Mock
    private AuthCheckService mockAuthCheckService;
    @Mock
    private HttpServletRequest mockHttpServletRequest;
    @Mock
    private H5Model mockH5Model;

    private KujialeBimPageHandler kujialeBimPageHandler;

    @Before
    public void setUp() {
        kujialeBimPageHandler = new KujialeBimPageHandler(
                mockBusinessAccountDb, mockDesignService, mockUserDb, mockProjectDesignFacadeDb,
                mockSynergyFacade, mockVrcRewriteService, mockSessionConflictHandler,
                mockToadProperties, mockSaaSConfigService, mockToolLinkConfigCache,
                mockUserInfoFacade, mockBusinessAccessService, mockVrcService, mockAuthCheckService
        );

        vrcUtilsMockedStatic = Mockito.mockStatic(VrcUtils.class);
        toolTypeContextHolderMockedStatic = Mockito.mockStatic(ToolTypeContextHolder.class);

        lenient().when(mockHttpServletRequest.getScheme()).thenReturn("http");
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                KUJIALE_HOST);
        lenient().when(mockHttpServletRequest.getParameterMap()).thenReturn(new HashMap<>());
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn("");

        lenient().when(mockBusinessAccountDb.getRootAccountForBAndC(USER_ID)).thenReturn(
                USER_ID + 1000);
        lenient().when(mockToadProperties.getHost()).thenReturn("//" + KUJIALE_HOST);
        lenient().when(mockToadProperties.getMyDesignUrl()).thenReturn(
                "http://" + KUJIALE_HOST + "/my/designs");
        lenient().when(mockToadProperties.getLoadingPageErrorRedirectUrl()).thenReturn(
                "http://" + KUJIALE_HOST + "/error-page");
    }

    @After
    public void tearDown() {
        if (vrcUtilsMockedStatic != null) {
            vrcUtilsMockedStatic.close();
        }
        if (toolTypeContextHolderMockedStatic != null) {
            toolTypeContextHolderMockedStatic.close();
        }
        ToolTypeContextHolder.clear();
    }

    private BimPageParam createValidExistingDesignParam() {
        return BimPageParam.builder()
                .userId(USER_ID)
                .obsDesignId(OBS_DESIGN_ID_VALID)
                .orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false)
                .levelId(LEVEL_ID_STR)
                .obsAppId(OBS_APP_ID_VALID)
                .build();
    }

    private BimPageParam createNewDesignParam() {
        return BimPageParam.builder()
                .userId(USER_ID)
                .obsDesignId(null)
                .obsAppId(OBS_APP_ID_VALID)
                .build();
    }

    @Test
    public void testHandleExistingDesign_AuthFailed_ShouldRedirectToDefaultPage() {
        BimPageParam param = createValidExistingDesignParam();
        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(false);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("handleExistingDesign重定向URL应为getMyDesignUrl",
                "http://" + KUJIALE_HOST + "/my/designs",
                result.getRedirectUrl());
    }

    @Test
    public void testHandleExistingDesign_IsSubDesign_ShouldRedirectToMainDesignPage() {
        BimPageParam param = BimPageParam.builder()
                .userId(USER_ID)
                .obsDesignId(OBS_DESIGN_ID_CHILD) // 使用子方案ID
                .orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false)
                .levelId(LEVEL_ID_STR)
                .obsAppId(OBS_APP_ID_VALID)
                .build();

        lenient().when(mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                        CHILD_DESIGN_ID_LONG, false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(CHILD_DESIGN_ID_LONG, USER_ID)).thenReturn(
                MAIN_DESIGN_ID_LONG);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_CHILD, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(),
                result.getStatusCode());
        String expectedRedirectUrl = "http://" + KUJIALE_HOST + KUJIALE_BIM_PATH_CONST +
                "?designid=" + OBS_MAIN_DESIGN_ID;
        assertEquals("handleExistingDesign重定向URL应指向主方案", expectedRedirectUrl,
                result.getRedirectUrl());
    }

    @Test
    public void testHandleExistingDesign_DesignInfoNotFound_ShouldReturnBadRequest() {
        BimPageParam param = createValidExistingDesignParam();
        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(DESIGN_ID_LONG, USER_ID)).thenReturn(
                DESIGN_ID_LONG);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_LONG)).thenReturn(null);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为BAD_REQUEST (400)",
                HttpStatus.BAD_REQUEST.value(), result.getStatusCode());
        assertEquals("handleExistingDesign响应体内容应为 'design not exist'", "design not exist",
                result.getBody());
    }

    @Test
    public void testHandleExistingDesign_VrcRewriteError_ShouldReturnBadRequest()
            throws VrcMappingNotFoundException {
        BimPageParam param = createValidExistingDesignParam();
        DiyDesignInfo mockDiyDesignInfo = mock(DiyDesignInfo.class);
        ProjectDesign mockProjectDesignWithVrc = mock(ProjectDesign.class);

        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(DESIGN_ID_LONG, USER_ID)).thenReturn(
                DESIGN_ID_LONG);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_LONG)).thenReturn(
                mockDiyDesignInfo);
        lenient().when(mockDiyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_LONG);
        lenient().when(mockDiyDesignInfo.getPlanId()).thenReturn(PLAN_ID_FROM_DESIGN_INFO);
        lenient().when(mockProjectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_LONG)).thenReturn(
                mockProjectDesignWithVrc);
        lenient().when(mockProjectDesignWithVrc.getVrc()).thenReturn("vrc1");

        lenient().when(mockVrcRewriteService.correctVrc("vrc1", PLAN_ID_FROM_DESIGN_INFO, param))
                .thenThrow(new VrcMappingNotFoundException(PLAN_ID_FROM_DESIGN_INFO, "vrc1",
                        param.getObsAppId()));

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为BAD_REQUEST (400)",
                HttpStatus.BAD_REQUEST.value(), result.getStatusCode());
        assertEquals("handleExistingDesign响应体内容应为 'update vrc error'", "update vrc error",
                result.getBody());
    }

    @Test
    public void testHandleExistingDesign_InvalidVrcCoworker_ShouldRedirectToSynergyErrorPage()
            throws VrcMappingNotFoundException {
        BimPageParam param = createValidExistingDesignParam();
        DiyDesignInfo mockDiyDesignInfo = mock(
                DiyDesignInfo.class); 
        ProjectDesign mockProjectDesignWithVrc = mock(ProjectDesign.class);

        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(DESIGN_ID_LONG, USER_ID)).thenReturn(
                DESIGN_ID_LONG);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_LONG)).thenReturn(
                mockDiyDesignInfo);
        lenient().when(mockDiyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_LONG);
        lenient().when(mockDiyDesignInfo.getPlanId()).thenReturn(PLAN_ID_FROM_DESIGN_INFO);
        lenient().when(mockDiyDesignInfo.getUserId()).thenReturn(
                USER_ID + 1); // Owner is not current user

        lenient().when(mockProjectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_LONG)).thenReturn(
                mockProjectDesignWithVrc);
        lenient().when(mockProjectDesignWithVrc.getVrc()).thenReturn("vrc1");
        lenient().when(mockVrcRewriteService.correctVrc("vrc1", PLAN_ID_FROM_DESIGN_INFO, param))
                .thenReturn("vrc_corrected");

        lenient().when(mockSynergyFacade.isSynergyDesign(DESIGN_ID_LONG, USER_ID)).thenReturn(true);
        vrcUtilsMockedStatic.when(() -> VrcUtils.isYunDesign(mockDiyDesignInfo))
                .thenReturn(true);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(),
                result.getStatusCode());
        String expectedErrorUrl =
                "http://" + KUJIALE_HOST + "/error-page?errorcode=YUN_DESIGN_NOT_SUPPORT";
        assertEquals("handleExistingDesign重定向URL应为协同错误页", expectedErrorUrl,
                result.getRedirectUrl());
    }

    @Test
    public void testHandleExistingDesign_SessionConflict_ShouldRedirectToConflictUri()
            throws VrcMappingNotFoundException {
        BimPageParam param = createValidExistingDesignParam();
        DiyDesignInfo mockDiyDesignInfo = mock(DiyDesignInfo.class);
        ProjectDesign mockProjectDesignWithVrc = mock(ProjectDesign.class);
        URI conflictUri = URI.create("http://conflict.uri/resolve");

        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(DESIGN_ID_LONG, USER_ID)).thenReturn(
                DESIGN_ID_LONG);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_LONG)).thenReturn(
                mockDiyDesignInfo);
        lenient().when(mockDiyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_LONG);
        lenient().when(mockDiyDesignInfo.getPlanId()).thenReturn(PLAN_ID_FROM_DESIGN_INFO);
        lenient().when(mockDiyDesignInfo.getUserId()).thenReturn(USER_ID); // Owner is current user
        lenient().when(mockProjectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_LONG)).thenReturn(
                mockProjectDesignWithVrc);
        lenient().when(mockProjectDesignWithVrc.getVrc()).thenReturn("vrc1");
        lenient().when(mockVrcRewriteService.correctVrc("vrc1", PLAN_ID_FROM_DESIGN_INFO, param))
                .thenReturn("vrc_corrected");
        lenient().when(mockSynergyFacade.isSynergyDesign(DESIGN_ID_LONG, USER_ID)).thenReturn(
                false);

        toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType)
                .thenReturn(ToolType.FOP_ORDER);
        lenient().when(
                        mockSessionConflictHandler.checkSessionConflict(DESIGN_ID_LONG, USER_ID,
                                true))
                .thenReturn(conflictUri);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign返回的result不应为null", result);
        assertEquals("handleExistingDesign返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("handleExistingDesign重定向URL应为冲突解决URI", conflictUri.toString(),
                result.getRedirectUrl());
    }

    @Test
    public void testHandleExistingDesign_AllChecksPass_ShouldReturnNull() throws
            VrcMappingNotFoundException {
        BimPageParam param = createValidExistingDesignParam();
        DiyDesignInfo mockDiyDesignInfo = mock(DiyDesignInfo.class);
        ProjectDesign mockProjectDesignWithVrc = mock(ProjectDesign.class);

        lenient().when(
                        mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                                DESIGN_ID_LONG,
                                false))
                .thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(DESIGN_ID_LONG, USER_ID)).thenReturn(
                DESIGN_ID_LONG);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DESIGN_ID_LONG)).thenReturn(
                mockDiyDesignInfo);
        lenient().when(mockDiyDesignInfo.getDesignId()).thenReturn(DESIGN_ID_LONG);
        lenient().when(mockDiyDesignInfo.getPlanId()).thenReturn(PLAN_ID_FROM_DESIGN_INFO);
        lenient().when(mockDiyDesignInfo.getUserId()).thenReturn(USER_ID);
        lenient().when(mockProjectDesignFacadeDb.getVrcAndParentId(DESIGN_ID_LONG)).thenReturn(
                mockProjectDesignWithVrc);
        lenient().when(mockProjectDesignWithVrc.getVrc()).thenReturn("vrc1");
        lenient().when(mockProjectDesignWithVrc.getCopyLogParentId()).thenReturn(null);
        lenient().when(mockVrcRewriteService.correctVrc("vrc1", PLAN_ID_FROM_DESIGN_INFO, param))
                .thenReturn("vrc_corrected");
        lenient().when(mockSynergyFacade.isSynergyDesign(DESIGN_ID_LONG, USER_ID)).thenReturn(
                false);
        toolTypeContextHolderMockedStatic.when(ToolTypeContextHolder::getToolType)
                .thenReturn(ToolType.YUN_DESIGN); // Using a valid ToolType
        lenient().when(
                        mockSessionConflictHandler.checkSessionConflict(DESIGN_ID_LONG, USER_ID,
                                false))
                .thenReturn(null);

        ToolPageResult response = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_VALID, LEVEL_ID_STR,
                mockH5Model);

        assertNull("所有校验通过时handleExistingDesign应返回null", response);
        verify(mockH5Model).setVrc("vrc_corrected");
        verify(mockH5Model).setDesignInfo(
                any(com.qunhe.diy.tool.project.service.web.page.data.DesignInfo.class));
    }

    @Test
    public void testHandleNewDesign_NoBimAccess_ShouldRedirectToDefaultPage() {
        BimPageParam param = createNewDesignParam();
        Long rootAccountId = USER_ID + 1000;
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, rootAccountId);

        assertNotNull("handleNewDesign返回的result不应为null", result);
        assertEquals("handleNewDesign返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("handleNewDesign重定向URL应为getMyDesignUrl",
                "http://" + KUJIALE_HOST + "/my/designs",
                result.getRedirectUrl());
    }


    @Test
    public void testRedirectMultiDesignPage_BasicRedirect() {
        BimPageParam param = BimPageParam.builder()
                .userId(USER_ID).obsDesignId(OBS_DESIGN_ID_CHILD).orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false).levelId(LEVEL_ID_STR).obsAppId(OBS_APP_ID_VALID).build();
        lenient().when(mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                CHILD_DESIGN_ID_LONG, false)).thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(CHILD_DESIGN_ID_LONG, USER_ID)).thenReturn(
                MAIN_DESIGN_ID_LONG);
        Map<String, String[]> currentParams = new HashMap<>();
        currentParams.put("otherParam", new String[]{ "value" });
        lenient().when(mockHttpServletRequest.getParameterMap()).thenReturn(currentParams);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_CHILD, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign多方案跳转返回的result不应为null", result);
        assertEquals("handleExistingDesign多方案跳转返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        String expectedUrl =
                "http://" + KUJIALE_HOST + KUJIALE_BIM_PATH_CONST + "?designid=" +
                        OBS_MAIN_DESIGN_ID + "&otherParam=value";
        assertEquals("handleExistingDesign多方案跳转重定向URL应正确",
                expectedUrl,
                result.getRedirectUrl());
    }

    @Test
    public void testRedirectMultiDesignPage_WithPlanInfoFromDb() {
        BimPageParam param = BimPageParam.builder()
                .userId(USER_ID).obsDesignId(OBS_DESIGN_ID_CHILD).orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false).levelId(LEVEL_ID_STR).obsAppId(OBS_APP_ID_VALID).build();
        lenient().when(mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                CHILD_DESIGN_ID_LONG, false)).thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(CHILD_DESIGN_ID_LONG, USER_ID)).thenReturn(
                MAIN_DESIGN_ID_LONG);

        Map<String, String[]> paramsWithPlanTrigger = new HashMap<>();
        paramsWithPlanTrigger.put(OBS_SER_PLAN_ID, new String[]{ "trigger" });
        lenient().when(mockHttpServletRequest.getParameterMap()).thenReturn(paramsWithPlanTrigger);

        ProjectDesign mockFloorPlan = mock(ProjectDesign.class);
        lenient().when(mockFloorPlan.getPlanId()).thenReturn(PLAN_ID_FROM_FLOOR_PLAN);
        lenient().when(mockFloorPlan.getDesignName()).thenReturn(DESIGN_NAME_FROM_FLOOR_PLAN);
        lenient().when(mockProjectDesignFacadeDb.getProjectByDesignId(MAIN_DESIGN_ID_LONG))
                .thenReturn(mockFloorPlan);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_CHILD, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign多方案跳转带plan信息返回的result不应为null", result);
        assertEquals("handleExistingDesign多方案跳转带plan信息返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        String expectedUrl = "http://" + KUJIALE_HOST + KUJIALE_BIM_PATH_CONST +
                "?designid=" + OBS_MAIN_DESIGN_ID +
                "&" + OBS_SER_PLAN_ID + "=" + OBS_PLAN_ID_FROM_FLOOR_PLAN +
                "&" + PLAN_NAME + "=" + DESIGN_NAME_FROM_FLOOR_PLAN.replace(" ", "%20");
        assertEquals("handleExistingDesign多方案跳转带plan信息重定向URL应正确",
                expectedUrl, result.getRedirectUrl());
    }

    @Test
    public void testRedirectMultiDesignPage_PlanInfoTriggeredButDbReturnsNull() {
        BimPageParam param = BimPageParam.builder()
                .userId(USER_ID).obsDesignId(OBS_DESIGN_ID_CHILD).orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false).levelId(LEVEL_ID_STR).obsAppId(OBS_APP_ID_VALID).build();
        lenient().when(mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                CHILD_DESIGN_ID_LONG, false)).thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(CHILD_DESIGN_ID_LONG, USER_ID)).thenReturn(
                MAIN_DESIGN_ID_LONG);
        Map<String, String[]> paramsWithPlanTrigger = new HashMap<>();
        paramsWithPlanTrigger.put(OBS_SER_PLAN_ID, new String[]{ "trigger" });
        lenient().when(mockHttpServletRequest.getParameterMap()).thenReturn(paramsWithPlanTrigger);
        lenient().when(mockProjectDesignFacadeDb.getProjectByDesignId(MAIN_DESIGN_ID_LONG))
                .thenReturn(null);

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_CHILD, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign多方案跳转plan触发但DB无数据返回的result不应为null",
                result);
        assertEquals("handleExistingDesign多方案跳转plan触发但DB无数据返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        String expectedUrl =
                "http://" + KUJIALE_HOST + KUJIALE_BIM_PATH_CONST + "?designid=" +
                        OBS_MAIN_DESIGN_ID;
        assertEquals("handleExistingDesign多方案跳转plan触发但DB无数据重定向URL应正确",
                expectedUrl, result.getRedirectUrl());
    }

    @Test
    public void testRedirectMultiDesignPage_UriSyntaxException_ShouldReturnInternalServerError() {
        BimPageParam param = BimPageParam.builder()
                .userId(USER_ID).obsDesignId(OBS_DESIGN_ID_CHILD).orderDesignId(ORDER_DESIGN_ID)
                .cooperate(false).levelId(LEVEL_ID_STR).obsAppId(OBS_APP_ID_VALID).build();
        lenient().when(mockAuthCheckService.checkAuthFromDesignId(USER_ID, ORDER_DESIGN_ID,
                CHILD_DESIGN_ID_LONG, false)).thenReturn(true);
        lenient().when(mockDesignService.getMainDesignId(CHILD_DESIGN_ID_LONG, USER_ID)).thenReturn(
                MAIN_DESIGN_ID_LONG);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                "invalid host with spaces"); // This will cause URI Syntax Exception in redirect logic

        ToolPageResult result = kujialeBimPageHandler.handleExistingDesign(
                mockHttpServletRequest, param, USER_ID, OBS_DESIGN_ID_CHILD, LEVEL_ID_STR,
                mockH5Model);

        assertNotNull("handleExistingDesign多方案跳转UriSyntaxException返回的result不应为null",
                result);
        assertEquals(
                "handleExistingDesign多方案跳转UriSyntaxException返回的状态码应为INTERNAL_SERVER_ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                result.getStatusCode());
        assertEquals("handleExistingDesign多方案跳转UriSyntaxException响应体应为'redirect error'",
                "redirect error", result.getBody());
    }

    @Test
    public void testRedirectDefaultPage_HostContainsKuSpace() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        String kuSpaceHost = "test.kukongjian.com";
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                kuSpaceHost);
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(null);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull("handleNewDesign跳转KuSpace域名返回的result不应为null", result);
        assertEquals("handleNewDesign跳转KuSpace域名返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转KuSpace域名重定向URL应正确",
                "//" + kuSpaceHost, result.getRedirectUrl());
    }

    @Test
    public void testRedirectDefaultPage_HostNotNull_NoKuSpace() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        String normalHost = "another.kujiale.com";
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                normalHost);
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(null);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull("handleNewDesign跳转非KuSpace域名返回的result不应为null", result);
        assertEquals("handleNewDesign跳转非KuSpace域名返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转非KuSpace域名重定向URL应正确",
                mockToadProperties.getMyDesignUrl(),
                result.getRedirectUrl());
    }

    @Test
    public void testRedirectDefaultPage_HostNull_QueryStringNull() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                null);
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(null);
        String myDesignUrl = "http://default.com/mydesigns";
        lenient().when(mockToadProperties.getMyDesignUrl()).thenReturn(myDesignUrl);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull("handleNewDesign跳转host为null且queryString为null返回的result不应为null",
                result);
        assertEquals("handleNewDesign跳转host为null且queryString为null返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转host为null且queryString为null重定向URL应正确",
                myDesignUrl, result.getRedirectUrl());
    }

    @Test
    public void testRedirectDefaultPage_QueryStringHasDesignId_DesignInfoFound() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                null);
        String queryStringWithDesign =
                "param=val&designid=" + DECRYPTED_QS_DESIGN_ID_STR + "&another=true";
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(queryStringWithDesign);

        DiyDesignInfo mockDesignInfo = mock(DiyDesignInfo.class);
        lenient().when(mockDesignInfo.getPlanId()).thenReturn(PLAN_ID_FROM_DESIGN_INFO);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DECRYPTED_QS_DESIGN_ID_LONG))
                .thenReturn(mockDesignInfo);
        String toadHost = "//toad.kujiale.com";
        lenient().when(mockToadProperties.getHost()).thenReturn(toadHost);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull(
                "handleNewDesign跳转queryString含designid且能查到DesignInfo返回的result不应为null",
                result);
        assertEquals(
                "handleNewDesign跳转queryString含designid且能查到DesignInfo返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转queryString含designid且能查到DesignInfo重定向URL应正确",
                mockToadProperties.getMyDesignUrl(),
                result.getRedirectUrl());
    }

    @Test
    public void testRedirectDefaultPage_QueryStringHasDesignId_DesignInfoNotFound() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                null);
        String queryStringWithDesign = "designid=" + DECRYPTED_QS_DESIGN_ID_STR;
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(queryStringWithDesign);
        lenient().when(mockProjectDesignFacadeDb.getDiyDesignInfo(DECRYPTED_QS_DESIGN_ID_LONG))
                .thenReturn(null);
        String myDesignUrl = "http://default.com/mydesigns";
        lenient().when(mockToadProperties.getMyDesignUrl()).thenReturn(myDesignUrl);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull(
                "handleNewDesign跳转queryString含designid但查不到DesignInfo返回的result不应为null",
                result);
        assertEquals(
                "handleNewDesign跳转queryString含designid但查不到DesignInfo返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转queryString含designid但查不到DesignInfo重定向URL应正确",
                myDesignUrl, result.getRedirectUrl());
    }

    @Test
    public void testRedirectDefaultPage_QueryStringHasMalformedDesignId_NoMatch() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                null);
        String queryStringNoMatch = "someparam=novalue"; // Pattern will not find "designid="
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(queryStringNoMatch);
        String myDesignUrl = "http://default.com/mydesigns";
        lenient().when(mockToadProperties.getMyDesignUrl()).thenReturn(myDesignUrl);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull("handleNewDesign跳转queryString不含designid返回的result不应为null",
                result);
        assertEquals("handleNewDesign跳转queryString不含designid返回的状态码应为FOUND (302)",
                HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("handleNewDesign跳转queryString不含designid重定向URL应正确",
                myDesignUrl, result.getRedirectUrl());
    }

    @Test
    public void testSendRedirect_UriSyntaxException_ShouldReturnInternalServerError() {
        BimPageParam param = createNewDesignParam();
        lenient().when(mockBusinessAccessService.checkAccess(eq(USER_ID), anyLong())).thenReturn(
                false);
        lenient().when(mockHttpServletRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(
                null);
        lenient().when(mockHttpServletRequest.getQueryString()).thenReturn(null);
        String invalidUrl = "http://invalid domain with spaces.com/mydesigns";
        lenient().when(mockToadProperties.getMyDesignUrl()).thenReturn(invalidUrl);

        ToolPageResult result = kujialeBimPageHandler.handleNewDesign(mockHttpServletRequest,
                param, USER_ID, USER_ID + 1000);

        assertNotNull("handleNewDesign跳转UriSyntaxException返回的result不应为null", result);
        assertEquals("handleNewDesign跳转UriSyntaxException返回的状态码应为INTERNAL_SERVER_ERROR",
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                result.getStatusCode());
        assertEquals("handleNewDesign跳转UriSyntaxException响应体应为'redirect error'",
                "redirect error", result.getBody());
    }

    @Test
    public void testGetRootAccountId_正常获取根账户ID() {
        Long testUserId = 12345L;
        Long expectedRootAccountId = 67890L;
        lenient().when(mockBusinessAccountDb.getRootAccountForBAndC(testUserId))
                .thenReturn(expectedRootAccountId);
        Long actualRootAccountId = kujialeBimPageHandler.getRootAccountId(testUserId);
        assertEquals("根账户ID应该与预期值匹配", expectedRootAccountId, actualRootAccountId);
    }

    @Test
    public void testGetRootAccountId_异常处理() {
        Long testUserId = 12345L;
        lenient().when(mockBusinessAccountDb.getRootAccountForBAndC(testUserId))
                .thenThrow(new RuntimeException("模拟数据库异常"));
        Long actualRootAccountId = kujialeBimPageHandler.getRootAccountId(testUserId);
        assertNull("发生异常时应返回null", actualRootAccountId);
    }

    @Test
    public void testCheckDesignOwnerStatus_所有者状态正常() {
        String obsDesignId = LongCipher.DEFAULT.encrypt(123L);
        DesignOwnerStatusResult mockResult = mock(DesignOwnerStatusResult.class);
        lenient().when(mockResult.getStatus()).thenReturn(UserStatus.NORMAL);
        lenient().when(mockSynergyFacade.getDesignOwnerStatus(123L)).thenReturn(mockResult);
        ToolPageResult result = kujialeBimPageHandler.checkDesignOwnerStatus(obsDesignId);
        assertNull("所有者状态正常时应返回null", result);
    }

    @Test
    public void testCheckDesignOwnerStatus_所有者状态异常() {
        String obsDesignId = LongCipher.DEFAULT.encrypt(123L);
        String redirectUrl = "http://redirect.url";
        DesignOwnerStatusResult mockResult = mock(DesignOwnerStatusResult.class);
        lenient().when(mockResult.getStatus()).thenReturn(UserStatus.DISABLED);
        lenient().when(mockResult.getRedirectUrl()).thenReturn(redirectUrl);
        lenient().when(mockSynergyFacade.getDesignOwnerStatus(123L)).thenReturn(mockResult);
        ToolPageResult result = kujialeBimPageHandler.checkDesignOwnerStatus(obsDesignId);
        assertNotNull("所有者状态异常时应返回响应对象", result);
        assertEquals("应返回302重定向状态码", HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("重定向URL应与预期匹配", redirectUrl,
                result.getRedirectUrl());
    }

    @Test
    public void testCheckDesignOwnerStatus_发生异常() {
        String obsDesignId = LongCipher.DEFAULT.encrypt(123L);
        lenient().when(mockSynergyFacade.getDesignOwnerStatus(123L))
                .thenThrow(new RuntimeException("模拟服务异常"));
        ToolPageResult result = kujialeBimPageHandler.checkDesignOwnerStatus(obsDesignId);
        assertNull("发生异常时应返回null", result);
    }

    @Test
    public void testGetDefaultLevelId_使用上次打开的层级ID() throws Exception {
        YunDesign mockYunDesign = mock(YunDesign.class);
        Long planId = 123L;
        String lastOpenedLevelId = "last_level_1";
        lenient().when(mockYunDesign.getPlanId()).thenReturn(planId);
        lenient().when(mockDesignService.getLastOpenedLevelIdByPlanId(planId))
                .thenReturn(lastOpenedLevelId);
        lenient().when(mockDesignService.isValidLevelId(anyLong(), eq(lastOpenedLevelId)))
                .thenReturn(true);
        String actualLevelId = kujialeBimPageHandler.getDefaultLevelId(mockYunDesign);
        assertEquals("应返回上次打开的有效层级ID", lastOpenedLevelId, actualLevelId);
    }

    @Test
    public void testGetDefaultLevelId_new_levelid() throws Exception {
        YunDesign mockYunDesign = mock(YunDesign.class);
        Long planId = 123L;
        String generatedLevelId = "generated_level_1";
        lenient().when(mockYunDesign.getPlanId()).thenReturn(planId);
        lenient().when(mockDesignService.getLastOpenedLevelIdByPlanId(planId))
                .thenReturn(null);
        lenient().when(mockYunDesign.generateLevelId()).thenReturn(generatedLevelId);
        String actualLevelId = kujialeBimPageHandler.getDefaultLevelId(mockYunDesign);
        assertEquals("无有效的上次层级ID时应返回新生成的层级ID", generatedLevelId, actualLevelId);
    }

    @Test
    public void testCheckAppAccess_无AppID时检查BIM访问权限() {
        Long userId = 123L;
        lenient().when(mockBusinessAccessService.checkBimAccess(userId)).thenReturn(true);
        boolean hasAccess = kujialeBimPageHandler.checkAppAccess(null, userId);
        assertTrue("用户应具有BIM访问权限", hasAccess);
    }

    @Test
    public void testCheckAppAccess_有效AppID且无需权限点校验() {
        Long userId = 123L;
        String obsAppId = LongCipher.DEFAULT.encrypt(456L);
        VrcAppIdData mockVrcAppIdData = mock(VrcAppIdData.class);
        lenient().when(mockVrcService.getByVTypeAndAppId("V0150", 456L))
                .thenReturn(mockVrcAppIdData);
        lenient().when(mockVrcService.skipAPCheck(mockVrcAppIdData)).thenReturn(true);
        boolean hasAccess = kujialeBimPageHandler.checkAppAccess(obsAppId, userId);
        assertTrue("无需权限点校验时应直接允许访问", hasAccess);
    }

    @Test
    public void testCheckAppAccess_有效AppID需要权限点校验() {
        Long userId = 123L;
        String obsAppId = LongCipher.DEFAULT.encrypt(456L);
        VrcAppIdData mockVrcAppIdData = mock(VrcAppIdData.class);
        lenient().when(mockVrcService.getByVTypeAndAppId("V0150", 456L))
                .thenReturn(mockVrcAppIdData);
        lenient().when(mockVrcService.skipAPCheck(mockVrcAppIdData)).thenReturn(false);
        lenient().when(mockVrcAppIdData.getAccessPoint()).thenReturn(789);
        lenient().when(mockBusinessAccessService.checkAccess(userId, 789L)).thenReturn(true);
        boolean hasAccess = kujialeBimPageHandler.checkAppAccess(obsAppId, userId);
        assertTrue("权限点校验通过时应允许访问", hasAccess);
    }

    @Test
    public void testCheckAppAccess_无效AppID() {
        Long userId = 123L;
        String obsAppId = LongCipher.DEFAULT.encrypt(456L);
        lenient().when(mockVrcService.getByVTypeAndAppId("V0150", 456L))
                .thenReturn(null);
        boolean hasAccess = kujialeBimPageHandler.checkAppAccess(obsAppId, userId);
        assertFalse("无效AppID时应拒绝访问", hasAccess);
    }

    @Test
    public void testFillBasicInfo_基本信息填充() {
        String redirectUrl = "http://test.url";
        Integer stage = 1;
        Long userId = 123L;
        H5Model h5Model = spy(new H5Model());
        kujialeBimPageHandler.fillBasicInfo(mockHttpServletRequest, redirectUrl, stage, userId,
                h5Model);
        verify(h5Model).setStage(stage);
        verify(h5Model).setProjectStage(any());
        verify(h5Model).setRedirectUrl(redirectUrl);
        verify(h5Model).setVrcConfig(any());
        verify(h5Model).setAbTestConfig(any());
    }

    @Test
    public void testFillParametricWallInfo_参数化墙面信息填充() throws Exception {
        Long userId = 123L;
        Long rootAccountId = 456L;
        Long accountId = 789L;
        H5Model h5Model = spy(new H5Model());
        kujialeBimPageHandler.fillParametricWallInfo(userId, rootAccountId, accountId, h5Model);
        verify(h5Model).setParametricWallPanelAccess(any());
        verify(h5Model).setParametricWallPanelPrivateLibAccess(any());
    }

    @Test
    public void testFillAppInfo_应用信息填充() {
        H5Model h5Model = spy(new H5Model());
        kujialeBimPageHandler.fillAppInfo(h5Model);
        verify(h5Model).setAppInfo(any());
    }

    @Test
    public void testFillUserInfo_用户信息填充() {
        Long userId = 123L;
        H5Model h5Model = new H5Model();
        UserDto mockUserDto = mock(UserDto.class);
        lenient().when(mockUserDto.getScore()).thenReturn(100L);
        lenient().when(mockUserDb.getUserBySession()).thenReturn(mockUserDto);
        lenient().when(mockUserInfoFacade.getUserInfoTags(userId)).thenReturn(Collections.singleton(10L));
        lenient().when(mockUserInfoFacade.getUserNameByUserId(userId)).thenReturn("testName");
        kujialeBimPageHandler.fillUserInfo(userId, h5Model);
        User user = h5Model.getUser();
        assertNotNull("用户对象不应为null", user);
        assertEquals("加密的用户ID应匹配", LongCipher.DEFAULT.encrypt(userId), user.getObsUserId());
        assertTrue("用户标签应包含预期值", user.getUserTags().contains(10L));
    }

    @Test
    public void testFillToolJsConfig_工具JS配置填充() throws Exception {
        H5Model h5Model = spy(new H5Model());
        BusinessConfig mockBusinessConfig = mock(BusinessConfig.class);
        lenient().when(mockToolLinkConfigCache.getDefaultConfig()).thenReturn(mockBusinessConfig);
        kujialeBimPageHandler.fillToolJsConfig(h5Model);
        verify(h5Model).setToolJsConfigJson(any());
    }

    @Test
    public void testFillBusinessFavorIcon_商家图标填充() {
        Long userId = 123L;
        H5Model h5Model = new H5Model();
        BusinessConfigMap mockBusinessConfigMap = mock(BusinessConfigMap.class);
        BusinessConfigElement mockElement = mock(BusinessConfigElement.class);
        when(mockSaaSConfigService.getBusinessConfigMap(userId, COMMON_CONFIG_FAVOR_ICON)).thenReturn(mockBusinessConfigMap);
        when(mockBusinessConfigMap.isNotEmpty()).thenReturn(true);
        when(mockBusinessConfigMap.get(COMMON_CONFIG_FAVOR_ICON)).thenReturn(mockElement);
        when(mockElement.getShow()).thenReturn(true);
        when(mockElement.getPicUrl()).thenReturn("http://example.com/icon.png");
        kujialeBimPageHandler.fillBusinessFavorIcon(userId, h5Model);
        FavorIcon favorIcon = h5Model.getFavorIcon();
        assertNotNull("商家图标不应为null", favorIcon);
        assertEquals("图片URL应匹配", "http://example.com/icon.png", favorIcon.getPicUrl());
    }

    @Test
    public void testFillAccountInfo_账户信息填充() {
        Long userId = 123L;
        Long rootAccountId = 456L;
        H5Model h5Model = spy(new H5Model());
        BusinessConfigElement element = new BusinessConfigElement();
        element.setStatus(1);
        BusinessConfigMap businessConfigMap = new BusinessConfigMap();
        businessConfigMap.put(ALL_TOOL_CONFIG_LANGUAGE_TYPE, element);
        when(mockSaaSConfigService.getBusinessConfigMap(userId, ALL_TOOL_CONFIG_LANGUAGE_TYPE)).thenReturn(businessConfigMap);
        kujialeBimPageHandler.fillAccountInfo(userId, rootAccountId, h5Model);
        verify(h5Model).setRootAccount(any());
    }

    @Test
    public void testFillPlanInfo_方案信息填充() {
        String obsSrcPlanId = "srcPlanId";
        String planName = "测试方案";
        String askId = "askId";
        Byte planType = 1;
        H5Model h5Model = spy(new H5Model());
        kujialeBimPageHandler.fillPlanInfo(obsSrcPlanId, planName, askId, planType, h5Model);
        verify(h5Model).setPlanInfo(any());
    }

    @Test
    public void testFillCustomerServiceSetting_客服设置填充() throws Exception {
        H5Model h5Model = spy(new H5Model());
        kujialeBimPageHandler.fillCustomerServiceSetting(h5Model);
        verify(h5Model).setCustomerServiceJson(any());
    }
}