/*
 * H5Model.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import com.google.common.collect.Maps;
import com.qunhe.diybe.module.toolversion.data.H5AppVer;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class H5Model {
    private String redirectUrl;
    private User user;
    private String userContactName;
    private H5AppVer appInfo;
    private FavorIcon favorIcon;
    private RootAccount rootAccount;
    private String projectStage;
    private DesignInfo designInfo;
    private PlanInfo planInfo;
    private String toolJsConfigJson;
    private String customerServiceJson;
    private Integer stage;
    private Map<String, String> abTestConfig = Maps.newHashMap();
    private String parametricWallPanelAccess;
    private String parametricWallPanelPrivateLibAccess;
    private String vrc;
    private String vrcConfig;
    private String dwDrawingVersion;
    private String defaultDrawingVersion;

    private boolean bimAccess;
    private Boolean stayDiy;
    private String obsOwnerUserId;
    private Boolean isYunTuUser = false;
    private String diyVersionUrl;
    private String yunDesignVersionUrl;
    private Boolean redirectDecoration;
    private Float weakNetworkAlertThreshold;
}
