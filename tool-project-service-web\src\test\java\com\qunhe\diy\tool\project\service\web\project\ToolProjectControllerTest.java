/*
 * ToolProjectControllerTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.common.data.DesignSearchableStatus;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.request.ToolProjectSaveRequest;
import com.qunhe.instdeco.diy.apiencrypt.data.EncryptData;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ToolProjectControllerTest {

    @InjectMocks
    private ToolProjectController toolProjectController;

    @Mock
    private ToolProjectService toolProjectService;

    @Mock
    private ToadProperties toadProperties;

    private ObjectMapper objectMapper;
    private MockHttpServletRequest request;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();
        request = new MockHttpServletRequest();
    }

    @Test
    public void testCreateProject_Success() throws Exception {
        // Setup
        ToolProjectSaveRequest saveRequest = new ToolProjectSaveRequest();
        saveRequest.setName("Test Design");

        String requestJson = objectMapper.writeValueAsString(saveRequest);
        EncryptData encryptData = new EncryptData(requestJson);

        ToolProjectHomeDesign mockResult = new ToolProjectHomeDesign();
        mockResult.setDesignId(1001L);
        
        when(toadProperties.isUserRangeCheck()).thenReturn(false);
        when(toolProjectService.create(any(ToolProjectSaveParam.class), anyBoolean()))
                .thenReturn(mockResult);
        
        // Execute
        EncryptData result = toolProjectController.createProject("zh_CN", encryptData, request);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertNotNull("Result body should not be null", result.getBody());
        
        // Parse the result to verify it's a success response
        Result<?> parsedResult = objectMapper.readValue(result.getBody(), Result.class);
        assertTrue("Should be success", parsedResult.success());
        
        verify(toolProjectService, times(1)).create(any(ToolProjectSaveParam.class), anyBoolean());
    }

    @Test
    public void testCreateProject_WithCoohomRequest() throws Exception {
        // Setup
        request.addHeader("User-Agent", "coohom");
        
        ToolProjectSaveRequest saveRequest = new ToolProjectSaveRequest();
        saveRequest.setName("Test Design");
        
        String requestJson = objectMapper.writeValueAsString(saveRequest);
        EncryptData encryptData = new EncryptData(requestJson);
        
        ToolProjectHomeDesign mockResult = new ToolProjectHomeDesign();
        mockResult.setDesignId(1001L);
        
        when(toadProperties.isUserRangeCheck()).thenReturn(false);
        when(toolProjectService.create(any(ToolProjectSaveParam.class), eq(true)))
                .thenReturn(mockResult);
        
        // Execute
        EncryptData result = toolProjectController.createProject("zh_CN", encryptData, request);
        
        // Verify
        assertNotNull("Result should not be null", result);
        verify(toolProjectService, times(1)).create(any(ToolProjectSaveParam.class), eq(true));
    }

    @Test
    public void testCreateProject_CreateException() throws Exception {
        // Setup
        ToolProjectSaveRequest saveRequest = new ToolProjectSaveRequest();
        saveRequest.setName("Test Design");
        
        String requestJson = objectMapper.writeValueAsString(saveRequest);
        EncryptData encryptData = new EncryptData(requestJson);
        
        when(toadProperties.isUserRangeCheck()).thenReturn(false);
        when(toolProjectService.create(any(ToolProjectSaveParam.class), anyBoolean()))
                .thenThrow(new ToolProjectCreateException(ErrorCode.CREATE_PROJECT_COUNT_ERROR));
        
        // Execute
        EncryptData result = toolProjectController.createProject("zh_CN", encryptData, request);
        
        // Verify
        assertNotNull("Result should not be null", result);
        
        Result<?> parsedResult = objectMapper.readValue(result.getBody(), Result.class);
        assertFalse("Should be error", parsedResult.success());
        assertEquals("Error code should match", 
                ErrorCode.CREATE_PROJECT_COUNT_ERROR.getCode(), parsedResult.getC());
        
        verify(toolProjectService, times(1)).create(any(ToolProjectSaveParam.class), anyBoolean());
    }

    @Test
    public void testGetDesignSearchableStatus_Success() {
        // Setup
        Long designId = 1001L;
        String encryptedDesignId = LongCipher.DEFAULT.encrypt(designId);
        
        when(toolProjectService.getDesignSearchableStatus(designId))
                .thenReturn(DesignSearchableStatus.TAG_CAN_BE_SEARCHED);
        
        // Execute
        Result<Integer> result = toolProjectController.getDesignSearchableStatus(encryptedDesignId);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertTrue("Should be success", result.success());
        assertEquals("Status should match",
                Integer.valueOf(DesignSearchableStatus.TAG_CAN_BE_SEARCHED.getValue()), result.getD());
        
        verify(toolProjectService, times(1)).getDesignSearchableStatus(designId);
    }

    @Test
    public void testGetDesignSearchableStatus_Exception() {
        // Setup
        String invalidDesignId = "invalid_id";
        
        // Execute
        Result<Integer> result = toolProjectController.getDesignSearchableStatus(invalidDesignId);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertFalse("Should be error", result.success());
        assertEquals("Error message should match", "系统异常", result.getM());
        
        verify(toolProjectService, never()).getDesignSearchableStatus(anyLong());
    }

    @Test
    public void testGetDesignSearchableStatus_ServiceException() {
        // Setup
        Long designId = 1001L;
        String encryptedDesignId = LongCipher.DEFAULT.encrypt(designId);
        
        when(toolProjectService.getDesignSearchableStatus(designId))
                .thenThrow(new RuntimeException("Service error"));
        
        // Execute
        Result<Integer> result = toolProjectController.getDesignSearchableStatus(encryptedDesignId);
        
        // Verify
        assertNotNull("Result should not be null", result);
        assertFalse("Should be error", result.success());
        assertEquals("Error message should match", "系统异常", result.getM());
        
        verify(toolProjectService, times(1)).getDesignSearchableStatus(designId);
    }
}
