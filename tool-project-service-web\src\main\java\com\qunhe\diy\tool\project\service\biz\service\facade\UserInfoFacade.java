/*
 * UserInfoFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.ImmutableSet;
import com.qunhe.diy.tool.project.service.biz.util.ProxyExtractor;
import com.qunhe.logcomplex.userinformation.client.UserInfoClientProxy;
import com.qunhe.logcomplex.userinformation.client.bean.param.ClientCheckUserInTagParam;
import com.qunhe.logcomplex.userinformation.common.bean.ext.ApiResult;
import com.qunhe.user.growth.property.center.client.api.KubiSoaApi;
import com.qunhe.user.growth.property.center.common.dto.kubi.KubiInfoSingleQueryDto;
import com.qunhe.user.growth.property.center.common.dto.kubi.KubiInfoSingleResultDto;
import com.qunhe.usergrowth.uic.data.dto.identify.UserIdentificationDto;
import com.qunhe.usergrowth.uic.data.dto.tag.UserTagMatchParamDto;
import com.qunhe.usergrowth.uic.rpc.client.IdentifyClient;
import com.qunhe.usergrowth.uic.rpc.client.UserTagClient;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Component
@RequiredArgsConstructor
public class UserInfoFacade implements ProxyExtractor<UserInfoFacade> {

    public static final String COOHOM_USER_TAG = "coohom_user";

    public static final String COOHOM_USER_APIC_TAG = "coohom_user_apic";

    public static final Set<String> COOHOM_USER_TAGS = Sets.newHashSet(
            COOHOM_USER_TAG, COOHOM_USER_APIC_TAG
    );

    public static final long TOOL_BIM_OLD_USER = 47112L;

    private final UserTagClient userTagClient;

    private final IdentifyClient identifyClient;

    private final UserInfoClientProxy userInfoClientProxy;

    private final KubiSoaApi kubiSoaApi;


    public boolean isCoohomUser(final Long userId) {
        final Set<String> matchCoohomTag = getProxy().matchUserTags(userId, COOHOM_USER_TAGS);
        return matchCoohomTag != null && !matchCoohomTag.isEmpty();
    }

    @SentinelResource(value = "matchUserTags")
    public Set<String> matchUserTags(final Long userId, final Set<String> tagKeys) {
        final UserTagMatchParamDto userTagMatchParamDto = new UserTagMatchParamDto(userId,
                tagKeys);
        //已过滤null，结果不会取到null
        return userTagClient.matchUserTags(userTagMatchParamDto);
    }

    @SentinelResource(value = "getUserNameByUserId", fallback = "getUserNameByUserIdFallback")
    public String getUserNameByUserId(final Long userId) {
        if (userId == null) {
            return null;
        }
        final UserIdentificationDto userIdentificationDto = identifyClient.getUserIdentification(
                userId);
        return userIdentificationDto == null ? null : userIdentificationDto.getRealName();
    }

    public String getUserNameByUserIdFallback(final Long userId, Throwable e) {
        LOG.message("getUserNameByUserId fall back to null.")
                .with("userId", userId)
                .warn();
        return null;
    }

    @SentinelResource(value = "ServiceCallFacade#getUserInfoTags",
            blockHandler = "getUserInfoTagsFallback")
    public Set<Long> getUserInfoTags(final Long userId) {
        ApiResult<Set<Long>> result = userInfoClientProxy.checkUserTags(
                ClientCheckUserInTagParam.builder()
                        .userId(userId)
                        .tagIds(ImmutableSet.of(TOOL_BIM_OLD_USER))
                        .build()
        );
        if (result != null && result.retrieveSuccessStatus()) {
            return result.getD();
        } else {
            LOG.message("getUserInfoTagsFailed")
                    .with("code", result == null ? null : result.getC())
                    .with("msg", result == null ? null : result.getM())
                    .with("userId", userId)
                    .warn();
            return Collections.emptySet();
        }
    }

    public Set<Long> getUserInfoTagsFallback(final Long userId, final BlockException e) {
        LOG.message("getUserInfoTagsFallback", e)
                .with("userId", userId)
                .warn();
        return Collections.emptySet();
    }

    @SentinelResource(value = "checkKubiInfo", fallback = "checkKubiInfoFallback")
    public int checkKubiInfo(final Long userId) {
        KubiInfoSingleQueryDto kubiInfoSingleQueryDto = new KubiInfoSingleQueryDto();
        kubiInfoSingleQueryDto.setUserId(userId);
        kubiInfoSingleQueryDto.setIncludePoint(false);
        Result<KubiInfoSingleResultDto> kubiInfo = kubiSoaApi.getKubiInfo(kubiInfoSingleQueryDto);
        if (!kubiInfo.success()) {
            return 0;
        }
        if (kubiInfo.getD() != null && kubiInfo.getD().getKubiInfo() != null) {
            return kubiInfo.getD().getKubiInfo().getKubiTotal();
        } else {
            LOG.message("checkKubiInfo - result is null")
                    .with("userId", userId)
                    .warn();
            return 0;
        }

    }

    public int checkKubiInfoFallback(final Long userId, final Throwable e) {
        LOG.message("checkKubiInfoFallback", e)
                .with("userId", userId)
                .error();
        return 0;
    }

}
