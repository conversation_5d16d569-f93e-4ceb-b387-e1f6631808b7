/*
 * FphFloorplanServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.house.property.floorplan.home.client.FphFloorplanClient;
import com.qunhe.house.property.floorplan.home.common.constants.BizConstants;
import com.qunhe.house.property.floorplan.home.common.enums.AreaGradeEnum;
import com.qunhe.house.property.floorplan.home.common.model.AreaItemDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FphFloorplanServiceTest {

    @Mock
    private FphFloorplanClient mockFphFloorplanClient;

    private FphFloorplanService fphFloorplanServiceUnderTest;

    @Before
    public void setUp() throws Exception {
        fphFloorplanServiceUnderTest = new FphFloorplanService(mockFphFloorplanClient);
    }

    @Test
    public void testGetIpLocation() throws Exception {
        // Setup
        // Configure FphFloorplanClient.getIpLocation(...).
        final Optional<AreaItemDTO> areaItemDTO = Optional.of(new AreaItemDTO(0L, "areaName", AreaGradeEnum.NATION));
        when(mockFphFloorplanClient.getIpLocation("ip", "toolDesignSaveIPLocationToken")).thenReturn(areaItemDTO);

        // Run the test
        final AreaItemDTO result = fphFloorplanServiceUnderTest.getIpLocation("ip");

        // Verify the results
        Assert.assertNotNull("getIpLocation is null", result);
    }

    @Test
    public void testGetIpLocation_FphFloorplanClientReturnsAbsent() throws Exception {
        // Setup
        when(mockFphFloorplanClient.getIpLocation("ip", "toolDesignSaveIPLocationToken")).thenReturn(Optional.empty());

        // Run the test
        final AreaItemDTO result = fphFloorplanServiceUnderTest.getIpLocation("ip");

        // Verify the results
        Assert.assertEquals("getIpLocation is not default", BizConstants.DEFAULT_AREA_ID, result.getAreaId());
    }

    @Test
    public void testGetIpLocation_FphFloorplanClientThrowsException() throws Exception {
        // Setup
        when(mockFphFloorplanClient.getIpLocation("ip", "toolDesignSaveIPLocationToken")).thenThrow(Exception.class);

        // Run the test
        final AreaItemDTO result = fphFloorplanServiceUnderTest.getIpLocation("ip");

        // Verify the results
        Assert.assertEquals("getIpLocation is not default", BizConstants.DEFAULT_AREA_ID, result.getAreaId());
    }

    @Test
    public void testGetIpLocationFallBack() {
        // Setup
        final BlockException e = null;

        // Run the test
        final AreaItemDTO result = fphFloorplanServiceUnderTest.getIpLocationFallBack("ip", e);

        // Verify the results
        Assert.assertEquals("getIpLocation is not default", BizConstants.DEFAULT_AREA_ID, result.getAreaId());
    }
}
