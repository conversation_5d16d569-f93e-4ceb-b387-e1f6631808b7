<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ CommunityMapper.xml
  ~ Copyright 2023 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.1//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        areaid,
        name,
        level,
        parentareaid,
        fullname,
        urldomain,
        shortname,
        created,
        lastmodified
        tier,
        nation
    </sql>

    <select id="getPidAndFullNameById"
            resultType="com.qunhe.diy.tool.project.service.common.param.CommunityInfo">
        SELECT
            parentareaid as sysParentAreaId,
            fullname as sysFullName
        FROM sysdictarea
        WHERE areaid = #{areaId}
    </select>

    <select id="getFullName" resultType="String">
        SELECT fullname
        FROM sysdictarea
        WHERE `areaid` = #{areaId}
    </select>

    <select id="getById" resultType="com.qunhe.instdeco.plan.data.SysDictArea">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sysdictarea
        WHERE `areaid` = #{areaId}
    </select>

    <select id="getByName" resultType="com.qunhe.instdeco.plan.data.SysDictArea">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        sysdictarea
        WHERE `name` LIKE CONCAT("%", #{name}, "%")
        <if test="level != null">
            AND `level` = #{level}
        </if>
    </select>

    <select id="getChildren" resultType="com.qunhe.instdeco.plan.data.SysDictArea">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sysdictarea
        WHERE `parentareaid` = #{areaId}
    </select>
</mapper>