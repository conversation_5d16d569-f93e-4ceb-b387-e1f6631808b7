/*
 * SaaSConfigServiceHasTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.instdeco.libra.client.AbTestClient;
import com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient;
import com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasExceptionCountDegradeResource;
import static com.qunhe.diy.tool.project.service.biz.util.HasTestUtil.setHasRtDegradeResource;
import static org.mockito.Mockito.any;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@Import({ SaaSConfigService.class, SentinelResourceAspect.class })
public class SaaSConfigServiceHasTest {

    @MockBean(name = "accessPointClient")
    AccessPointClient accessPointClient;
    @MockBean
    AbTestClient abTestClient;
    @MockBean
    UserInfoFacade userInfoFacade;
    @MockBean
    SaasConfigFacade saasConfigFacade;
    @MockBean(name = "commercialAccessPointClient")
    com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient
            commercialAccessPointClient;
    @MockBean
    BusinessAccountDb businessAccountDb;

    @Autowired
    SaaSConfigService saaSConfigService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void checkProjectCountPointAccess_fallback_success() throws AccessAuthenticatorException {
        // 构造 has 降级规则，模拟异常数降级
        setHasExceptionCountDegradeResource("checkProjectCountPointAccess");

        // 模拟业务代码抛出异常
        Mockito.when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class)))
                .thenThrow(new RuntimeException("mock exception"));

        // 走到fallback 逻辑，返回true
        Assert.assertFalse("fallback to false", saaSConfigService.checkProjectCountPointAccess(2L));

    }

    @Test
    public void checkProjectCountPointAccess_rt_degrade() throws AccessAuthenticatorException {
        // 构造 has 降级规则，模拟rt超时降级
        setHasRtDegradeResource("checkProjectCountPointAccess");

        // 模拟业务代码
        Mockito.when(accessPointClient.filterAccessPoint(any(CheckAccessibleRequestBody.class))).thenReturn(Lists.newArrayList());

        Assert.assertFalse("first time return false",
                saaSConfigService.checkProjectCountPointAccess(2L));

        // 走到rt fallback 逻辑，返回false
        Assert.assertFalse("fallback to true", saaSConfigService.checkProjectCountPointAccess(2L));

    }
}