/*
 * BatchOperationCheckAdvice.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.advice;

import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.log.QHLogger;
import org.apache.http.HttpStatus;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
@Aspect
public class BatchOperationCheckAdvice {
    private static final QHLogger LOG = QHLogger.getLogger(BatchOperationCheckAdvice.class);

    private static final String UP_VIP = "cli-ver";

    @Autowired
    private GeneralProperties generalProperties;

    @Pointcut("@annotation(com.qunhe.diy.tool.project.service.biz.annotation.BatchOperationCheck)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object arroundAdvice(final ProceedingJoinPoint joinPoint) throws Throwable {
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        final ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) requestAttributes;
        final HttpServletRequest request = servletRequestAttributes.getRequest();
        final HttpServletResponse response = servletRequestAttributes.getResponse();

        //上游vip
        final String upVip = request.getHeader(UP_VIP);
        try {
            if (!generalProperties.getLegalVipForBatchOperation().contains(upVip)) {
                LOG.message("illegal vip in batchOperation")
                        .with("upVip", upVip)
                        .error();
                response.setStatus(HttpStatus.SC_FORBIDDEN);
                return null;
            }
        } catch (Exception e) {
            //异常处理
            LOG.message("BatchOperationCheckAdvice occurs exception", e)
                    .with("upVip", upVip)
                    .error();
            return null;
        }
        return joinPoint.proceed();
    }
}
