/*
 * ToolProjectService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.assembly.oplog.annotation.DiyPrint;
import com.qunhe.assembly.oplog.annotation.OpLogAnalyze;
import com.qunhe.assembly.oplog.enums.PrintTypeEnum;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.constants.BusinessConstant;
import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.FloorPlanMetaDataDb;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.regionfinder.ProjectRegionFinder;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFindResult;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFinderParam;
import com.qunhe.diy.tool.project.service.biz.service.CommunityService;
import com.qunhe.diy.tool.project.service.biz.service.ProjectAuthService;
import com.qunhe.diy.tool.project.service.biz.service.facade.AlbumService;
import com.qunhe.diy.tool.project.service.biz.service.facade.DowngradingService;
import com.qunhe.diy.tool.project.service.biz.service.facade.ProjectNotifyService;
import com.qunhe.diy.tool.project.service.biz.service.facade.ProjectSearchService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SecurityDetectService;
import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.DesignSearchableStatus;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.enums.ProjectBusinessEnum;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.CommunityInfo;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.common.util.ParamCheckUtil;
import com.qunhe.diy.tool.project.service.common.util.PlanTypeUtil;
import com.qunhe.diy.tool.project.service.common.util.SecurityUtil;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.diybe.dms.util.VrcUtil;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.i18n.locale.context.FileMessageSource;
import com.qunhe.i18n.locale.helper.LocaleHelper;
import com.qunhe.instdeco.picservice.data.Album;
import com.qunhe.instdeco.plan.util.StringUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.monitor.faros.api.Faros;
import com.qunhe.monitor.faros.api.MetricMeta;
import com.qunhe.project.platform.project.auth.data.TransferErrorCode;
import com.qunhe.project.platform.project.auth.data.UserAuthTransferResult;
import com.qunhe.projectmanagement.client.data.QueryByDesignIdParam;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.utils.uniqueid.NotEnoughUniqueIdException;
import com.qunhe.utils.uniqueid.UniqueIdGenerationUnrecoverableException;
import com.qunhe.utils.uniqueid.UniqueIdGenerator;
import com.qunhe.web.standard.data.Result;
import com.qunhe.web.standard.exception.BizzException;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@SuppressWarnings(
        { "PMD.ExcessiveImports", "PMD.MethodTooLongRule", "PMD.CyclomaticComplexity", "PMD" +
                ".SignatureDeclareThrowsException", "PMD" +
                ".CognitiveComplexity", "PMD.NPathComplexity" })
public class ToolProjectService {
    private static final int DESIGN_NAME_LENGTH_LIMIT = 100;

    private static final QHLogger LOG = QHLogger.getLogger(ToolProjectService.class);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final HomeDesignFacadeDb homeDesignFacadeDb;

    private final UniqueIdGenerator uniqueIdGenerator;

    private final ProjectNotifyService projectNotifyService;

    private final AlbumService albumService;

    private final UserDb userDb;

    private final GeneralProperties generalProperties;

    private final SaaSConfigService saaSConfigService;

    private final ProjectSearchService projectSearchService;

    private final DowngradingService downgradingService;

    private final SecurityDetectService securityDetectService;

    private final CommunityDb communityDb;

    private final SysDictAreaMapper sysDictAreaMapper;

    private final LongCrypt longCrypt;

    private final ProjectAuthService projectAuthService;

    private final TrialUserFacade trialUserFacade;

    private final CommunityService communityService;

    private final ProjectRegionFinder projectRegionFinder;

    private final FloorPlanMetaDataDb floorPlanMetaDataDb;

    private final ToadProperties toadProperties;

    private final LevelDesignFacadeDb levelDesignFacadeDb;

    private final FileMessageSource fileMessageSource;

    private final ProjectClient projectClient;

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "createProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#toolProjectSaveParam.userId"))
    public ToolProjectHomeDesign create(ToolProjectSaveParam toolProjectSaveParam,
            final boolean isCoohom) throws Exception {
        fillDefaultProjectName(toolProjectSaveParam);
        ToolProjectHomeDesign tphd = createProjectDesign(toolProjectSaveParam, isCoohom);
        downgradingService.addHotData(toolProjectSaveParam, tphd,
                UserDb.getUserIdBySession());
        return tphd;
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "createProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#toolProjectSaveParam.userId"))
    private ToolProjectHomeDesign createProjectDesign(ToolProjectSaveParam toolProjectSaveParam,
            final boolean isCoohom) throws Exception {

        //before create
        ParamCheckUtil.checkSaveParam(toolProjectSaveParam);
        if (StringUtil.containsEmoji(MAPPER.writeValueAsString(toolProjectSaveParam))) {
            LOG.message("toolProjectSaveParam contains emoji")
                    .withPoJo(toolProjectSaveParam)
                    .warn();
            throw new ToolProjectCreateException(ErrorCode.PARAM_ERROR);
        }
        if (StringUtils.isNotEmpty(toolProjectSaveParam.getName()) &&
                toolProjectSaveParam.getName().length() > DESIGN_NAME_LENGTH_LIMIT) {
            LOG.message("toolProjectSaveParam's desginname is beyond length limit")
                    .withPoJo(toolProjectSaveParam)
                    .warn();
            throw new ToolProjectCreateException(ErrorCode.PARAM_ERROR);
        }

        if (trialUserFacade.isTrialUser() && generalProperties.isDisableTrialUser()) {
            LOG.message("disable trial user create project")
                    .withPoJo(toolProjectSaveParam)
                    .warn();
            throw new ToolProjectCreateException(ErrorCode.TRIAL_USER_FORBIDDEN);
        }

        // 数量限制
        if (generalProperties.getCountCheck()) {
            countCheck(toolProjectSaveParam.getUserId());
        }

        completeSaveParam(toolProjectSaveParam);

        RegionFindResult regionFindResult = null;
        if (toolProjectSaveParam.getRegion() == null) {
            regionFindResult =
                    projectRegionFinder.findRegion(
                            new RegionFinderParam(toolProjectSaveParam.getDesignId(),
                                    toolProjectSaveParam.getUserId(), toolProjectSaveParam.getVrc(),
                                    isCoohom));
            LOG.message("find the user region")
                    .with("userId", toolProjectSaveParam.getUserId())
                    .with("region", regionFindResult)
                    .info();
            toolProjectSaveParam.setRegion(regionFindResult.getRegion().getCode());
        }

        generateDataId(toolProjectSaveParam);
        Album album = getAlbum(toolProjectSaveParam);
        toolProjectSaveParam.setAlbumId(album.getAlbumId());

        //create
        ProjectSequence projectSequence = new ProjectSequence();
        HomeDesignData homeDesignData;
        try {
            //analyze location
            communityService.updateCommidIfPreciseIpSupport(isCoohom, toolProjectSaveParam);

            projectSequence = projectDesignFacadeDb.createProject(toolProjectSaveParam);
            LOG.message("createProjectDesign -- success")
                    .with("toolProjectSaveParam", toolProjectSaveParam)
                    .with("regionFindResult", regionFindResult)
                    .info();
            homeDesignData = homeDesignFacadeDb.createHomeDesign(projectSequence,
                    toolProjectSaveParam);
            saveUserAuthSilently(toolProjectSaveParam.getUserId(), projectSequence.getDesignId());
            if (trialUserFacade.isTrialUser()) {
                LOG.message("trial user create design")
                        .with("userId", UserDb.getUserIdBySession())
                        .with("ip", HunterContext.getContext().get("X-Forwarded-For"))
                        .info();
                Faros.counter(MetricMeta.builder("trial_design_create_count")
                        .alias("游客方案创建数量")
                        .build()).inc();
                trialUserFacade.markTrialDesignCreated(projectSequence.getDesignId());
            }
        } catch (final Exception e) {
            if (projectSequence != null && projectSequence.getDesignId() != 0) {
                rollbackDesign(projectSequence.getDesignId());
            }
            throw e;
        }
        ToolProjectHomeDesign toolProjectHomeDesign = convert2Result(homeDesignData);

        // after create

        albumService.updateAlbumDesignId(album, toolProjectHomeDesign.getDesignId());
        projectNotifyService.notifyProjectCreate(toolProjectSaveParam, projectSequence,
                UserDb.getSessionUserBySession());
        // update dwDrawingVersion
        floorPlanMetaDataDb.addOrUpdateDwDrawingVersion(
                toolProjectHomeDesign.getPlanId(), toadProperties.getDwDrawingVersion());
        return toolProjectHomeDesign;
    }


    public ToolProjectHomeDesign copyProjectDesign(final ToolProjectCopyParam projectCopyParam,
            final boolean isCoohom)
            throws Exception {
        // before copy
        ParamCheckUtil.checkCopyParam(projectCopyParam);

        // get and check original project
        ProjectDesign srcProjectDesign = projectDesignFacadeDb.getProject(convert2QueryDesignParam(
                projectCopyParam));
        if (srcProjectDesign == null) {
            throw new ToolProjectCreateException(ErrorCode.COPY_GET_PROJECT_ERROR);
        }
        checkOriginalProjectCanCopy(projectCopyParam, srcProjectDesign);

        RegionFindResult regionFindResult = null;
        if (projectCopyParam.getRegion() == null) {
            regionFindResult = projectRegionFinder.findRegion(new RegionFinderParam(
                    srcProjectDesign.getDesignId(), projectCopyParam.getDstUserId(),
                    srcProjectDesign.getVrc(), isCoohom));
            LOG.message("copyProjectDesign -- find the user region")
                    .with("userId", projectCopyParam.getDstUserId())
                    .with("region", regionFindResult)
                    .info();
            projectCopyParam.setRegion(regionFindResult.getRegion().getCode());
        }

        if (projectCopyParam.getCreatedAppId() == null) {
            Map<Long, Integer> map = projectClient.getCreatedAppIdsByDesignIds(
                    Collections.singletonList(srcProjectDesign.getDesignId()));
            if (MapUtils.isNotEmpty(map)) {
                Integer createdAppId = map.get(srcProjectDesign.getDesignId());
                projectCopyParam.setCreatedAppId(createdAppId);
            }
        }
        if (projectCopyParam.getCreatedAppId() == null) {
            LOG.message("createdAppId is null when copy design")
                    .with("srcDesignId", srcProjectDesign.getDesignId())
                    .warn();
        }
        // create project design
        ToolProjectSaveParam saveParam = convert2SaveParam(srcProjectDesign, projectCopyParam);
        ToolProjectHomeDesign toolProjectHomeDesign = createProjectDesign(saveParam, isCoohom);
        LOG.message("copyProjectDesign -- success")
                .with("userId", projectCopyParam.getDstUserId())
                .with("projectCopyParam", projectCopyParam)
                .with("regionFindResult", regionFindResult)
                .info();

        // copy home design
        try {
            val dstHomeDesignData = homeDesignFacadeDb.copyHomeDesignIgnoreRecycles(
                    srcProjectDesign, toolProjectHomeDesign);
            completeHomeDesign(toolProjectHomeDesign, dstHomeDesignData);
        } catch (final Exception e) {
            rollbackDesign(toolProjectHomeDesign.getDesignId());
            throw e;
        }
        downgradingService.addHotData(saveParam, toolProjectHomeDesign,
                UserDb.getUserIdBySession());
        // update dwDrawingVersion
        floorPlanMetaDataDb.copyDwDrawingVersion(projectCopyParam.getScrPlanId(),
                toolProjectHomeDesign.getPlanId());
        return toolProjectHomeDesign;
    }


    private void checkOriginalProjectCanCopy(final ToolProjectCopyParam projectCopyParam,
            final ProjectDesign srcProjectDesign) throws BizzException {
        if (srcProjectDesign == null) {
            throw new ToolProjectCreateException(ErrorCode.COPY_GET_PROJECT_ERROR);
        }
        if (!projectCopyParam.getIncludeDeleted() && srcProjectDesign.getUserId() == 1) {
            throw new ToolProjectCreateException(ErrorCode.COPY_GET_PROJECT_ERROR);
        }
        Integer dstPlanType = projectCopyParam.getPlanType();
        checkPlanType(dstPlanType, srcProjectDesign.getPlanType().intValue());
    }

    /**
     * 针对传入planType的校验
     */
    private void checkPlanType(Integer dstPlanType, Integer srcPlanType)
            throws ToolProjectCreateException {
        if (dstPlanType != null && PlanTypeUtil.isBIMPlan(srcPlanType.byteValue()) &&
                !dstPlanType.equals(srcPlanType)) {
            throw new ToolProjectCreateException(ErrorCode.PLAN_TYPE_INVALID_ERROR);
        }
    }

    private void completeHomeDesign(ToolProjectHomeDesign toolProjectHomeDesign,
            HomeDesignData homeDesignData) {
        if (homeDesignData == null) {
            return;
        }
        toolProjectHomeDesign.setLevels(homeDesignFacadeDb.generateLevelDesign(homeDesignData));
    }

    private void rollbackDesign(final Long designId) throws BizzException {
        LOG.message("create project design error -- rollback project").
                with("designId", designId)
                .info();
        int res = projectDesignFacadeDb.deleteProject(designId);
        if (res == 0) {
            LOG.message("create project design error -- rollback project error").
                    with("designId", designId).error();
            throw new ToolProjectCreateException(ErrorCode.ROLLBACK_PROJECT_ERROR);
        }
    }

    private ToolProjectSaveParam convert2SaveParam(ProjectDesign projectDesign,
            ToolProjectCopyParam projectCopyParam) {
        ToolProjectSaveParam saveParam = new ToolProjectSaveParam();
        saveParam.setUserId(projectCopyParam.getDstUserId());
        if (projectCopyParam.getPlanType() != null) {
            saveParam.setPlanType(projectCopyParam.getPlanType().byteValue());
        } else {
            saveParam.setPlanType(projectDesign.getPlanType());
        }
        saveParam.setVrc(projectDesign.getVrc());
        saveParam.setProjectBusinessEnum(ProjectBusinessEnum.BACKEND_COPY);
        saveParam.setArea(projectDesign.getArea());
        saveParam.setRealArea(projectDesign.getRealArea());
        saveParam.setSrcArea(projectDesign.getSrcArea());
        saveParam.setCommId(projectDesign.getCommId());
        saveParam.setModelStatus(BusinessConstant.MODEL_STATUS);
        saveParam.setSpecId(projectDesign.getSpecId());
        saveParam.setUploadPics(projectDesign.getUploadPics());
        saveParam.setUnitId(projectDesign.getUnitId());
        saveParam.setRecommend(projectDesign.getRecommend() != 0);
        saveParam.setDesignAttribute(projectDesign.getDesignattrPrivatestatus());
        saveParam.setAuthorId(projectDesign.getUserId());
        saveParam.setRegion(projectCopyParam.getRegion());
        saveParam.setCreatedAppId(projectCopyParam.getCreatedAppId());
        if (StringUtils.isBlank(projectCopyParam.getName())) {
            saveParam.setName(projectDesign.getDesignName() + BusinessConstant.COPY_ITEM_SUFFIX);
        } else {
            saveParam.setName(projectCopyParam.getName());
        }
        if (projectCopyParam.getReworked() != null) {
            saveParam.setReworked(projectCopyParam.getReworked());
        } else {
            saveParam.setReworked(projectDesign.getReworked() != 0);
        }
        // 有一些脏数据方案 designSaved = 0, 导致复制后的方案查询不可见，这里手动设置为true，兼容脏数据
        // 见 https://cf.qunhequnhe.com/pages/viewpage.action?pageId=***********
        saveParam.setDesignSaved(true);
        if (projectCopyParam.getSourceId() != null) {
            saveParam.setSourceId(projectCopyParam.getSourceId());
        } else {
            saveParam.setSourceId(projectDesign.getSourceId());
        }
        saveParam.setDesignDesc(projectDesign.getDesignDesc());
        return saveParam;
    }

    private QueryByDesignIdParam convert2QueryDesignParam(
            ToolProjectCopyParam toolProjectCopyParam) {
        return QueryByDesignIdParam.builder()
                .designId(toolProjectCopyParam.getSrcDesignId())
                .deleted(toolProjectCopyParam.getIncludeDeleted() ? null : false)
                .build();
    }

    private void generateDataId(final ToolProjectSaveParam projectDesignSaveData)
            throws UniqueIdGenerationUnrecoverableException, NotEnoughUniqueIdException {
        // modelDataId 优先级高于designDataId
        if (StringUtils.isNotBlank(projectDesignSaveData.getModelDataId())) {
            projectDesignSaveData.setDesignDataId(projectDesignSaveData.getModelDataId());
            return;
        }
        if (StringUtils.isNotBlank(projectDesignSaveData.getDesignDataId())) {
            projectDesignSaveData.setModelDataId(projectDesignSaveData.getDesignDataId());
            return;
        }
        final String dataId = uniqueIdGenerator.generateCaseInsensetiveUniqueId();
        projectDesignSaveData.setModelDataId(dataId);
        projectDesignSaveData.setDesignDataId(dataId);
    }

    private Album getAlbum(final ToolProjectSaveParam projectDesignSaveData) {
        return albumService.getAlbum(projectDesignSaveData);
    }

    private ToolProjectHomeDesign convert2Result(final HomeDesignData homeDesignData) {
        return ToolProjectHomeDesign.builder()
                .designId(homeDesignData.getDesignId())
                .planId(homeDesignData.getId())
                .levels(homeDesignFacadeDb.generateLevelDesign(homeDesignData))
                .build();
    }


    private void completeSaveParam(final ToolProjectSaveParam toolProjectSaveParam) {
        toolProjectSaveParam.setModelStatus(BusinessConstant.MODEL_STATUS);
        if (toolProjectSaveParam.getPlanType() == null) {
            toolProjectSaveParam.setPlanType((byte) 0);
        }
        if (toolProjectSaveParam.getVrc() == null) {
            toolProjectSaveParam.setVrc(VrcUtil.getVrcByPlanType(null,
                    toolProjectSaveParam.getPlanType()));
        }
        if (toolProjectSaveParam.getArea() != null && toolProjectSaveParam.getArea() < 0) {
            toolProjectSaveParam.setArea(null);
        }
        if (toolProjectSaveParam.getRealArea() != null && toolProjectSaveParam.getRealArea() < 0) {
            toolProjectSaveParam.setRealArea(null);
        }
        if (toolProjectSaveParam.getRealArea() == null && toolProjectSaveParam.getArea() != null) {
            toolProjectSaveParam.setRealArea(toolProjectSaveParam.getArea());
        }
        if (toolProjectSaveParam.getSrcArea() != null && toolProjectSaveParam.getSrcArea() < 0) {
            toolProjectSaveParam.setSrcArea(null);
        }
        if (toolProjectSaveParam.getUploadPics() != null &&
                toolProjectSaveParam.getUploadPics().length() >
                        BusinessConstant.IMAGE_URL_MAX_LENGTH) {
            toolProjectSaveParam.setUploadPics(null);
        }
        if (toolProjectSaveParam.getDesignSaved() == null) {
            toolProjectSaveParam.setDesignSaved(true);
        }
        if (toolProjectSaveParam.getSpecId() == null) {
            toolProjectSaveParam.setSpecId(0L);
        }
        String name = toolProjectSaveParam.getName();
        final Boolean reworked = toolProjectSaveParam.getReworked();
        if (BooleanUtils.isTrue(reworked) && toolProjectSaveParam.getUserId() != null) {
            final UserDto user = userDb.getUser(toolProjectSaveParam.getUserId());
            if (user != null) {
                name = user.getUserName() + BusinessConstant.RECOMMEND_NAME_SUFFIX;
            }
        }
        if (name != null) {
            name = name.length() <= BusinessConstant.MAX_NAME_LENGTH ? name :
                    name.substring(0, BusinessConstant.MAX_NAME_LENGTH);
        }
        toolProjectSaveParam.setName(name);
        toolProjectSaveParam.setRecommend(getRecommend(toolProjectSaveParam));
    }

    private Boolean getRecommend(final ToolProjectSaveParam floorPlan) {
        boolean areaValid = floorPlan.getRealArea() != null && floorPlan
                .getRealArea() < BusinessConstant.RECOMMEND_MIN_AREA;
        if (!checkIfRecommend(floorPlan.getName()) || areaValid) {
            return false;
        }

        final Long specId = floorPlan.getSpecId();
        if (specId == null) {
            return false;
        }

        long spec = specId;
        for (int i = 0; i < BusinessConstant.DIVIDE_TIME; i++) {
            if (spec % 10 == 0) {
                return false;
            }
            spec = spec / 10;
        }
        return true;
    }

    private boolean checkIfRecommend(final String name) {
        if (name == null) {
            return false;
        }
        for (final String unRecommendName : BusinessConstant.FLOORPLAN_NAME_FILTER) {
            if (name.contains(unRecommendName)) {
                return false;
            }
        }
        return name.length() >= BusinessConstant.MINIMAL_NAME_LENGTH;
    }

    private void countCheck(final Long userId) throws ToolProjectCreateException {
        //方案数量限制
        if (saaSConfigService.checkProjectCountPointAccess(userId)) {
            final long curCount = projectSearchService.countProject(userId);
            // 当前方案已经达到limit
            if (curCount >= generalProperties.getCountLimit()) {
                throw new ToolProjectCreateException(ErrorCode.CREATE_PROJECT_COUNT_ERROR);
            }
        }
    }

    public Boolean recycleDesign(Long userId, final Long planId, final Long designId) {
        try {
            Long finalDesignId = designId;
            ProjectDesign projectDesign = null;
            if (designId == null) {
                projectDesign = projectDesignFacadeDb.getProject(planId);
                if (projectDesign != null && projectDesign.getDeleted() == 0) {
                    finalDesignId = projectDesign.getDesignId();
                }
            }

            if (userId == null && projectDesign != null) {
                userId = projectDesign.getUserId();
            }
            boolean success = projectDesignFacadeDb.recycleDesign(userId, finalDesignId) > 0;
            recycleUserAuthSilently(userId, finalDesignId);
            return success;
        } catch (Exception e) {
            // 回收失败, 返回false
            LOG.message("recycle design fail", e).with("userId", userId).with("planId", planId)
                    .with("designId", designId);
            return false;
        }
    }

    public BatchDeleteDTO batchRecycleDesign(Long userId, List<Long> designIds) {
        List<Long> noAuthDesignIds = new ArrayList<>();
        BatchDeleteDTO batchDeleteDTO;
        try {
            for (Long designId : designIds) {
                if (!projectAuthService.hasDeletedAccess(null, null, null, designId)) {
                    LOG.message("batchRecycleDesign - no auth to delete design").with("userId",
                            userId).with("designId", designId).info();
                    noAuthDesignIds.add(designId);
                }
            }
            designIds.removeAll(noAuthDesignIds);
            boolean result = projectDesignFacadeDb.batchRecycleDesign(userId, designIds) > 0;
            if (result) {
                batchRecycleUserAuthSilently(userId, designIds);
            }
            batchDeleteDTO =
                    BatchDeleteDTO.builder().deleteResult(result).noDeleteAuthDesignIds(
                                    noAuthDesignIds)
                            .build();
        } catch (Exception e) {
            // // 回收失败, 封装状态码
            LOG.message("batchRecycleDesign fail", e).with("designIds", designIds)
                    .with("userId", userId).error();
            batchDeleteDTO = BatchDeleteDTO.builder().deleteResult(false).noDeleteAuthDesignIds(
                            noAuthDesignIds)
                    .build();
        }
        return batchDeleteDTO;
    }

    private void recycleUserAuthSilently(Long userId, Long designId) {
        try {
            if (designId == null) {
                return;
            }
            projectAuthService.recycleUserAuth(userId, designId);
        } catch (Exception e) {
            // 权限回收失败不影响正常流程
            LOG.message("recycle user auth error", e)
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
        }
    }

    public Boolean recoverProjectDesign(final Long userId, final Long planId, Long designId)
            throws ToolProjectCreateException {
        // 数量限制
        if (Boolean.TRUE.equals(generalProperties.getCountCheck())) {
            countCheck(userId);
        }
        try {
            Long finalPlanId = planId == null ? projectDesignFacadeDb.getPlanId(designId) : planId;
            if (finalPlanId == null) {
                return false;
            }
            boolean success = projectDesignFacadeDb.recoverProject(finalPlanId) > 0;
            recoverUserAuthSilently(userId,
                    designId == null ? projectDesignFacadeDb.getDesignId(finalPlanId) :
                            designId);
            return success;
        } catch (Exception e) {
            // 恢复失败, 返回false
            LOG.message("recover design fail", e).with("userId", userId).with("planId", planId)
                    .with("designId", designId).error();
            return false;
        }
    }

    public Boolean batchRecoverProjectDesign(final Long userId, final List<Long> designIds)
            throws ToolProjectCreateException {
        // 数量限制
        if (Boolean.TRUE.equals(generalProperties.getCountCheck())) {
            countCheck(userId);
        }
        try {
            boolean result = projectDesignFacadeDb.batchRecoverProject(designIds) > 0;
            if (result) {
                batchRecoverUserAuthSilently(userId, designIds);
            }
            return result;
        } catch (Exception e) {
            // 批量恢复失败, 返回false
            LOG.message("batch recover design fail", e).with("userId", userId).with("designIds",
                    designIds);
            return false;
        }
    }

    public Boolean restoreProjectDesign(final Long userId, final Long planId,
            Long designId, final boolean needCheckUserId)
            throws ToolProjectCreateException {
        if (planId == null && designId == null) {
            return false;
        }
        // 数量限制
        if (Boolean.TRUE.equals(generalProperties.getCountCheck())) {
            countCheck(userId);
        }

        if (designId == null) {
            final ProjectDesign projectDesign = projectDesignFacadeDb.getProject(planId);
            designId = projectDesign.getDesignId();
        }
        return projectDesignFacadeDb.restoreProject(userId, designId, needCheckUserId) > 0;
    }

    private void saveUserAuthSilently(Long userId, Long designId) {
        try {
            if (designId == null) {
                return;
            }
            projectAuthService.saveUserAuth(userId, designId);
        } catch (Exception e) {
            // 权限创建失败不影响正常流程
            LOG.message("save user auth error", e)
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
        }
    }

    private void recoverUserAuthSilently(Long userId, Long designId) {
        try {
            if (designId == null) {
                return;
            }
            projectAuthService.recoverUserAuth(userId, designId);
        } catch (Exception e) {
            // 权限恢复失败不影响正常流程
            LOG.message("recover user auth error", e)
                    .with("userId", userId)
                    .with("designId", designId)
                    .error();
        }
    }

    private void batchRecoverUserAuthSilently(Long userId, List<Long> designIds) {
        try {
            List<UserAuthTransferResult> transferExceptionAuths =
                    projectAuthService.batchRecoverUserAuth(userId, designIds)
                            .stream().filter(
                                    auth -> auth.getErrorCode() == TransferErrorCode.EXCEPTION)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transferExceptionAuths)) {
                LOG.message("batchRecoverProjectDesign -- auth update error")
                        .with("userId", userId)
                        .with("transferExceptionAuths", transferExceptionAuths)
                        .error();
            }
        } catch (Exception e) {
            // 权限恢复失败不影响正常流程
            LOG.message("batch recover user auth error", e)
                    .with("userId", userId)
                    .with("designIds", designIds)
                    .error();
        }
    }

    private void batchRecycleUserAuthSilently(Long userId, List<Long> designIds) {
        try {
            List<UserAuthTransferResult> transferExceptionAuths =
                    projectAuthService.batchRecoverUserAuth(userId, designIds)
                            .stream().filter(
                                    auth -> auth.getErrorCode() == TransferErrorCode.EXCEPTION)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transferExceptionAuths)) {
                LOG.message("batchRecoverProjectDesign -- auth update error")
                        .with("userId", userId)
                        .with("transferExceptionAuths", transferExceptionAuths)
                        .error();
            }
        } catch (Exception e) {
            // 权限回收失败不影响正常流程
            LOG.message("batch recycle user auth error", e)
                    .with("userId", userId)
                    .with("designIds", designIds)
                    .error();
        }
    }

    public Boolean deleteDesign(final Long userId, Long planId, Long designId) {
        final ProjectDesign projectDesign = designId == null ?
                projectDesignFacadeDb.getProject(planId)
                : projectDesignFacadeDb.getProjectByDesignId(designId);
        if (projectDesign == null) {
            return true;
        }
        if (projectDesign.getDeleted() == 0 && projectDesign.getUserId().equals(userId)) {
            return projectDesignFacadeDb.deleteProject(projectDesign.getDesignId()) > 0;
        }
        return true;
    }

    public Result<Boolean> modifyProjectInfo(final Long userId,
            final ToolProjectModifyParam modifyParam) {
        SecurityUtil.defendXxsAttack(modifyParam);
        //同盾脱敏处理, 将多个词组合到一起，避免多次调用同盾
        final String designNameSrc = Optional.ofNullable(modifyParam)
                .map(ToolProjectModifyParam::getName).orElse(" ");
        final String commName = Optional.ofNullable(modifyParam)
                .map(ToolProjectModifyParam::getCommName).orElse(" ");
        final String checkContent = designNameSrc + " " + " " + commName;
        if (!securityDetectService.sensitiveWordCheck(userId, checkContent)) {
            return Result.error("包含敏感词不允许修改");
        }

        //0.校验用户是否有修改方案权限(方案本人/特殊用户)
        if (StringUtils.isEmpty(modifyParam.getObsDesignId())) {
            return Result.error("ObsDesignId is empty");
        }
        final Long designId = longCrypt.decrypt(modifyParam.getObsDesignId());

        try {
            if (projectAuthService.checkAuth(userId, designId)) {
                final ProjectDesign projectDesign = projectDesignFacadeDb.getProjectByDesignId(
                        designId);
                if (projectDesign == null) {
                    return Result.error("fail to update project design, design is null");
                }
                //1.更新projectDesign
                val design = SecurityUtil.filterModifyParam(projectDesign, modifyParam);
                design.setPlanId(modifyParam.getObsPlanId() == null ?
                        projectDesignFacadeDb.getPlanId(designId)
                        : longCrypt.decrypt(modifyParam.getObsPlanId()));

                //2.更新社区相关字段,关联标准小区与户型关系
                final int areaId = Integer.valueOf(modifyParam.getAreaId());
                final CommunityInfo communityInfo = communityDb.getCommFullInfos(commName, areaId);
                if (Objects.isNull(communityInfo)) {
                    return Result.error(
                            "fail to get community, please check commName and areaId are correct " +
                                    "and matching");
                }
                design.setCommId(Long.valueOf(communityInfo.getCommId()));
                //开关打开后，其他社区由oceanus任务同步处理，避免二次刷写
                if (!generalProperties.isCommIdSyncByOceanus()) {
                    design.setCommunityHiden(communityInfo.getHiden());
                    design.setSysdictareaFullname(communityInfo.getSysFullName());

                    final int parentAreaId = communityInfo.getSysParentAreaId();
                    final CommunityInfo sysDictAreaInfo = sysDictAreaMapper.getPidAndFullNameById(
                            parentAreaId);
                    design.setCommLogicProvinceId(sysDictAreaInfo.getSysParentAreaId());
                    design.setCommFullName(sysDictAreaInfo.getSysFullName());
                }

                if (projectDesignFacadeDb.updateProjectWithModifiedTime(true, design) < 1) {
                    return Result.error("fail to modifyProjectInfo, sql-write exception occurs");
                }

                //埋点
                LOG.message("project-info-channge")
                        .with("designId", modifyParam.getObsDesignId())
                        .with("designName_after", design.getDesignName())
                        .with("area_change", modifyParam.getAreaId())
                        .with("community_change", modifyParam.getObsStdCommId())
                        .with("community_type", modifyParam.getObsStdCommId() != null ? 0 : 1)
                        .with("user_id", userId)
                        .analyze();

                //(暂存，实际未使用)校验方案名称是否剔除了特殊字段
                if (!StringUtils.equals(designNameSrc, design.getDesignName())) {
                    return Result.ok(true);
                }
            } else {
                return Result.error("userId has no right to edit");
            }
        } catch (IllegalArgumentException e) {
            LOG.message("fail to modifyProjectInfo", e)
                    .with("designId", designId)
                    .with("userId", userId)
                    .error();
            return Result.error("fail to modifyProjectInfo");
        }
        return Result.ok();
    }

    public Boolean rollbackProjectDesign(final Long userId, Long planId, Long designId)
            throws ToolProjectCreateException {
        if (planId == null) {
            planId = projectDesignFacadeDb.getPlanId(designId);
        }
        // 数量限制
        if (Boolean.TRUE.equals(generalProperties.getCountCheck())) {
            countCheck(userId);
        }
        int result = projectDesignFacadeDb.recoverDeleteProject(planId);
        return result > 0;
    }

    public DesignSearchableStatus getDesignSearchableStatus(Long designId) {
        ProjectDesign projectDesign = projectDesignFacadeDb.getProjectByDesignId(designId);
        if (projectDesign == null) {
            return DesignSearchableStatus.DESIGN_NOT_EXIST;
        }
        boolean tagSearchable = projectSearchService.checkDesignTagSearchable(designId);
        if (tagSearchable) {
            return DesignSearchableStatus.TAG_CAN_BE_SEARCHED;
        } else {
            return DesignSearchableStatus.TAG_CAN_NOT_BE_SEARCHED;
        }
    }

    public ProjectDesign getProjectDesignByDesignId(@NotNull final Long designId) {
        return projectDesignFacadeDb.getProjectByDesignId(designId);
    }

    public ProjectDesign getProjectDesignByPlanId(@NotNull final Long planId) {
        return projectDesignFacadeDb.getProject(planId);
    }

    public ProjectDesign getProjectDesignByLevelId(@NotNull final String levelId) {
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        return Optional.ofNullable(levelDesign)
                .map(ld -> projectDesignFacadeDb.getProjectByDesignId(levelDesign.getDesignId()))
                .orElse(null);
    }

    /**
     * 判断是否为需要需要生成方案历史版本的方案，目前只有筑多维导入
     */
    public boolean needDesignRevision(final ProjectDesign projectDesign) {
        // 筑多维融合后导入的方案，按vrc区分
        if (!toadProperties.isMultiLevelRevisionEnable()) {
            return false;
        }
        return toadProperties.getMultiLevelVrcSet().contains(projectDesign.getVrc());
    }

    public void fillDefaultProjectName(final ToolProjectSaveParam toolProjectSaveParam) {
        if (toolProjectSaveParam.getName() == null) {
            try {
                String designName = fileMessageSource.getMessage(BusinessConstant.LOCALE_DESIGN_KEY,
                        null, null, LocaleHelper.getLocale());
                toolProjectSaveParam.setName(designName);
            } catch (Exception e) {
                // 获取不到默认名称，则使用默认名称
                LOG.message("get project name locale fail", e).warn();
                toolProjectSaveParam.setName(BusinessConstant.DEFAULT_DESIGN_NAME);
            }
        }
    }

}
