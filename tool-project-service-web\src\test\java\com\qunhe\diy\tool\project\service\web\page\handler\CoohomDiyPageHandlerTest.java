/*
 * CoohomDiyPageHandlerTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diy.tool.project.service.web.page.diy.TreEnum;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyByte;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CoohomDiyPageHandler 的单元测试类。
 * <AUTHOR>
 * @date 2025/5/12
 */
@RunWith(MockitoJUnitRunner.class)
public class CoohomDiyPageHandlerTest {

    @InjectMocks
    @Spy
    private CoohomDiyPageHandler coohomDiyPageHandler;

    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private BusinessAccountDb businessAccountDb;
    @Mock
    private BusinessAccessService businessAccessService;
    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    private VrcAppIdDataDb vrcAppIdDataDb;
    @Mock
    private SessionConflictHandler sessionConflictHandler;
    @Mock
    private GrayLunchService grayLunchService;
    @Mock
    private ToadProperties toadProperties;
    @Mock
    private UserDb userDb;
    @Mock
    private SaaSConfigService saaSConfigService;
    @Mock
    private ToolLinkConfigCache toolLinkConfigCache;
    @Mock
    private SaasConfigFacade saasConfigFacade;
    @Mock
    private AuthCheckService authCheckService;

    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Spy
    private DiyPageParam param; // 使用Spy以便修改其内部值

    private final Long DEFAULT_USER_ID = 123L;
    private final Long DEFAULT_DESIGN_ID = 456L;
    private final String DEFAULT_OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(DEFAULT_DESIGN_ID);
    private final Long DEFAULT_ROOT_ACCOUNT_ID = 789L;
    private final String DEFAULT_ACTUAL_APP_ID = "testAppId";
    private final Long DEFAULT_APP_ID_NUM = 1011L;

    public static final byte MOCK_BIM_PLAN_TYPE_FF = 10; // 模拟 PlanTypeUtil.BIM_PLAN_TYPE_FF
    public static final byte MOCK_PHOTO_STUDIO_PLAN_TYPE = 20; // 模拟 PlanTypeUtil
    // .PHOTO_STUDIO_PLAN_TYPE
    public static final byte MOCK_UPDATE_BACKUP_PLAN_TYPE = 30; // 模拟 PlanTypeUtil
    // .UPDATE_BACKUP_PLAN_TYPE

    @Before
    public void setUp() {
        param.setUserId(DEFAULT_USER_ID);
        lenient().when(businessAccountDb.getRootAccountForBAndC(DEFAULT_USER_ID)).thenReturn(DEFAULT_ROOT_ACCOUNT_ID);
        // 默认重定向方法返回特定的PageServiceResult，以便验证它们是否被调用
        doReturn(ToolPageResult.builder().statusCode(HttpStatus.SEE_OTHER.value()).body("redirectDefaultPage_invoked").build()).when(coohomDiyPageHandler).redirectDefaultPage(any());
        doReturn(ToolPageResult.builder().statusCode(HttpStatus.SEE_OTHER.value()).body("coohomBimRedirect_invoked").build()).when(coohomDiyPageHandler).redirectCoohomBIMPage(any());
        doReturn(ToolPageResult.builder().statusCode(HttpStatus.SEE_OTHER.value()).body("redirectByUrl_invoked").build()).when(coohomDiyPageHandler).redirectByUrl(anyString());
        // spy/mock 父类的方法，确保它们在 finalizeH5ModelAndRespond 中被调用时不会执行真实逻辑，除非特定测试需要
        doNothing().when(coohomDiyPageHandler).fillUserInfo(anyLong(), any(H5Model.class));
        doNothing().when(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        doNothing().when(coohomDiyPageHandler).fillFavorIcon(anyLong(), any(H5Model.class));
        doNothing().when(coohomDiyPageHandler).fillAccountInfo(anyLong(), anyLong(), any(H5Model.class));
        lenient().doNothing().when(coohomDiyPageHandler).fillPlanInfo(anyString(), anyString(), anyString(), anyByte(), any(H5Model.class));
        doReturn(false).when(coohomDiyPageHandler).useNewDecorationVersion(any(HttpServletRequest.class));
        lenient().doReturn(false).when(coohomDiyPageHandler).checkUpgradeAccess(anyString(), anyString(), anyString());
        lenient().doReturn("mockRedirectUrl").when(coohomDiyPageHandler).redirectSuperOrPublicUpgradeUrl(anyString(), anyString(), anyString(), any(HttpServletRequest.class));
        doReturn("mockUpdateUrl").when(coohomDiyPageHandler).redirectUpdateUrl(any(HttpServletRequest.class), anyString());
        doReturn("mockUpdateUrlWithTre").when(coohomDiyPageHandler).redirectUpdateUrlWithTre(any(HttpServletRequest.class), anyLong(), anyList());
        lenient().doReturn(false).when(coohomDiyPageHandler).needRedirectBIM(any(HttpServletRequest.class), anyLong(), anyLong());

        // Mock toadProperties
        when(toadProperties.getProjectStage()).thenReturn("test-stage");
        when(toadProperties.getVrcConfig()).thenReturn("test-vrc-config");

        // Mock abTestConfig
        when(saaSConfigService.getAbTestResult(anyString(), anyString(), anyLong(), anyString()))
                .thenReturn(new HashMap<>());

        when(authCheckService.checkAuthFromDesignId(DEFAULT_USER_ID, null, DEFAULT_DESIGN_ID, false)).thenReturn(false);
    }

    /**
     * 测试 CoohomDiyPageHandler 的 handleExistingDesign 方法的一个基本场景。
     */
    @Test
    public void testHandleExistingDesign_BasicScenario() throws Exception {
        // Arrange
        // 中文注释：准备参数和mock对象的行为
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        param.setPlanName("基础场景测试方案"); // 为 fillPlanInfo 提供参数
        param.setPlanType((byte)0);

        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), eq(DEFAULT_DESIGN_ID), anyBoolean())).thenReturn(true);

        DiyDesignInfo mockDiyDesignInfo = new DiyDesignInfo();
        mockDiyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        mockDiyDesignInfo.setPlanId(99L); // 与其他测试不同的planId
        mockDiyDesignInfo.setName("基础场景测试方案");
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(mockDiyDesignInfo);

        ProjectDesign mockProjectDesign = new ProjectDesign();
        mockProjectDesign.setVrc("V0140R0401"); // 一个简单且不需要更新的VRC
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(mockProjectDesign);

        // 中文注释：确保不走任何提前重定向的逻辑
        when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(anyLong(), anyMap())).thenReturn(false);
        // checkUpgradeAccess 已经在 setUp 中 mock 为 false
        // param.isForceUpdate() 默认为 false
        // param.getTre() 默认为 null

        // Act
        // 中文注释：执行被测方法
        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // Assert
        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码应为 OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        // 中文注释：验证响应体包含预期的设计ID
        assertNotNull("响应体不应为空", result.getBody());
        assertTrue("响应体应包含设计ID", result.getBody().toString().contains("\"obsDesignId\":\"" + DEFAULT_OBS_DESIGN_ID + "\""));
        // 中文注释：验证响应体包含预期的方案名称
        assertTrue("响应体应包含方案名称", result.getBody().toString().contains("\"planName\":\"基础场景测试方案\""));
        // 中文注释：验证响应体包含预期的VRC
        assertTrue("响应体应包含VRC", result.getBody().toString().contains("\"vrc\":\"V0140R0401\""));

        // 中文注释：验证核心的父类填充方法被调用
        verify(coohomDiyPageHandler).fillUserInfo(eq(DEFAULT_USER_ID), any(H5Model.class));
        verify(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        verify(coohomDiyPageHandler).fillAccountInfo(eq(DEFAULT_USER_ID), eq(DEFAULT_ROOT_ACCOUNT_ID), any(H5Model.class));
        // fillPlanInfo 是静态的，其效果通过JSON内容验证
    }

    /**
     * 测试 CoohomDiyPageHandler 的 handleNewDesign 方法的一个基本场景。
     * (这是一个占位符测试，需要根据 CoohomDiyPageHandler 的具体逻辑进行扩展)
     */
    @Test
    public void testHandleNewDesign_BasicScenario() throws Exception {
        // Arrange
        // 中文注释：准备参数和mock对象的行为，确保是新建设计
        param.setObsDesignId(null); // 新建设计
        param.setPlanName("新基础场景方案");
        param.setPlanType((byte)10); // 任意一个planType

        // 中文注释：确保不走任何BIM重定向或AB测试重定向的逻辑
        // AppType.isBimApp(appIdNum) -> 依赖于 DEFAULT_APP_ID_NUM 不是 BIM app, 或该检查不通过
        // param.isRedirectBim() 默认为 false
        // needRedirectBIM 已经在 setUp 中 mock 为 false
        when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(anyLong(), anyMap())).thenReturn(false);

        // Act
        // 中文注释：执行被测方法
        ToolPageResult result = coohomDiyPageHandler.handleNewDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // Assert
        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码应为 OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        // 中文注释：验证响应体包含项目阶段信息 (来自 finalizeH5ModelAndRespond)
        assertNotNull("响应体不应为空", result.getBody());
        assertTrue("响应体应包含项目阶段信息", result.getBody().toString().contains("\"projectStage\":\"test-stage\""));
        // 中文注释：验证响应体包含VRC配置信息 (来自 finalizeH5ModelAndRespond)
        assertTrue("响应体应包含VRC配置信息", result.getBody().toString().contains("\"vrcConfig\":\"test-vrc-config\""));

        // 中文注释：验证核心的父类填充方法被调用
        verify(coohomDiyPageHandler).fillUserInfo(eq(DEFAULT_USER_ID), any(H5Model.class));
        verify(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        verify(coohomDiyPageHandler).fillAccountInfo(eq(DEFAULT_USER_ID), eq(DEFAULT_ROOT_ACCOUNT_ID), any(H5Model.class));
        // fillPlanInfo 是静态的，其效果通过JSON内容验证
        // 对于新建设计， obsDesignId 传入 finalizeH5ModelAndRespond 为 null，DesignInfo 也为空
        // 但 fillPlanInfo 仍然会基于 param 的值被调用
    }

    // --- handleExistingDesign Tests ---

    @Test
    public void testHandleExistingDesign_AuthFailed() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(DEFAULT_USER_ID, null, DEFAULT_DESIGN_ID, false)).thenReturn(false);

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体是否为预期的重定向标识
        assertEquals("响应体应为默认页面重定向标识", "redirectDefaultPage_invoked", result.getBody());
        // 中文注释：验证重定向到默认页面的方法被调用
        verify(coohomDiyPageHandler).redirectDefaultPage(request);
    }

    @Test
    public void testHandleExistingDesign_BimPlan_RedirectsToCoohomBim() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType(MOCK_BIM_PLAN_TYPE_FF); // 使用模拟常量
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        // 中文注释：显式 mock redirectCoohomBIMPage 的行为，覆盖 setUp 中的通用 mock (如果需要特定返回值)
        // doReturn(ResponseEntity.ok("coohomBimRedirect_invoked")).when(coohomDiyPageHandler).redirectCoohomBIMPage(request);

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, true);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体是否为预期的BIM重定向标识
        assertEquals("响应体应为BIM页面重定向标识", "coohomBimRedirect_invoked", result.getBody());
        // 中文注释：验证重定向到Coohom BIM页面的方法被调用
        verify(coohomDiyPageHandler).redirectCoohomBIMPage(request);
    }

    @Test
    public void testHandleExistingDesign_PhotoStudioPlan_ReturnsNotFound() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType(MOCK_PHOTO_STUDIO_PLAN_TYPE); // 使用模拟常量
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证返回 404 状态码的 ToolPageResult
        assertNotNull("响应实体不应为空", result);
        assertEquals("状态码应为 NOT_FOUND", HttpStatus.NOT_FOUND.value(), result.getStatusCode());
        assertEquals("返回消息应为影棚方案提示", "影棚方案请到营销工具后台查看", result.getBody());
    }
    
    @Test
    public void testHandleExistingDesign_UpdateBackupPlan_ReturnsNotFound() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType(MOCK_UPDATE_BACKUP_PLAN_TYPE); // 使用模拟常量
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证返回 404 状态码的 ToolPageResult
        assertNotNull("响应实体不应为空", result);
        assertEquals("状态码应为 NOT_FOUND", HttpStatus.NOT_FOUND.value(), result.getStatusCode());
        assertEquals("返回消息应为升级备份方案提示", "升级备份方案不支持直接打开", result.getBody());
    }


    @Test
    public void testHandleExistingDesign_ForceUpdate() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        param.setForceUpdate(true);
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0); // 普通方案
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc("someVrc");
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);

        // 中文注释：显式 mock redirectUpdateUrl 和 redirectByUrl 的行为
        // String updateUrl = "http://update.url"; // setUp 中 mockUpdateUrl 返回 "mockUpdateUrl"
        // doReturn(updateUrl).when(coohomDiyPageHandler).redirectUpdateUrl(request, DEFAULT_OBS_DESIGN_ID);
        // doReturn(ResponseEntity.ok("redirected:" + updateUrl)).when(coohomDiyPageHandler).redirectByUrl(updateUrl);

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, true); // bimAccess = true for forceUpdate

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体是否为预期的重定向标识 (来自setUp中对redirectByUrl的mock)
        assertEquals("响应体应为redirectByUrl的调用标识", "redirectByUrl_invoked", result.getBody());
        // 中文注释：验证 redirectUpdateUrl 方法被以正确的参数调用
        verify(coohomDiyPageHandler).redirectUpdateUrl(request, DEFAULT_OBS_DESIGN_ID);
        // 中文注释：验证 redirectByUrl 方法被以 redirectUpdateUrl 的返回值调用
        verify(coohomDiyPageHandler).redirectByUrl("mockUpdateUrl");
    }

    @Test
    public void testHandleExistingDesign_TreUpdate() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        param.setTre(Arrays.asList(TreEnum.UPDATE_BIM.getTreId(), "otherTre"));
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0); // 普通方案
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc("someVrc");
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);
        
        // 中文注释：显式 mock redirectUpdateUrlWithTre 和 redirectByUrl 的行为
        // String updateUrlWithTre = "http://update.url/withTre"; // setUp 中 mockUpdateUrlWithTre 返回 "mockUpdateUrlWithTre"
        // doReturn(updateUrlWithTre).when(coohomDiyPageHandler).redirectUpdateUrlWithTre(eq(request), eq(DEFAULT_DESIGN_ID), eq(Arrays.asList("otherTre")));
        // doReturn(ResponseEntity.ok("redirected:" + updateUrlWithTre)).when(coohomDiyPageHandler).redirectByUrl(updateUrlWithTre);


        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, true); // bimAccess = true for treUpdate

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体是否为预期的重定向标识
        assertEquals("响应体应为redirectByUrl的调用标识", "redirectByUrl_invoked", result.getBody());
        // 中文注释：验证 redirectUpdateUrlWithTre 方法被以正确的参数调用
        verify(coohomDiyPageHandler).redirectUpdateUrlWithTre(request, DEFAULT_DESIGN_ID, Arrays.asList("otherTre"));
        // 中文注释：验证 redirectByUrl 方法被以 redirectUpdateUrlWithTre 的返回值调用
        verify(coohomDiyPageHandler).redirectByUrl("mockUpdateUrlWithTre");
    }

    @Test
    public void testHandleExistingDesign_VrcUpdate_Success() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), anyLong(), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0); // 普通方案
        diyDesignInfo.setPlanId(999L);
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ProjectDesign projectDesign = new ProjectDesign();
        String initialVrc = VrcEnum.YUNTU_FPI.getCode(); // VRC需要更新
        projectDesign.setVrc(initialVrc);
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);

        String newVrc = "newVrcCode";
        when(vrcAppIdDataDb.getVrcByVtypeAndAppId("V0140", Math.toIntExact(DEFAULT_APP_ID_NUM),
                null)).thenReturn(newVrc);
        
        // 中文注释：确保不走其他提前重定向的逻辑
        when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(anyLong(), anyMap())).thenReturn(false);
        // checkUpgradeAccess 已在 setUp 中 mock 为 false

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码为OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        // 中文注释：验证响应体中包含更新后的VRC
    }


    @Test
    public void testHandleExistingDesign_NormalFlow() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID);
        param.setPlanName("普通流程方案"); // 为 fillPlanInfo 提供参数
        param.setPlanType((byte)0);

        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), eq(DEFAULT_DESIGN_ID), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0); // 普通方案
        diyDesignInfo.setPlanId(999L);
        diyDesignInfo.setName("普通流程方案");
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);

        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc("V0140R0401"); // VRC 不需要更新
        projectDesign.setCopyLogParentId(111);
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);

        // 确保不会提前重定向
        when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(anyLong(), anyMap())).thenReturn(false);
        // checkUpgradeAccess 已在 setUp 中 mock 为 false

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码应为 OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        String responseBody = result.getBody().toString();
        // 中文注释：验证响应体包含预期的设计ID
        assertTrue("响应体应包含设计ID", responseBody.contains("\"obsDesignId\":\"" + DEFAULT_OBS_DESIGN_ID + "\""));
        // 中文注释：验证响应体包含预期的方案名称 (来自diyDesignInfo)
        assertTrue("响应体应包含方案名称", responseBody.contains("\"planName\":\"普通流程方案\""));
        // 中文注释：验证响应体包含预期的VRC
        assertTrue("响应体应包含VRC", responseBody.contains("\"vrc\":\"V0140R0401\""));
        // 中文注释：验证响应体包含父方案ID的加密值（如果存在）
        assertTrue("响应体应包含父方案ID", responseBody.contains("\"obsParentId\":\"")); 
        // 中文注释：验证响应体包含planInfoConfig中的planName (来自param)
        // 注意：实际的H5Model JSON结构可能更复杂，这里仅做简单包含性检查
        // 如果 AbstractDiyPageHandler.fillPlanInfo 使用了 param.getPlanName(), 则应该在json中出现
        // assertTrue("响应体应包含来自param的planName", responseBody.contains("\"planInfoConfig\":{\"planName\":\"普通流程方案\""));

        // 中文注释：验证核心的父类填充方法被调用
        verify(coohomDiyPageHandler).fillUserInfo(eq(DEFAULT_USER_ID), any(H5Model.class));
        verify(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        verify(coohomDiyPageHandler).fillAccountInfo(eq(DEFAULT_USER_ID), eq(DEFAULT_ROOT_ACCOUNT_ID), any(H5Model.class));
        // fillPlanInfo 是静态方法，不能在 spy 实例上直接 verify。其效果应通过验证 H5Model JSON 内容来体现。
        verify(projectDesignFacadeDb, never()).updateVrc(anyLong(), anyString());
    }

    // --- handleNewDesign Tests ---

    @Test
    public void testHandleNewDesign_BimApp_RedirectsToCoohomBim() throws Exception {
        param.setObsDesignId(null); // New design
        // 中文注释：为了触发 AppType.isBimApp(appIdNum) 返回 true，我们需要控制 appIdNum 或者 mock AppType。
        // 由于 AppType.isBimApp 是静态方法且难以直接mock，我们将测试另一个分支：bimAccess && param.isRedirectBim() && needRedirectBIM()
        // 如果要专门测试 AppType.isBimApp, 可能需要重构 AppType 使其可测试，或者传入一个 appIdNum 已知会使 AppType.isBimApp 返回 true 的值。
        // 这里我们先测试第二个可控的BIM重定向条件

        param.setRedirectBim(true);
        // businessAccessService.checkBimAccess 由方法参数 bimAccess 控制，这里传入 true
        doReturn(true).when(coohomDiyPageHandler).needRedirectBIM(request, DEFAULT_USER_ID, DEFAULT_ROOT_ACCOUNT_ID);


        ToolPageResult result = coohomDiyPageHandler.handleNewDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, true); // bimAccess = true

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体是否为预期的BIM重定向标识
        assertEquals("响应体应为BIM页面重定向标识", "coohomBimRedirect_invoked", result.getBody());
        // 中文注释：验证重定向到Coohom BIM页面的方法被调用
        verify(coohomDiyPageHandler).redirectCoohomBIMPage(request);
    }

    @Test
    public void testHandleNewDesign_NormalFlow() throws Exception {
        param.setObsDesignId(null); // New design
        param.setRedirectBim(false); // 不会触发BIM重定向
        param.setPlanName("新普通流程方案");
        param.setPlanType((byte)11);

        // 中文注释：确保不走BIM重定向，bimAccess设为false
        // needRedirectBIM 已经在 setUp 中 mock 为 false

        // 中文注释：期望正常走到最后返回H5Model JSON
        when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(anyLong(), anyMap())).thenReturn(false); // 确保不走回收逻辑

        ToolPageResult result = coohomDiyPageHandler.handleNewDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码应为 OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        String responseBody = result.getBody().toString();
        // 中文注释：验证响应体包含项目阶段信息
        assertTrue("响应体应包含项目阶段信息", responseBody.contains("\"projectStage\":\"test-stage\""));
        // 中文注释：验证响应体包含VRC配置信息
        assertTrue("响应体应包含VRC配置信息", responseBody.contains("\"vrcConfig\":\"test-vrc-config\""));
        // 中文注释：验证响应体中包含来自param的planName (通过fillPlanInfo设置到H5Model的planInfoConfig中)
        // assertTrue("响应体应包含planInfoConfig中的planName", responseBody.contains("\"planName\":\"新普通流程方案\""));
        // 中文注释：验证响应体中包含来自param的planType
        // assertTrue("响应体应包含planInfoConfig中的planType", responseBody.contains("\"planType\":" + (byte)11));

        // 中文注释：验证核心的父类填充方法被调用
        verify(coohomDiyPageHandler).fillUserInfo(eq(DEFAULT_USER_ID), any(H5Model.class));
        verify(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        // fillPlanInfo 是静态方法，不能在 spy 实例上直接 verify。其效果应通过验证 H5Model JSON 内容来体现。
        // 中文注释：确认没有发生BIM重定向
        verify(coohomDiyPageHandler, never()).redirectCoohomBIMPage(request);
    }

    // --- finalizeH5ModelAndRespond related tests (indirectly tested via handleExistingDesign and handleNewDesign) ---


    @Test
    public void testFinalizeH5Model_RecycleDiyAuth_NewDesign_RedirectsToCoohomBim() throws Exception {
        param.setObsDesignId(null); // New design
        param.setRedirectBim(false);

        Map<String, String> abTestConfig = new HashMap<>(); // Empty or non-triggering for upgrade
        lenient().when(saaSConfigService.getAbTestResult(anyString(), anyString(), anyLong(), anyString())).thenReturn(abTestConfig);
        lenient().when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(DEFAULT_USER_ID, abTestConfig)).thenReturn(true); // 触发回收

        // doReturn(ResponseEntity.ok("coohomBimRedirect_invoked")).when(coohomDiyPageHandler).redirectCoohomBIMPage(request); // setUp中已mock
        

        ToolPageResult result = coohomDiyPageHandler.handleNewDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体为预期的BIM重定向标识 (finalizeH5ModelAndRespond 中会调用 redirectCoohomBIMPage)
        assertEquals("响应体应为BIM页面重定向标识", "coohomBimRedirect_invoked", result.getBody());
        // 中文注释：验证重定向到Coohom BIM页面的方法被调用
        verify(coohomDiyPageHandler).redirectCoohomBIMPage(request);
    }

    @Test
    public void testFinalizeH5Model_RecycleDiyAuth_ExistingDesign_EnablePageOpen_RedirectsToUpdate() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID); // Existing design
        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), eq(DEFAULT_DESIGN_ID), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0);
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc("vrcNoUpgrade");
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);


        Map<String, String> abTestConfig = new HashMap<>();
        lenient().when(saaSConfigService.getAbTestResult(anyString(), anyString(), anyLong(), anyString())).thenReturn(abTestConfig);
        lenient().when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(DEFAULT_USER_ID, abTestConfig)).thenReturn(true); // 触发回收
        when(toadProperties.isEnablePageOpenRecycleDiy()).thenReturn(true); // 开启已有方案回收时跳转更新

        // String updateUrl = "http://update.url/recycle"; // setUp中mockUpdateUrl返回 "mockUpdateUrl"
        // doReturn(updateUrl).when(coohomDiyPageHandler).redirectUpdateUrl(request, DEFAULT_OBS_DESIGN_ID);
        // doReturn(ResponseEntity.ok("redirected:" + updateUrl)).when(coohomDiyPageHandler).redirectByUrl(updateUrl);
        // checkUpgradeAccess 已在 setUp 中 mock 为 false
        // doReturn(false).when(coohomDiyPageHandler).checkUpgradeAccess(any(), any(), any());


        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应体为预期的重定向标识
        assertEquals("响应体应为redirectByUrl的调用标识", "redirectByUrl_invoked", result.getBody());
        // 中文注释：验证生成更新URL的方法被调用
        verify(coohomDiyPageHandler).redirectUpdateUrl(request, DEFAULT_OBS_DESIGN_ID);
        // 中文注释：验证通过URL重定向的方法被调用
        verify(coohomDiyPageHandler).redirectByUrl("mockUpdateUrl");
    }
    
    @Test
    public void testFinalizeH5Model_RecycleDiyAuth_ExistingDesign_DisablePageOpen_ReturnsOk() throws Exception {
        param.setObsDesignId(DEFAULT_OBS_DESIGN_ID); // Existing design
        param.setPlanName("回收不跳转方案");
        param.setPlanType((byte)0);

        when(authCheckService.checkAuthFromDesignId(anyLong(), any(), eq(DEFAULT_DESIGN_ID), anyBoolean())).thenReturn(true);

        DiyDesignInfo diyDesignInfo = new DiyDesignInfo();
        diyDesignInfo.setDesignId(DEFAULT_DESIGN_ID);
        diyDesignInfo.setPlanType((byte) 0);
        diyDesignInfo.setPlanId(999L);
        diyDesignInfo.setName("回收不跳转方案");
        when(projectDesignFacadeDb.getDiyDesignInfo(DEFAULT_DESIGN_ID)).thenReturn(diyDesignInfo);
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc("vrcNoUpgradeNoRecycleRedirect");
        when(projectDesignFacadeDb.getVrcAndParentId(DEFAULT_DESIGN_ID)).thenReturn(projectDesign);


        Map<String, String> abTestConfig = new HashMap<>();
        lenient().when(saaSConfigService.getAbTestResult(anyString(), anyString(), anyLong(), anyString())).thenReturn(abTestConfig);
        lenient().when(saaSConfigService.checkRecycleDiyAuthWithAbConfig(DEFAULT_USER_ID, abTestConfig)).thenReturn(true); // 触发回收
        when(toadProperties.isEnablePageOpenRecycleDiy()).thenReturn(false); // 关闭已有方案回收时跳转更新
        // checkUpgradeAccess 已在 setUp 中 mock 为 false

        ToolPageResult result = coohomDiyPageHandler.handleExistingDesign(request, response, param, DEFAULT_ACTUAL_APP_ID, DEFAULT_APP_ID_NUM, DEFAULT_ROOT_ACCOUNT_ID, false);

        // 中文注释：验证响应不为空
        assertNotNull("响应结果不应为空", result);
        // 中文注释：验证响应状态码应为 OK
        assertEquals("响应状态码应为 OK", HttpStatus.OK.value(), result.getStatusCode());
        // 中文注释：确认返回的是H5Model内容，例如包含方案名
        assertTrue("响应体应包含方案名称", result.getBody().toString().contains("\"planName\":\"回收不跳转方案\""));
        // 中文注释：验证未发生更新重定向
        verify(coohomDiyPageHandler, never()).redirectUpdateUrl(any(), any());
        // 中文注释：验证未发生BIM重定向
        verify(coohomDiyPageHandler, never()).redirectCoohomBIMPage(any());
        // 中文注释：验证核心的父类填充方法被调用
        verify(coohomDiyPageHandler).fillUserInfo(eq(DEFAULT_USER_ID), any(H5Model.class));
        verify(coohomDiyPageHandler).fillToolConfig(any(H5Model.class));
        // fillPlanInfo 是静态方法，不能在 spy 实例上直接 verify。其效果应通过验证 H5Model JSON 内容来体现。
    }
}