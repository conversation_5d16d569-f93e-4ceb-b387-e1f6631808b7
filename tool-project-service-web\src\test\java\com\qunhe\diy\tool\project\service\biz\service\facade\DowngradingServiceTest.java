/*
 * DowngradingServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.project.service.client.client.DowngradingClient;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectLevelDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DowngradingServiceTest {

    @Mock
    private DowngradingClient mockDowngradingClient;

    @InjectMocks
    private DowngradingService downgradingServiceUnderTest;

    @Test
    public void testRefreshSearchData() {
        // Setup
        // Run the test
        downgradingServiceUnderTest.refreshSearchData(Collections.singletonList("value"));

        // Verify the results
        verify(mockDowngradingClient).refresh(Collections.singletonList("value"));
    }

    @Test
    public void testAddHotData() {
        // Setup
        final ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .name("name")
                .build();
        final ToolProjectHomeDesign toolProjectHomeDesign = ToolProjectHomeDesign.builder()
                .designId(0L)
                .planId(0L)
                .levels(Collections.singletonList(ToolProjectLevelDesign.builder()
                        .levelId("levelId")
                        .build()))
                .build();

        // Run the test
        downgradingServiceUnderTest.addHotData(toolProjectSaveParam, toolProjectHomeDesign, 0L);

        // Verify the results
        verify(mockDowngradingClient).refresh(anyList());
    }
}
