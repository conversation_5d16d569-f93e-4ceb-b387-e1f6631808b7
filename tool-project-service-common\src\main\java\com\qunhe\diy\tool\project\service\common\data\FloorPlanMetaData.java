/*
 * FloorPlanMetaData.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Function:
 *
 * <AUTHOR>
 * 2024/5/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FloorPlanMetaData {

    /**
     * planId
     */
    private Integer planId;

    /**
     * 方案对应的单位制Id
     */
    private Byte unitId;

    /**
     * 导入图后户型透明度设置
     */
    private Double alphaSetting;

    /**
     * 是否显示底图
     */
    private Boolean imgVisible;

    /**
     * 用户处于的设计阶段，0-户型阶段，1-装修阶段
     */
    private Byte designStage;

    /**
     * 备用字段
     */
    private String reverse02;

    /**
     * 上次打开的levelId,表字段:reverse03
     */
    private String lastOpenedLevelId;

    /**
     * 对应备用字段 reverse04
     * 用于标识门窗的施工图新旧版本
     */
    private String dwDrawingVersion;

    /**
     *
     */
    private Timestamp created;

    /**
     *
     */
    private Timestamp lastmodified;

    /**
     * 户型配置项
     */
    private String configuration;

}

