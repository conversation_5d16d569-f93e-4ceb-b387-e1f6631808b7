/*
 * HomeDesignFacadeDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectLevelDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.client.HomeDesignClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.middleware.toad.client.common.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class HomeDesignFacadeDbTest {

    @Mock
    HomeDesignClient homeDesignClient;
    @InjectMocks
    HomeDesignFacadeDb homeDesignFacadeDb;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetOrCreateHomeDesign() throws DiyManageServiceException {
        // Setup
        final HomeDesignData expectedResult = new HomeDesignData();
        expectedResult.setId(0L);
        expectedResult.setDesignId(0L);
        expectedResult.setUndergroundLevels(Collections.singletonList("value"));
        expectedResult.setOvergroundLevels(Collections.singletonList("value"));
        final LevelInfo levelInfo = new LevelInfo();
        expectedResult.setLevelInfos(Collections.singletonList(levelInfo));
        when(homeDesignClient.getOrCreateHomeDesign(0L, 0L, 0L)).thenReturn(expectedResult);

        // Run the test
        final HomeDesignData result = homeDesignFacadeDb.getOrCreateHomeDesign(0L, 0L, 0L);

        // Verify the results
        assertEquals("result not as expected", result, expectedResult);
    }

    @Test
    public void testGetOrCreateHomeDesign_returnException() throws DiyManageServiceException {
        // Setup
        when(homeDesignClient.getOrCreateHomeDesign(0L, 0L, 0L))
                .thenThrow(new DiyManageServiceException("homeDesign get fail"));

        // Run the test
        final HomeDesignData result = homeDesignFacadeDb.getOrCreateHomeDesign(0L, 0L, 0L);

        // Verify the results
        assertNull("getOrCreateHomeDesign return null, result not null", result);
    }

    @Test
    public void testGenerateLevelDesign() {
        HomeDesignData homeDesignData = new HomeDesignData();
        List<String> underGroundLevels = new ArrayList<>();
        underGroundLevels.add("MKGEZWNMDSPJCAABAAAAAAI8");
        underGroundLevels.add("MKFVDVNMDSJH2AABAAAAAAY8");
        List<String> overGroundLevels = new ArrayList<>();
        overGroundLevels.add("MKFUALFMDSJH2AABAAAAAEI8");
        overGroundLevels.add("MJMNI4FMDSDHQAABAAAAAAI8");
        homeDesignData.setUndergroundLevels(underGroundLevels);
        homeDesignData.setOvergroundLevels(overGroundLevels);
        List<ToolProjectLevelDesign> toolProjectLevelDesigns =
                homeDesignFacadeDb.generateLevelDesign(homeDesignData);
        assertEquals("should have 4 levels", toolProjectLevelDesigns.size(), 4);
    }

    @Test
    public void testGetHomeDesign() {
        // Setup
        final HomeDesignData expectedResult = new HomeDesignData();
        expectedResult.setId(0L);
        expectedResult.setDesignId(0L);
        expectedResult.setUndergroundLevels(Collections.singletonList("value"));
        expectedResult.setOvergroundLevels(Collections.singletonList("value"));
        final LevelInfo levelInfo = new LevelInfo();
        expectedResult.setLevelInfos(Collections.singletonList(levelInfo));
        when(homeDesignClient.getHomeDesign(0L)).thenReturn(expectedResult);

        // Run the test
        final HomeDesignData result = homeDesignFacadeDb.getHomeDesign(0L);

        // Verify the results
        assertEquals("getHomeDesign not as expected", result, expectedResult);
    }

    @Test
    public void testSaveHomeDesign() {
        // Setup
        final HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setId(0L);
        homeDesignData.setDesignId(0L);
        homeDesignData.setUndergroundLevels(Collections.singletonList("value"));
        homeDesignData.setOvergroundLevels(Collections.singletonList("value"));
        final LevelInfo levelInfo = new LevelInfo();
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));

        // Run the test
        final boolean result = homeDesignFacadeDb.saveHomeDesign(homeDesignData);

        // Verify the results
        assertFalse("save homeDesign return true", result);
    }

    @Test
    public void testBatchGetHomeDesignData() throws Exception {
        // Setup
        List<Long> designIds = Collections.singletonList(1L);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setId(1L);
        homeDesignData.setDesignId(1L);

        List<HomeDesignData> mockResponse = Collections.singletonList(homeDesignData);
        when(homeDesignClient.batchGetHomeDesignData(JsonUtils.objectToJson(designIds)))
                .thenReturn(mockResponse);

        // Run the test
        Map<Long, HomeDesignData> result = homeDesignFacadeDb.batchGetHomeDesignData(designIds);

        // Verify the results
        assertEquals("应返回一个元素", 1, result.size());
        assertEquals("应返回正确的设计数据", homeDesignData, result.get(1L));
    }

    @Test
    public void testCreateHomeDesign() throws Exception {
        // Setup
        ProjectSequence projectSequence = new ProjectSequence();
        projectSequence.setPlanId(1L);
        projectSequence.setDesignId(2L);

        ToolProjectSaveParam saveParam = new ToolProjectSaveParam();
        saveParam.setUserId(3L);
        saveParam.setLevelName("测试楼层");

        HomeDesignData expectedResult = new HomeDesignData();
        expectedResult.setId(1L);
        expectedResult.setDesignId(2L);

        when(homeDesignClient.getOrCreateHomeDesign(
                projectSequence.getPlanId(),
                projectSequence.getDesignId(),
                saveParam.getUserId(),
                saveParam.getLevelName()))
                .thenReturn(expectedResult);

        // Run the test
        HomeDesignData result = homeDesignFacadeDb.createHomeDesign(projectSequence, saveParam);

        // Verify the results
        assertEquals("应返回正确的设计数据", expectedResult, result);
    }

    @Test
    public void testCopyHomeDesignIgnoreRecycles() throws Exception {
        // Setup
        ProjectDesign srcProjectDesign = new ProjectDesign();
        srcProjectDesign.setPlanId(1L);
        srcProjectDesign.setDesignId(2L);

        ToolProjectHomeDesign dstProjectDesign = new ToolProjectHomeDesign();
        dstProjectDesign.setPlanId(3L);
        dstProjectDesign.setDesignId(4L);

        HomeDesignData expectedResult = new HomeDesignData();
        expectedResult.setId(4L);
        expectedResult.setDesignId(4L);

        when(homeDesignClient.copyHomeDesignIgnoreRecycles(
                srcProjectDesign.getPlanId(),
                srcProjectDesign.getDesignId(),
                dstProjectDesign.getPlanId(),
                dstProjectDesign.getDesignId()))
                .thenReturn(expectedResult);

        // Run the test
        HomeDesignData result = homeDesignFacadeDb.copyHomeDesignIgnoreRecycles(
                srcProjectDesign, dstProjectDesign);

        // Verify the results
        assertEquals("应返回正确的设计数据", expectedResult, result);
    }

    @Test(expected = DiyManageServiceException.class)
    public void testCopyHomeDesignFallBack() throws Exception {
        // Setup
        ProjectDesign srcProjectDesign = new ProjectDesign();
        srcProjectDesign.setPlanId(1L);
        srcProjectDesign.setDesignId(2L);

        ToolProjectHomeDesign dstProjectDesign = new ToolProjectHomeDesign();
        dstProjectDesign.setPlanId(3L);
        dstProjectDesign.setDesignId(4L);

        BlockException blockException = Mockito.mock(BlockException.class);

        // Run the test - should throw exception
        homeDesignFacadeDb.copyHomeDesignFallBack(srcProjectDesign, dstProjectDesign,
                blockException);
    }

    @Test
    public void testBatchCreateOrUpdateLevels() throws Exception {
        // Setup
        Long designId = 1L;
        List<LevelInfo> levelInfos = new ArrayList<>();
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("test-level");
        levelInfos.add(levelInfo);

        LevelBatchUpdateResponse expectedResponse = new LevelBatchUpdateResponse();
        expectedResponse.setStatus(0);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setLevelInfos(levelInfos);
        expectedResponse.setHomeDesignData(homeDesignData);

        when(homeDesignClient.batchCreateOrUpdateLevels(designId, levelInfos))
                .thenReturn(expectedResponse);

        // Run the test
        LevelBatchUpdateResponse result = homeDesignFacadeDb.batchCreateOrUpdateLevels(designId,
                levelInfos);

        // Verify the results
        assertEquals("状态应该匹配", expectedResponse.getStatus(), result.getStatus());
        assertEquals("HomeDesignData应该匹配", expectedResponse.getHomeDesignData(),
                result.getHomeDesignData());
    }

    @Test
    public void testGenerateLevelDesign_WithEmptyLists() {
        // Setup
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setUndergroundLevels(new ArrayList<>());
        homeDesignData.setOvergroundLevels(new ArrayList<>());

        // Run the test
        List<ToolProjectLevelDesign> result = homeDesignFacadeDb.generateLevelDesign(
                homeDesignData);

        // Verify the results
        assertEquals("空列表应该返回空结果", 0, result.size());
    }

    @Test
    public void testGenerateLevelDesign_OnlyUnderground() {
        // Setup
        HomeDesignData homeDesignData = new HomeDesignData();
        List<String> underGroundLevels = Arrays.asList("level-1", "level-2");
        homeDesignData.setUndergroundLevels(underGroundLevels);
        homeDesignData.setOvergroundLevels(new ArrayList<>());

        // Run the test
        List<ToolProjectLevelDesign> result = homeDesignFacadeDb.generateLevelDesign(
                homeDesignData);

        // Verify the results
        assertEquals("应该有两个地下楼层", 2, result.size());
        assertEquals("地下楼层应该有负索引", -2, result.get(0).getIndex());
        assertEquals("地下楼层应该有负索引", -1, result.get(1).getIndex());
    }

    @Test
    public void testGenerateLevelDesign_OnlyOverground() {
        // Setup
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setUndergroundLevels(new ArrayList<>());
        List<String> overGroundLevels = Arrays.asList("level-1", "level-2");
        homeDesignData.setOvergroundLevels(overGroundLevels);

        // Run the test
        List<ToolProjectLevelDesign> result = homeDesignFacadeDb.generateLevelDesign(
                homeDesignData);

        // Verify the results
        assertEquals("应该有两个地上楼层", 2, result.size());
        assertEquals("地上楼层应该有正索引", 1, result.get(0).getIndex());
        assertEquals("地上楼层应该有正索引", 2, result.get(1).getIndex());
    }
}

