/*
 * DesignMetaDataMapper.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("DesignMetaDataMapper")
public interface DesignMetaDataMapper {

    /**
     * 获取上一次打开的levelId
     *
     * @param planId planId
     * @return levelId
     */
    String getLastOpenedLevelId(@Param("planId") final Long planId);

    /**
     * 更新上次打开的levelId
     *
     * @param planId            planId
     * @param lastOpenedLevelId LastOpenedLevelId
     * @return 是否成功
     */
    int updateLastOpenedLevelId(@Param("planId") Long planId,
            @Param("lastOpenedLevelId") String lastOpenedLevelId);

    /**
     * 保存上次打开的levelId
     *
     * @param planId            planId
     * @param lastOpenedLevelId levelId
     * @return 是否成功
     */
    int insertLastOpenedLevelId(@Param("planId") final Long planId,
            @Param("lastOpenedLevelId") String lastOpenedLevelId);

}
