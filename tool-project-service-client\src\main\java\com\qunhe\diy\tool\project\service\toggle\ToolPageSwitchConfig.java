/*
 * ToolPageSwitchConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.toggle;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2025/5/20
 */
@Configuration
@Import({ToolPageSwitch.class})
public class ToolPageSwitchConfig {


}
