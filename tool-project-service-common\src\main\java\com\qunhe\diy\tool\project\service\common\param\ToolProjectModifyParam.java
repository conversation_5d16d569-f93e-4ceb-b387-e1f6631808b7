/*
 * ToolProjectEditParam.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qunhe.diybe.dms.data.PlanType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * 迁移/api/design/info
 * 文档：https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80566488166&kpm=JPBL.9467584bfbf79df2.23c83dc.1673259206226
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolProjectModifyParam {
    /**
     * 方案名
     */
    private String name;
    private String obsDesignId;
    private String areaId;
    private String obsPlanId;
    private String commName;
    /**
     * 关联标准户型
     */
    private String obsStdCommId;

    private PlanType.DesignType designType = PlanType.DesignType.UNKNOWN;
}
