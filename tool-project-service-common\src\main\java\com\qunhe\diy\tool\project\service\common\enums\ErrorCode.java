/*
 *  ErrorCode.java
 *  Copyright 2022 Qunhe Tech, all rights reserved.
 *  Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.common.enums;

import com.qunhe.diy.tool.project.service.common.exception.ToolProjectBizzException;
import com.qunhe.web.standard.data.GeneralError;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */
public enum ErrorCode implements GeneralError {

    UNDEFINED_ERROR("50T00000", "未定义异常"),
    NOT_LOGIN("50T00001", "用户未登录"),
    PARAM_ERROR("50T00101", "参数异常"),
    USER_ERROR("50T00102", "用户异常"),
    CREATE_ERROR("50T00001", "方案创建失败"),
    CREATE_HOME_ERROR("50T00002", "方案创建失败"),
    COPY_GET_PROJECT_ERROR("50T00003", "原方案获取失败"),
    GET_PROJECT_LEVEL_ERROR("50T00004", "方案楼层信息获取失败"),
    ROLLBACK_PROJECT_ERROR("50T00005", "方案回滚失败"),
    CREATE_PROJECT_COUNT_ERROR("50T00006", "方案数量超过限制"),
    PLAN_TYPE_INVALID_ERROR("50T00007", "复制方案指定planType不合法"),
    CREATE_PROJECT_FREQUENCY_BEYONG_LIMIT("50T00008", "方案创建频率超过限制"),
    BATCH_DESIGN_ID_LIMIT("50T00009", "designId数量超过限制"),
    TRIAL_USER_FORBIDDEN("50T00010", "游客用户禁止操作"),
    COMMUNITY_CREATE_ERROR("50T00011", "社区信息创建失败"),
    BATCH_OPERATION_PARAM_INVALID("50T00012", "批量操作参数异常"),
    GET_BATCH_OPERATION_STATUS_ERROR("50T00013", "批量操作状态获取失败"),
    BATCH_OPERATION_ORIGIN_COUNT_EMPTY("50T00014", "批量操作需要处理的方案为空"),

    BATCH_OPERATION_PARAM_OVER_LIMIT("50T00015", "批量操作参数数量超过限制"),

    // 承载页逻辑模块，第三位数字为模块编号
    USER_CONFIG_FETCH_FAILED("50T00100", "用户配置获取失败"),

    ;

    @Getter
    private final String code;

    @Getter
    private final String msg;

    ErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ErrorCode getErrorCodeByCode(String code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return ErrorCode.UNDEFINED_ERROR;
    }

    public ToolProjectBizzException asException() {
        return new ToolProjectBizzException()
                .setCode(code)
                .setDesc(msg)
                .setStatus(HttpStatus.OK);
    }

}
