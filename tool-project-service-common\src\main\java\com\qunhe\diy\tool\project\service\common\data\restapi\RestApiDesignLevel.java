/*
 * RestApiDesign.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data.restapi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RestApiDesignLevel {

    private String levelId;

    private String designId;

    /**
     * 楼层序号
     */
    private Integer index;

    /**
     * 楼层名称
     */
    private String name;

    /**
     * 套内使用面积
     */
    private Double realArea;

    /**
     * 套内建筑面积
     */
    private Double ibArea;

    /**
     * 建筑外框面积
     */
    private Double sourceArea;

    /**
     * 户外面积
     */
    private Double outdoorArea;

    /**
     * 户型图
     */
    private String planPic;


    private Long created;

    private Long lastModifiedTime;

}
