/*
 * RestApiDesign.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data.restapi;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RestApiDesign {

    private String designId;

    private String planId;

    private String ownerAppUid;

    private String name;

    private String coverPic;

    private String designDesc;

    /**
     * 楼盘名称
     */
    private String communityName;

    /**
     * 省市区完整名称
     */
    private String distinctFullName;

    private RestApiDesignStatus status;


    private Long createdTime;

    private Long modifiedTime;

    /**
     * 楼层数据
     */
    private List<RestApiDesignLevel> levels;

}
