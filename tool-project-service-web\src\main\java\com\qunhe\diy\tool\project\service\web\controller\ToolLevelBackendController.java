/*
 * ToolLevelBackendController.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import com.qunhe.assembly.oplog.annotation.DiyPrint;
import com.qunhe.assembly.oplog.annotation.OpLogAnalyze;
import com.qunhe.assembly.oplog.enums.PrintTypeEnum;
import com.qunhe.diy.tool.project.service.biz.annotation.LevelId;
import com.qunhe.diy.tool.project.service.biz.annotation.MultiLevelRevision;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.ToolLevelService;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelDesignCreateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.instdeco.plan.annotations.DesignId;
import com.qunhe.instdeco.plan.annotations.PlanId;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.security.PreAuthorize;
import com.qunhe.utils.uniqueid.NotEnoughUniqueIdException;
import com.qunhe.utils.uniqueid.UniqueIdGenerationUnrecoverableException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
@RestController
@RequiredArgsConstructor
public class ToolLevelBackendController {

    public static final QHLogger LOG = QHLogger.getLogger(ToolLevelBackendController.class);

    private final ToolLevelService toolLevelService;
    private final HomeDesignFacadeDb homeDesignFacadeDb;

    @MultiLevelRevision(designId = "#designId")
    @PostMapping(value = ApiConstant.PROJECT_LEVEL_BATCH_UPDATE)
    @PreAuthorize("hasWrite(#designId)")
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "batchCreateOrUpdateLevels",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designId"))
    public LevelBatchUpdateResponse batchCreateOrUpdateLevels(
            @RequestParam final Long designId,
            @RequestBody final List<LevelInfo> levelInfos)
            throws DiyManageServiceException {
        return homeDesignFacadeDb.batchCreateOrUpdateLevels(designId, levelInfos);
    }

    @MultiLevelRevision(planId = "#planId", designId = "#designId")
    @PostMapping(ApiConstant.PROJECT_LEVEL_CREATE)
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "createLevel",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS}, opTargetKey = "#designId"))
    public LevelDesignCreateResponse createLevel(
            @PlanId @RequestParam(value = "planid", required = false) final Long planId,
            @DesignId @RequestParam(value = "designid", required = false) final Long designId,
            @RequestParam(value = "userid") final Long userId,
            @RequestParam(value = "upward", defaultValue = "true") final Boolean upward,
            @RequestParam(value = "insert", defaultValue = "false") final Boolean insert,
            @RequestParam(value = "levelid", required = false) final String currentLevelId,
            @RequestParam(value = "levelname", required = false) final String levelName,
            final HttpServletResponse response) {
        if (planId == null && designId == null) {
            LOG.message("createLevelDesign - both planId and designId are null")
                    .warn();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        }
        if (Boolean.TRUE.equals(insert) && StringUtils.isEmpty(currentLevelId)) {
            LOG.message("createLevelDesign - insert mode required currentLevelId")
                    .warn();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        }
        HomeDesignData homeDesign = homeDesignFacadeDb.getOrCreateHomeDesign(planId, designId, userId);
        if (homeDesign == null) {
            LOG.message("createLevelDesign - homeDesign is null")
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .warn();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        }
        if (CollectionUtils.isEmpty(homeDesign.getLevelInfos())) {
            return new LevelDesignCreateResponse(
                    LevelDesignCreateResponse.CreateStatus.LEVEL_NO_EXIST.getStatus(), null, null);
        }
        try {
            return toolLevelService.createLevel(
                    homeDesign, userId, upward, insert, currentLevelId, levelName);
        } catch (final UniqueIdGenerationUnrecoverableException | NotEnoughUniqueIdException e) {
            LOG.message("createLevelDesign", e)
                    .with("planid", planId)
                    .error();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;
        }
    }

    @MultiLevelRevision(planId = "#planId", designId = "#designId")
    @PostMapping(ApiConstant.PROJECT_LEVEL_UPDATE)
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "updateLevel",
            diyPrint = @DiyPrint(printType = {PrintTypeEnum.ALL_ARGS}, opTargetKey = "#designId"))
    public Boolean updateLevel(
            @PlanId @RequestParam(value = "planid", required = false) final Long planId,
            @DesignId @RequestParam(value = "designid", required = false) final Long designId,
            @RequestParam(value = "userid", required = false) final Long userId,
            @RequestBody final List<LevelInfo> levelInfos,
            final HttpServletResponse response) {
        boolean paramCheck = levelInfos == null || (planId == null && designId == null);
        if (paramCheck) {
            LOG.message("updateLevelInfos - homeDesignData or designId and planId is null")
                    .with("levelInfos", levelInfos)
                    .with("designId", designId)
                    .with("planId", planId)
                    .warn();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        }
        return toolLevelService.updateLevelInfos(levelInfos, planId, designId, userId);
    }

    @MultiLevelRevision(levelId = "#levelId")
    @DeleteMapping(ApiConstant.PROJECT_LEVEL_DELETE)
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "deleteLevel",
            diyPrint = @DiyPrint(printType = {PrintTypeEnum.ALL_ARGS}, opTargetKey = "#levelId"))
    public Boolean deleteLevel(
            @LevelId @RequestParam("levelid") final String levelId,
            @RequestParam("userid") final Long userId,
            final HttpServletResponse response) {
        if (levelId == null || userId == null) {
            LOG.message("deleteLevel - invalid params")
                    .with("levelId", levelId)
                    .with("userId", userId)
                    .warn();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        }
        return toolLevelService.deleteLevel(levelId, userId);
    }

    @MultiLevelRevision(designId = "#designId", levelId = "#levelId")
    @PostMapping(ApiConstant.PROJECT_LEVEL_RECOVER)
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "recoverLevel",
            diyPrint = @DiyPrint(printType = {PrintTypeEnum.ALL_ARGS}, opTargetKey = "#levelId"))
    public Boolean recoverLevel(
            @DesignId @RequestParam(value = "designid") final Long designId,
            @LevelId @RequestParam(value = "levelid") final String levelId,
            @RequestParam(value = "userid") final Long userId,
            @RequestParam(value = "levelindex") final Integer levelIndex,
            @RequestParam(value = "levelname", required = false) final String levelName) {
        return toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
    }

}
