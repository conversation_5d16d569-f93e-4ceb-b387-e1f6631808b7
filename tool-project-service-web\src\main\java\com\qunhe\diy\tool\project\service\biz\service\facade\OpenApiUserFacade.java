/*
 * OpenApiExternalService.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.instdeco.plan.openapi.client.PartnerUserMappingClient;
import com.qunhe.instdeco.plan.openapi.data.PartnerUserMappingRelation;
import com.qunhe.instdeco.plan.openapi.exception.OpenApiClientException;
import com.qunhe.log.QHLogger;
import com.qunhe.web.standard.exception.BizzException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
public class OpenApiUserFacade {

    private static final QHLogger LOG = QHLogger.getLogger(OpenApiUserFacade.class);

    private final PartnerUserMappingClient partnerUserMappingClient;

    @SentinelResource(value = "getAppUidByUserIds")
    @SneakyThrows({OpenApiClientException.class, BizzException.class})
    public Map<Long, String> getAppUidByUserIds(List<Long> userIds) {
        List<PartnerUserMappingRelation> relations =
                partnerUserMappingClient.getPartnerUserMappingRelationsByUserIds(userIds);
        return relations.stream()
                .collect(
                        Collectors.toMap(
                                PartnerUserMappingRelation::getUserId,
                                PartnerUserMappingRelation::getAppUid, (a,b)->a));
    }


}
