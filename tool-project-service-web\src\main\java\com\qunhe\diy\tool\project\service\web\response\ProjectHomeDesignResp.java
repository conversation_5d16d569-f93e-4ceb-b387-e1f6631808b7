/*
 * ProjectHomeDesign.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.response;

import com.qunhe.diy.tool.project.service.common.data.ToolProjectLevelDesign;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectHomeDesignResp {

    private String obsDesignId;
    private String obsId;
    private List<ToolProjectLevelDesign> levels;
}
