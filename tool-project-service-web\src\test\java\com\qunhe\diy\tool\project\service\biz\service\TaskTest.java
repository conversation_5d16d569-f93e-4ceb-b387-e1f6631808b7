/*
 * TaskTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverFailDetail;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TaskTest {
    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    ProjectAuthService projectAuthService;
    @Mock
    List<HandoverFailDetail> failDetails;

    Task task;

    @Before
    public void setUp() {
        Long fromUserId = 111L;
        Long designId = 35338021L;
        Long toUserId = 222L;
        task = new Task(projectDesignFacadeDb, projectAuthService, fromUserId, toUserId, designId, failDetails);
    }

    @Test
    public void testCall() {
        when(projectDesignFacadeDb.switchDesignUser(anyLong(), anyLong(), anyLong())).thenReturn(true);

        Long result = task.call();
        Assert.assertEquals("design id is equal", result.longValue(), 35338021L);
    }
}