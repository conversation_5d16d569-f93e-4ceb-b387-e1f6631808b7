/*
 * KujialeDiyPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.util.PlanTypeUtil;
import com.qunhe.diy.tool.project.service.web.context.ToolType;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DesignInfo;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.FlashCustomizeSetting;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.diy.AppType;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diy.tool.project.service.web.page.diy.TreEnum;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.hunter.util.IpUtil;
import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.instdeco.plan.graylaunchmw.data.UserInfoData;
import com.qunhe.instdeco.plan.util.HttpUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.stream.Collectors;

import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.ALLOW;
import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.YUN_TU_PERSSION;

/**
 * <AUTHOR>
 */
@Component
public class KujialeDiyPageHandler extends AbstractDiyPageHandler {

    private static final QHLogger LOG = QHLogger.getLogger(KujialeDiyPageHandler.class);

    public KujialeDiyPageHandler(UserInfoFacade userInfoFacade, BusinessAccountDb businessAccountDb,
            BusinessAccessService businessAccessService,
            ProjectDesignFacadeDb projectDesignFacadeDb, VrcAppIdDataDb vrcAppIdDataDb,
            SessionConflictHandler sessionConflictHandler, GrayLunchService grayLunchService,
            ToadProperties toadProperties, UserDb userDb, SaaSConfigService saaSConfigService,
            ToolLinkConfigCache toolLinkConfigCache, SaasConfigFacade saasConfigFacade,
            AuthCheckService authCheckService) {
        super(userInfoFacade, businessAccountDb, businessAccessService, projectDesignFacadeDb,
                vrcAppIdDataDb, sessionConflictHandler, grayLunchService, toadProperties, userDb,
                saaSConfigService, toolLinkConfigCache, saasConfigFacade, authCheckService);
    }

    @Override
    protected ToolPageResult handleExistingDesign(HttpServletRequest request,
            HttpServletResponse response, DiyPageParam param, String actualAppId, Long appIdNum,
            Long rootAccountId, boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException {
        Long userId = param.getUserId();
        final String obsDesignId = param.getObsDesignId();
        final H5Model h5Model = new H5Model();
        h5Model.setBimAccess(bimAccess);

        long designId = LongCipher.DEFAULT.decrypt(obsDesignId);

        // 1. 权限校验
        if (!checkAuth(userId, param, designId)) {
            return redirectDefaultPage(request);
        }

        // 2. 查询设计信息
        final DiyDesignInfo diyDesignInfo = projectDesignFacadeDb.getDiyDesignInfo(designId);
        final DesignInfo designInfo = new DesignInfo();
        designInfo.setObsDesignId(obsDesignId);

        if (diyDesignInfo != null) {
            ProjectDesign projectDesign = projectDesignFacadeDb.getVrcAndParentId(
                    diyDesignInfo.getDesignId());

            // 3. BIM 跳转
            if (isBIMPlan(diyDesignInfo)) {
                return redirectToBIMPage(request);
            }

            // 4. BIM 强制更新
            ToolPageResult forceUpdateResp = handleBimForceUpdate(param, bimAccess,
                    diyDesignInfo, request, obsDesignId);
            if (forceUpdateResp != null) {
                return forceUpdateResp;
            }

            // 5. 404 场景
            if (isPhotoStudioOrUpdateBackupPlan(diyDesignInfo)) {
                return ToolPageResult.create(org.springframework.http.HttpStatus.NOT_FOUND, null, null);
            }

            // 6. Session 冲突
            ToolPageResult sessionConflictResp = handleSessionConflict(designId, userId);
            if (sessionConflictResp != null) {
                return sessionConflictResp;
            }

            // 7. VRC 处理
            String vrc = handleVrcUpdateIfNeeded(projectDesign, diyDesignInfo, appIdNum, param,
                    actualAppId, userId);
            h5Model.setVrc(vrc);

            // 8. 填充 DesignInfo
            fillDesignInfo(designInfo, diyDesignInfo, param, projectDesign);
        }
        h5Model.setDesignInfo(designInfo);

        // 9. 返回最终响应
        return finalizeH5ModelAndRespond(request, param, userId, rootAccountId, h5Model,
                obsDesignId);
    }

    @Override
    protected ToolPageResult handleNewDesign(HttpServletRequest request,
            HttpServletResponse response, DiyPageParam param, String actualAppId, Long appIdNum,
            Long rootAccountId, boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException {
        Long userId = param.getUserId();
        final H5Model h5Model = new H5Model();
        h5Model.setBimAccess(bimAccess);

        if (AppType.isBimApp(appIdNum)) {
            LOG.message("KujialeHandler: New design, BIM App type, redirecting to Kujiale BIM" +
                    " page.").with("appIdNum", appIdNum).with("userId", userId).info();
            return redirectToBIMPage(request);
        }
        if (bimAccess && param.isRedirectBim() && needRedirectBIM(request, userId, rootAccountId)) {
            LOG.message("KujialeHandler: New design, BIM access and redirectBim=true, " +
                    "redirecting to Kujiale BIM page.").with("userId", userId).info();
            return redirectToBIMPage(request);
        }

        return finalizeH5ModelAndRespond(request, param, userId, rootAccountId, h5Model, null);
    }

    private ToolPageResult finalizeH5ModelAndRespond(HttpServletRequest request,
            DiyPageParam param, Long userId, Long rootAccountId, H5Model h5Model,
            String obsDesignId) throws IOException {
        LOG.message("KujialeHandler: Finalizing H5Model.").with("userId", userId).with("DesignId",
                obsDesignId).with("ToolName", param.getToolName()).with("Referer",
                HttpUtil.getReferer(request)).info();

        final UserInfoData userInfoData = new UserInfoData();
        userInfoData.setRootAccountId(rootAccountId);
        userInfoData.setUserId(userId);
        final boolean yunTuPermission = grayLunchService.checkVersion(YUN_TU_PERSSION, ALLOW,
                new HttpRequestMetaData(request, userInfoData));

        h5Model.setProjectStage(toadProperties.getProjectStage());
        h5Model.setRedirectUrl(param.getRedirectUrl());
        h5Model.setStayDiy(param.getStayDiy());

        fillUserInfo(userId, h5Model);

        h5Model.setStage(param.getStage());
        h5Model.setVrcConfig(toadProperties.getVrcConfig());
        h5Model.setIsYunTuUser(yunTuPermission);
        h5Model.setAbTestConfig(saaSConfigService.getAbTestResult(YUN_DESIGN_APP_KEY,
                request.getHeader(COOKIE_HEADER_KEY), userId, IpUtil.getUserIp(request)));

        if (obsDesignId != null && h5Model.getDesignInfo() != null) {
            final String upgradeSuperFloorPlanAb = h5Model.getAbTestConfig().get(
                    UPGRADE_SUPER_FLOORPLAN);
            final String upgradePublicDecorationAb = h5Model.getAbTestConfig().get(
                    UPGRADE_PUBLIC_DECORATION);
            if (checkUpgradeAccess(upgradeSuperFloorPlanAb, upgradePublicDecorationAb,
                    h5Model.getVrc())) {
                LOG.message("KujialeHandler: AB test upgrade access.").with("designId", obsDesignId)
                        .with("userId", userId).info();
                return redirectByUrl(
                        redirectSuperOrPublicUpgradeUrl(obsDesignId, upgradeSuperFloorPlanAb,
                                upgradePublicDecorationAb, request));
            }
        }

        if (saaSConfigService.checkRecycleDiyAuthWithAbConfig(userId, h5Model.getAbTestConfig())) {
            if (obsDesignId == null) {
                LOG.message("KujialeHandler: Recycle DIY Auth (AB), new design, redirecting " +
                        "to Kujiale BIM page.").with("userId", userId).info();
                return redirectToBIMPage(request);
            } else {
                if (toadProperties.isEnablePageOpenRecycleDiy()) {
                    LOG.message("KujialeHandler: Recycle DIY Auth (AB), existing design, " +
                            "redirecting to update URL.").with("designId", obsDesignId).with(
                            "userId", userId).info();
                    return redirectByUrl(redirectUpdateUrl(request, obsDesignId));
                }
            }
        }

        fillToolConfig(h5Model);
        fillFavorIcon(userId, h5Model);
        fillAccountInfo(userId, rootAccountId, h5Model);
        fillPlanInfo(param.getObsSrcPlanId(), param.getPlanName(), param.getAskId(),
                param.getPlanType(), h5Model);

        if (CooperateHelper.isCooperate()) {
            h5Model.setObsOwnerUserId(
                    LongCipher.DEFAULT.encrypt(CooperateHelper.getCooperateOwner()));
        }
        final FlashCustomizeSetting customerService = new FlashCustomizeSetting();
        customerService.setShow(true);
        h5Model.setCustomerServiceJson(JsonMapper.writeValueAsString(customerService));

        h5Model.setRedirectDecoration(useNewDecorationVersion(request));
        LOG.message("KujialeHandler: Returning H5Model:").with("H5Model", h5Model.toString())
                .debug();
        return ToolPageResult.success(JsonMapper.writeValueAsString(h5Model));
    }

    // ========== 以下为抽取的私有方法 ========== //
    private boolean checkAuth(Long userId, DiyPageParam param, long designId) {
        if (!authCheckService.checkAuthFromDesignId(userId, param.getOrderDesignId(), designId,
                param.isCooperate())) {
            LOG.message("KujialeHandler: Auth check failed for existing design.").with("userId",
                    userId).with("designId", designId).warn();
            return false;
        }
        return true;
    }

    private boolean isBIMPlan(DiyDesignInfo diyDesignInfo) {
        if (PlanTypeUtil.isBIMPlan(diyDesignInfo.getPlanType())) {
            LOG.message("KujialeHandler: BIM Plan, redirecting to Kujiale BIM page.").with(
                    "designId", diyDesignInfo.getDesignId()).with("userId",
                    diyDesignInfo.getUserId()).info();
            return true;
        }
        return false;
    }

    private ToolPageResult handleBimForceUpdate(DiyPageParam param, boolean bimAccess,
            DiyDesignInfo diyDesignInfo, HttpServletRequest request, String obsDesignId) {
        List<String> tre = param.getTre();
        if (bimAccess) {
            if (param.isForceUpdate()) {
                LOG.message("KujialeHandler: Force update plan.").with("designId",
                                diyDesignInfo.getDesignId()).with("userId",
                                diyDesignInfo.getUserId())
                        .info();
                return redirectByUrl(redirectUpdateUrl(request, obsDesignId));
            } else if (tre != null && tre.contains(TreEnum.UPDATE_BIM.getTreId())) {
                LOG.message("KujialeHandler: TRE force update plan.").with("designId",
                                diyDesignInfo.getDesignId()).with("userId",
                                diyDesignInfo.getUserId())
                        .info();
                final List<String> tres = tre.stream().filter(
                        t -> !TreEnum.UPDATE_BIM.getTreId().equals(t)).collect(Collectors.toList());
                return redirectByUrl(
                        redirectUpdateUrlWithTre(request, diyDesignInfo.getDesignId(), tres));
            }
        }
        return null;
    }

    private boolean isPhotoStudioOrUpdateBackupPlan(DiyDesignInfo diyDesignInfo) {
        if (PlanTypeUtil.isPhotoStudioPlan(diyDesignInfo.getPlanType()) ||
                PlanTypeUtil.isUpdateBackupPlan(diyDesignInfo.getPlanType())) {
            LOG.message("KujialeHandler: Photo studio or update backup plan, returning 404.").with(
                    "designId", diyDesignInfo.getDesignId()).with("userId",
                    diyDesignInfo.getUserId()).info();
            return true;
        }
        return false;
    }

    private ToolPageResult handleSessionConflict(long designId, Long userId) {
        URI redirectUri = sessionConflictHandler.checkSessionConflict(designId, userId,
                ToolTypeContextHolder.getToolType() == ToolType.FOP_ORDER);
        if (redirectUri != null) {
            LOG.message("KujialeHandler: Session conflict detected, redirecting.").with("designId",
                    designId).with("userId", userId).info();
            return ToolPageResult.redirect(redirectUri.toString());
        }
        return null;
    }

    private String handleVrcUpdateIfNeeded(ProjectDesign projectDesign, DiyDesignInfo diyDesignInfo,
            Long appIdNum, DiyPageParam param, String actualAppId, Long userId) {
        String vrc = projectDesign.getVrc();
        if (VrcEnum.YUNTU_FPI.getCode().equals(vrc)) {
            final int index = vrc.indexOf('R');
            final String dstVrcCode = vrcAppIdDataDb.getVrcByVtypeAndAppId(vrc.substring(0, index),
                    Math.toIntExact(appIdNum), System.getenv(APP_STAGE));
            if (org.apache.commons.lang3.StringUtils.isEmpty(dstVrcCode)) {
                LOG.message("KujialeHandler: Invalid VRC or AppID for VRC update.").with("VRC", vrc)
                        .with("ObsAppId", param.getObsAppId()).with("ActualAppId", actualAppId)
                        .with("userId", userId).error();
                return vrc;
            }
            vrc = dstVrcCode;
            projectDesignFacadeDb.updateVrc(diyDesignInfo.getPlanId(), vrc);
            LOG.message("KujialeHandler: VRC updated successfully.").with("dstVrcCode", dstVrcCode)
                    .with("DesignId", diyDesignInfo.getDesignId()).with("userId", userId).info();
        }
        return vrc;
    }

    private void fillDesignInfo(DesignInfo designInfo, DiyDesignInfo diyDesignInfo,
            DiyPageParam param, ProjectDesign projectDesign) {
        designInfo.setObsPlanId(LongCipher.DEFAULT.encrypt(diyDesignInfo.getPlanId()));
        if (param.getLevelId() == null) {
            designInfo.setLevelId(diyDesignInfo.generateLevelId());
        } else {
            designInfo.setLevelId(param.getLevelId());
        }
        designInfo.setPlanName(diyDesignInfo.getName());
        Integer copyParentId = projectDesign.getCopyLogParentId();
        if (copyParentId != null) {
            designInfo.setObsParentId(LongCipher.DEFAULT.encrypt(Long.valueOf(copyParentId)));
        }
    }
} 