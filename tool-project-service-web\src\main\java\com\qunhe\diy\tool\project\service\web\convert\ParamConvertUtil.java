/*
 * ProjectDesignConvertUtil.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.convert;

import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.common.constant.FloorPlanSourceConstants;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.enums.ProjectBusinessEnum;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectBackendController;
import com.qunhe.diy.tool.project.service.web.request.ToolProjectSaveRequest;
import com.qunhe.diy.tool.project.service.web.response.ProjectHomeDesignResp;
import com.qunhe.diybe.module.restapi.core.util.ObsValueCheckUtil;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ParamConvertUtil {

    private final static QHLogger LOG = QHLogger.getLogger(ParamConvertUtil.class);

    private final static long MAX_10_DIGIT_NUMBER = (long) Math.pow(10, 10) - 1;

    public static void validRequest(final ToolProjectSaveRequest toolProjectSaveRequest,
            boolean userRangeCheck) throws ToolProjectCreateException {
        if (toolProjectSaveRequest.getObsUserId() == null) {
            if (UserDb.getUserIdBySession() == null) {
                throw new ToolProjectCreateException(ErrorCode.USER_ERROR);
            }
            toolProjectSaveRequest.setObsUserId(LongCipher.DEFAULT.encrypt(UserDb.getUserIdBySession()));
        } else {
            if (!ObsValueCheckUtil.isValid(toolProjectSaveRequest.getObsUserId())) {
                LOG.message("invalid obs user id")
                        .with("targetUserId", toolProjectSaveRequest.getObsUserId())
                        .with("userId", UserDb.getUserIdBySession())
                        .error();
                throw new ToolProjectCreateException(ErrorCode.USER_ERROR);
            }
            long targetUserId = LongCipher.DEFAULT.decrypt(toolProjectSaveRequest.getObsUserId());
            // userId 字段在 db 各种表中长度只有10位，往后传会有问题。这里先拦截，后续userid字段修改类型，这里需要改
            if (targetUserId <= 0 || (userRangeCheck && targetUserId > MAX_10_DIGIT_NUMBER)) {
                LOG.message("invalid user id")
                        .with("targetUserId", targetUserId)
                        .with("userId", UserDb.getUserIdBySession())
                        .error();
                throw new ToolProjectCreateException(ErrorCode.USER_ERROR);
            }
        }
    }


    public static ToolProjectSaveParam convert(final ToolProjectSaveRequest toolProjectSaveRequest) {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setProjectBusinessEnum(ProjectBusinessEnum.FRONT_NEW);
        toolProjectSaveParam.setUserId(LongCipher.DEFAULT.decrypt(toolProjectSaveRequest.getObsUserId()));
        if (StringUtils.isNotBlank(toolProjectSaveRequest.getObsCommId())) {
            toolProjectSaveParam.setCommId(
                    LongCipher.DEFAULT.decrypt(toolProjectSaveRequest.getObsCommId()));
        }
        toolProjectSaveParam.setPlanType(toolProjectSaveRequest.getPlanType());
        toolProjectSaveParam.setVrc(ToolTypeContextHolder.getVrcHolder().get());
        toolProjectSaveParam.setName(toolProjectSaveRequest.getName());
        toolProjectSaveParam.setRecommend(toolProjectSaveRequest.getRecommend());
        toolProjectSaveParam.setReworked(toolProjectSaveRequest.getReworked());
        toolProjectSaveParam.setRealArea(toolProjectSaveRequest.getRealArea());
        toolProjectSaveParam.setArea(toolProjectSaveRequest.getArea());
        toolProjectSaveParam.setSrcArea(toolProjectSaveRequest.getSourceArea());
        toolProjectSaveParam.setSpecId(toolProjectSaveRequest.getSpecId());
        toolProjectSaveParam.setCreatedAppId(toolProjectSaveRequest.getCreatedAppId());


        if (Objects.equals(toolProjectSaveRequest.getCadImported(), true)) {
            toolProjectSaveParam.setSourceId(FloorPlanSourceConstants.CAD_IMPORT_SOURCE);
        } else if (Objects.equals(toolProjectSaveRequest.getTutorial(), true)) {
            toolProjectSaveParam.setSourceId(FloorPlanSourceConstants.TUTORIAL_PLAN_SOURCE);
        } else if (toolProjectSaveRequest.getDarenIncomingId() != null) {
            toolProjectSaveParam.setSourceId(FloorPlanSourceConstants.DAREN_IMPORT_SOURCE);
            toolProjectSaveParam.setDarenIncomingId(toolProjectSaveRequest.getDarenIncomingId());
        }
        if (toolProjectSaveParam.getSourceId() == null && toolProjectSaveRequest.getImgUrl() != null) {
            toolProjectSaveParam.setUploadPics(toolProjectSaveRequest.getImgUrl());
        }
        toolProjectSaveParam.setLevelName(toolProjectSaveRequest.getLevelName());
        return toolProjectSaveParam;
    }

    public static ProjectHomeDesignResp convert2Response(
            final ToolProjectHomeDesign toolProjectHomeDesign) {
        ProjectHomeDesignResp resp = new ProjectHomeDesignResp();
        if (toolProjectHomeDesign != null) {
            resp.setObsDesignId(LongCipher.DEFAULT.encrypt(toolProjectHomeDesign.getDesignId()));
            resp.setObsId(LongCipher.DEFAULT.encrypt(toolProjectHomeDesign.getPlanId()));
            resp.setLevels(toolProjectHomeDesign.getLevels());
        }
        return resp;
    }
}
