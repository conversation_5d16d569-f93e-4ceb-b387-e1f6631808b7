/*
 * BusinessAccessService.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.qunhe.diy.tool.project.service.biz.util.ProxyExtractor;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.saas.design.client.DesignBimSwitchClient;
import com.qunhe.utils.HunterLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BusinessAccessService implements ProxyExtractor<BusinessAccessService> {
    private static final HunterLogger LOG = HunterLogger.getLogger(BusinessAccessService.class);

    @Autowired
    private com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient
            commercialAccessPointClient;

    @Autowired
    private DesignBimSwitchClient designBimSwitchClient;


    public Boolean checkBimAccess(final Long userId) {
        try {
            return designBimSwitchClient.getBimAccess(userId).getD().getBimAccess();
        } catch (final Exception e) {
            LOG.message("checkBimAccess failed")
                    .with(e)
                    .warn();
            return false;
        }
    }

    /**
     * 校验权限点
     */
    public Boolean checkAccess(final Long userId, final Long accessPoint) {
        try {
            final List<Long> points = getProxy().getAccessPoints(
                    Collections.singletonList(accessPoint), userId);
            return CollectionUtils.isNotEmpty(points) && points.contains(accessPoint);
        } catch (final Exception e) {
            // 捕获异常，避免影响核心链路
            LOG.message("checkAccess failed")
                    .with(e)
                    .warn();
            return false;
        }
    }

    @SentinelResource(value = "getAccessPoints")
    public List<Long> getAccessPoints(final List<Long> inputAccessPoints, final Long userId)
            throws AccessAuthenticatorException {
        if (CollectionUtils.isEmpty(inputAccessPoints) || userId == null) {
            return Collections.emptyList();
        }
        final com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody requestBody =
                new com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody();

        // 只需要设置以下2个参数，权限ID列表、用户ID
        requestBody.setAccessPoints(inputAccessPoints);
        requestBody.setUserId(userId);

        // 调用成功，获取过滤后的权限ID列表，表示当前用户在指定的权限ID列表中，所拥有的权限列表
        final List<Long> output = commercialAccessPointClient.filterAccessPoint(requestBody);
        LOG.message("access points")
                .with("in-points", inputAccessPoints)
                .with("out-points", JSON.toJSONString(output))
                .info();
        return output;
    }
}
