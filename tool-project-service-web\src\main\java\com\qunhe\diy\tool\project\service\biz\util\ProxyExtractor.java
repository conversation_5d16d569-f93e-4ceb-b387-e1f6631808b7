/*
 * ProxyExtractor.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.qunhe.log.QHLogger;
import org.springframework.aop.framework.AopContext;

/**
 * <AUTHOR>
 *
 */
public interface ProxyExtractor<T> {
    QHLogger LOG = QHLogger.getLogger(ProxyExtractor.class);
    /**
     * Get proxy class with AOP
     * @return proxy
     */
    @SuppressWarnings("unchecked")
    default T getProxy() {
        try {
            return (T) AopContext.currentProxy();
        } catch (final IllegalStateException e) {
            LOG.message("getProxy error", e).error();
            return (T) this;
        }
    }
}
