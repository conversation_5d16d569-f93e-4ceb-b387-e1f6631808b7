/*
 * ToolPageService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.handler.CoohomBimPageHandler;
import com.qunhe.diy.tool.project.service.web.page.handler.KujialeBimPageHandler;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@RequiredArgsConstructor
public class BimToolPageService {

    private static final QHLogger LOG = QHLogger.getLogger(BimToolPageService.class);

    private final TrialUserFacade trialUserFacade;

    private final KujialeBimPageHandler kujialePageHandler;

    private final CoohomBimPageHandler coohomPageHandler;


    @SentinelResource(value = "handlePageForBim")
    public ToolPageResult handlePageForBim(HttpServletRequest request, BimPageParam param)
            throws AccessAuthenticatorException {

        LOG.message("handle diy page")
                .with("param", param)
                .with("queryString", request.getQueryString())
                .info();

        // 如果不是游客，且方案是游客创建，则修正为游客更新
        String obsDesignId = param.getObsDesignId();
        boolean isTrialUser = param.isTrialUser();
        if (StringUtil.isNotEmpty(obsDesignId)) {
            long designId = LongCipher.DEFAULT.decrypt(obsDesignId);
            if (!isTrialUser && trialUserFacade.isTrialDesign(designId)) {
                trialUserFacade.markTrialDesignUpdated(designId);
                LOG.message("update trial design status to updated")
                        .with("designId", designId)
                        .info();
            }
        }
        return handlePage(request, param);
    }

    public ToolPageResult handlePage(HttpServletRequest request, BimPageParam param)
            throws AccessAuthenticatorException {
        if (param.isFromCoohom()) {
            return coohomPageHandler.handlePage(request, param);
        } else {
            return kujialePageHandler.handlePage(request, param);
        }
    }

    @SentinelResource(value = "handlePageForTrial")
    public ToolPageResult handlePageForTrial(HttpServletRequest request, BimPageParam param)
            throws AccessAuthenticatorException {
        // 如果是游客，且方案不是游客方案，则重定向到5.0工具
        String obsDesignId = param.getObsDesignId();
        if (StringUtil.isNotEmpty(obsDesignId)) {
            boolean isTrialUser = param.isTrialUser();
            if (isTrialUser && !trialUserFacade.isTrialDesign(
                    LongCipher.DEFAULT.decrypt(obsDesignId))) {
                // 创建一个新的参数对象，保留原始请求中的必要参数
                BimPageParam newParam = BimPageParam.builder()
                        .userId(param.getUserId())
                        .build();
                return handlePage(request, newParam);
            }
        }
        return handlePage(request, param);
    }


}
