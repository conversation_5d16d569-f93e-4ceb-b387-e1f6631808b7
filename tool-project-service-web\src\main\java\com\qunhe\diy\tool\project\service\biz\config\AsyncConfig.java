/*
 * AsyncConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.qunhe.hunter.async.HunterTaskDecorator;
import com.qunhe.log.QHLogger;

/**
 * Function: 自定义默认线程池
 *
 * <AUTHOR>
 */
@EnableAsync
@Configuration
public class AsyncConfig {

    private static final QHLogger LOG = QHLogger.getLogger(AsyncConfig.class);

    @Bean(name = AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public Executor taskExecutor(@Value("${qunhe.tps.taskExecutor.corePoolSize:10}") int corePoolSize,
            @Value("${qunhe.tps.taskExecutor.maxPoolSize:20}") int maxPoolSize,
            @Value("${qunhe.tps.taskExecutor.capacity:200}") int queueSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueSize);
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.setRejectedExecutionHandler((r, e) -> LOG.message(
                "taskExecutor thread pool is full, discard task").error());
        executor.setThreadFactory(new ThreadFactoryBuilder()
                .setUncaughtExceptionHandler((t, e) -> LOG.message("taskExecutor uncaught " +
                        "exception", e).error())
                .setNameFormat("taskExecutor-%d").build());
        executor.setTaskDecorator(new HunterTaskDecorator());
        return executor;
    }
}

