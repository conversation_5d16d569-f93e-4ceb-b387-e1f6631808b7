/*
 * UserDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.log.QHLogger;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.usergrowth.uic.rpc.client.UicUserInfoClient;
import com.qunhe.usergrowth.uic.rpc.exception.UicApiException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class UserDbTest {
    @Mock
    QHLogger LOG;
    @Mock
    HttpServletRequest request;
    @InjectMocks
    UserDb userDb;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetUserIdBySession2() {
        RequestAttributes attributes = new ServletRequestAttributes(request);
        attributes.setAttribute("cooperate",Integer.valueOf(0),0);
        RequestContextHolder.setRequestAttributes(attributes,false);
        when(CooperateHelper.isCooperate()).thenReturn(true);
        Long result = UserDb.getUserIdBySession(true);
        Assert.assertEquals(null, null, result);
    }

    @Test
    public void testGetUser() {
        UicUserInfoClient uicUserInfoClient = mock(UicUserInfoClient.class);
        doThrow(new UicApiException("0","testGetUser抛UicApiException异常")).when(uicUserInfoClient).getUserById(anyLong());
        UserDto result = userDb.getUser(1111251217L);
        Assert.assertEquals(null,null, result);
    }
}