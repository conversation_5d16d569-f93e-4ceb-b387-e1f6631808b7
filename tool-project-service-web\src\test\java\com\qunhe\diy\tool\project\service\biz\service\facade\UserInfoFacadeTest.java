/*
 * UserInfoFacadeTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import static com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade.COOHOM_USER_TAG;
import static com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade.COOHOM_USER_TAGS;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.util.collections.Sets;
import org.mockito.junit.MockitoJUnitRunner;

import com.qunhe.usergrowth.uic.data.dto.tag.UserTagMatchParamDto;
import com.qunhe.usergrowth.uic.rpc.client.UserTagClient;
import org.springframework.aop.framework.ProxyHelper;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@RunWith(MockitoJUnitRunner.class)
public class UserInfoFacadeTest {

    @Mock
    UserTagClient userTagClient;

    @InjectMocks
    UserInfoFacade userInfoFacade;

    @Test
    public void isCoohomUser() {
        ProxyHelper.setProxy(userInfoFacade);

        Long userId = 22L;
        Mockito.when(userTagClient.matchUserTags(new UserTagMatchParamDto(userId, COOHOM_USER_TAGS))).thenReturn(Sets.newSet(COOHOM_USER_TAG));
        boolean coohomUser = userInfoFacade.isCoohomUser(userId);
        Assert.assertTrue("coohom user when match tag", coohomUser);
        Mockito.when(userTagClient.matchUserTags(new UserTagMatchParamDto(userId, COOHOM_USER_TAGS))).thenReturn(Sets.newSet());
        coohomUser = userInfoFacade.isCoohomUser(userId);
        Assert.assertFalse("coohom user when not match tag", coohomUser);
    }

}