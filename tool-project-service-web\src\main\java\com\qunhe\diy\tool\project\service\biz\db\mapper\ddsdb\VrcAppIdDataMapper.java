/*
 * VrcAppIdDataMapper.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb;

import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface VrcAppIdDataMapper {


    List<VrcAppIdData> getAllVrcAppIdList(@Param("stage") final String stage);

    /**
     * 根据Vtype和AppId映射唯一的vrc
     * @return
     */
    String getVrcByVtypeAndAppId(@Param("vType") final String vType,
            @Param("appId") final Integer appId, @Param("stage") final String stage);

    /**
     * 新增vrc和appid关系
     * @return
     */
    boolean saveVrcAppIdData(VrcAppIdData vrcAppidData);

    int updateVrcAppIdData(VrcAppIdData vrcAppidData);

    boolean deleteVrcAppIdData(@Param("vType") final String vType,
            @Param("appId") final Integer appId, @Param("stage") final String stage);

    VrcAppIdData getByVTypeAndAppId(@Param("vType") final String vType,
            @Param("appId") final Integer appId, @Param("stage") final String stage);
}
