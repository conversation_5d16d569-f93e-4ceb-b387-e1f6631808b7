/*
 * AbstractDiyPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.FavorIcon;
import com.qunhe.diy.tool.project.service.web.page.data.FlashCustomizeSetting;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.data.PlanHelpConfig;
import com.qunhe.diy.tool.project.service.web.page.data.PlanInfo;
import com.qunhe.diy.tool.project.service.web.page.data.PlanInfoConfig;
import com.qunhe.diy.tool.project.service.web.page.data.RootAccount;
import com.qunhe.diy.tool.project.service.web.page.data.ToolJsConfig;
import com.qunhe.diy.tool.project.service.web.page.data.User;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.instdeco.businessaccount.sdk.data.TobBusinessAccount;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfig;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.instdeco.plan.graylaunchmw.data.UserInfoData;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.eclipse.jetty.util.UrlEncoded;
import org.jetbrains.annotations.NotNull;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE;
import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants.ALLOW;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public abstract class AbstractDiyPageHandler {

    private static final QHLogger LOG = QHLogger.getLogger(AbstractDiyPageHandler.class);

    // Constants
    public static final String COOHOM_BIM_PATH = "/pub/tool/bim/cloud";
    public static final String BIM_PATH = "/tool/h5/bim";
    public static final String PREFIX_UPGRADE_PATH = "/pub/tool/yundesign/upgrade5";
    public static final String UPGRADE_PATH = PREFIX_UPGRADE_PATH + "?designId=";
    protected static final String APP_STAGE = "APP_STAGE";
    public static final String YUN_DESIGN_APP_KEY = "yunDesign";
    public static final String COOKIE_HEADER_KEY = "qhdi";
    public static final String DECORATION_SERVICE_NEW_VERSION = "new";
    public static final String COOHOM = "coohom";
    public static final String H5_PAGE_CLEAN_PATH = "/tool/h5/diy";
    protected static final Integer UPGRADE_AUTHORIZED_FAILED = 0;
    protected static final Integer UPGRADE_AUTHORIZED_SUCCESS = 1;
    protected static final String FALSE = "false";
    protected static final String TRUE = "true";
    protected static final String UPGRADE_SUPER_FLOORPLAN = "upgradeSuperFloorPlan";
    protected static final String UPGRADE_PUBLIC_DECORATION = "upgradePublicDecoration";
    public static final String DECORATION_SERVICE_NAME = "decoration";
    private static final String I18N_HEADER = "x-qh-i18n";
    private static final Pattern PATTERN = Pattern.compile("designid=3(?:F|f)\\w{10}");
    private static final int SPLIT_LENGTH = 2;
    public static final String API_PAGE_BIM_REDIRECT = "api_bim_redirect";

    // Dependencies
    protected final UserInfoFacade userInfoFacade;
    protected final BusinessAccountDb businessAccountDb;
    protected final BusinessAccessService businessAccessService;
    protected final ProjectDesignFacadeDb projectDesignFacadeDb;
    protected final VrcAppIdDataDb vrcAppIdDataDb;
    protected final SessionConflictHandler sessionConflictHandler;
    protected final GrayLunchService grayLunchService;
    protected final ToadProperties toadProperties;
    protected final UserDb userDb;
    protected final SaaSConfigService saaSConfigService;
    protected final ToolLinkConfigCache toolLinkConfigCache;
    protected final SaasConfigFacade saasConfigFacade;
    protected final AuthCheckService authCheckService;

    @NotNull
    public ToolPageResult handlePage(HttpServletRequest request,
            HttpServletResponse response,
            DiyPageParam param,
            String actualAppId,
            Long appIdNum)
            throws AccessAuthenticatorException, YunDesignException, IOException {

        Long userId = param.getUserId();
        final String obsDesignId = param.getObsDesignId();
        final Long rootAccountId = businessAccountDb.getRootAccountForBAndC(userId);
        final boolean bimAccess = businessAccessService.checkBimAccess(userId);
        LOG.message("AbstractDiyPageHandler: dispatching page request")
                .with("userId", userId)
                .with("obsDesignId", obsDesignId)
                .with("isFromCoohom", param.isFromCoohom())
                .with("bimAccess", bimAccess)
                .info();

        if (obsDesignId != null) {
            return handleExistingDesign(request, response, param, actualAppId, appIdNum,
                    rootAccountId, bimAccess);
        } else {
            return handleNewDesign(request, response, param, actualAppId, appIdNum, rootAccountId,
                    bimAccess);
        }
    }

    // New abstract methods
    protected abstract ToolPageResult handleExistingDesign(HttpServletRequest request,
            HttpServletResponse response,
            DiyPageParam param,
            String actualAppId,
            Long appIdNum,
            Long rootAccountId,
            boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException;

    protected abstract ToolPageResult handleNewDesign(HttpServletRequest request,
            HttpServletResponse response,
            DiyPageParam param,
            String actualAppId,
            Long appIdNum,
            Long rootAccountId,
            boolean bimAccess)
            throws AccessAuthenticatorException, YunDesignException, IOException;

    // Helper methods remain protected
    protected void fillFavorIcon(final Long userId, final H5Model h5Model) {
        final com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap businessConfigMap =
                saaSConfigService.getBusinessConfigMap(userId,
                        SaaSConfigService.COMMON_CONFIG_FAVOR_ICON);
        if (businessConfigMap != null && businessConfigMap.isNotEmpty()) {
            final BusinessConfigElement businessConfigElement = businessConfigMap.get(
                    SaaSConfigService.COMMON_CONFIG_FAVOR_ICON);
            if (businessConfigElement != null &&
                    Boolean.TRUE.equals(businessConfigElement.getShow())) {
                final FavorIcon favorIcon = new FavorIcon();
                favorIcon.setPicUrl(businessConfigElement.getPicUrl());
                h5Model.setFavorIcon(favorIcon);
            }
        }
    }

    protected void fillToolConfig(final H5Model h5Model) {
        final BusinessConfig toolLinkConfig = this.toolLinkConfigCache.getDefaultConfig();

        final FlashCustomizeSetting diyCreate3dPano = new FlashCustomizeSetting();
        final FlashCustomizeSetting renderFeedback = new FlashCustomizeSetting();
        final FlashCustomizeSetting recentChanges = new FlashCustomizeSetting();

        if (toolLinkConfig != null) {
            final BusinessConfigMap allToolConfigs = toolLinkConfig.getAllToolConfig();
            final BusinessConfigMap diyConfigs = toolLinkConfig.getDiyConfig();

            try {
                final Boolean recentChangesShow = allToolConfigs.get("recentChanges").getShow();
                final Boolean renderFeedbackShow = diyConfigs.get("renderFeedback").getShow();
                final Boolean diyCreate3dPanoShow = diyConfigs.get("diyCreate3dPano").getShow();

                recentChanges.setShow(recentChangesShow);
                renderFeedback.setShow(renderFeedbackShow);
                diyCreate3dPano.setShow(diyCreate3dPanoShow);
            } catch (final Exception e) {
                LOG.message("h5DiyPageApi")
                        .with(e)
                        .with("empty config keys in", "recentChanges")
                        .with("renderFeedback", "diyCreate3dPano")
                        .warn();
                recentChanges.setShow(true);
                renderFeedback.setShow(true);
                diyCreate3dPano.setShow(true);
            }
        } else {
            recentChanges.setShow(true);
            renderFeedback.setShow(true);
            diyCreate3dPano.setShow(true);
        }

        final ToolJsConfig toolJsConfig = new ToolJsConfig();
        toolJsConfig.setDiyCreate3dPano(diyCreate3dPano);
        toolJsConfig.setRecentChanges(recentChanges);
        toolJsConfig.setRenderFeedback(renderFeedback);
        h5Model.setToolJsConfigJson(JsonMapper.writeValueAsString(toolJsConfig));
    }

    protected void fillUserInfo(final Long userId, final H5Model h5Model) {
        final User modelUser = new User();
        modelUser.setObsUserId(LongCipher.DEFAULT.encrypt(userId));
        final UserDto user = userDb.getUserBySession();
        if (user != null) {
            modelUser.setScore(userInfoFacade.checkKubiInfo(userId));
            final String name = userInfoFacade.getUserNameByUserId(userId);
            final String nickName = user.getUserName();
            modelUser.setName(nickName);
            h5Model.setUserContactName(name == null ? nickName : name);
        }
        h5Model.setUser(modelUser);
    }

    protected boolean useNewDecorationVersion(final HttpServletRequest request) {
        final UserInfoData userInfoData = new UserInfoData();
        final SessionUser sessionUser = UserDb.getSessionUserBySession();
        if (sessionUser == null) {
            LOG.message("useNewDecorationVersion - sessionUser is null")
                    .warn();
        } else {
            userInfoData.setUserId(sessionUser.getUserId());
            userInfoData.setUserVersion(String.valueOf(sessionUser.getUserVersion()));
        }
        return DECORATION_SERVICE_NEW_VERSION.equals(
                grayLunchService.getGrayVersion(DECORATION_SERVICE_NAME,
                        new HttpRequestMetaData(request, userInfoData)));
    }

    protected void fillPlanInfo(String obsSrcPlanId, String planName, String askId,
            Byte planType, final H5Model h5Model) {
        final PlanInfoConfig planConfig = new PlanInfoConfig();
        planConfig.setPlanName(planName);
        planConfig.setPlanType(planType);
        final PlanHelpConfig planHelpConfig = new PlanHelpConfig();
        planHelpConfig.setAskId(askId);
        planHelpConfig.setObsSrcPlanId(obsSrcPlanId);
        h5Model.setPlanInfo(new PlanInfo(planConfig, planHelpConfig));
    }

    protected void fillAccountInfo(final Long userId, final Long rootAccountId,
            final H5Model h5Model) {
        final com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap
                moduleConfigMapOfLang = saaSConfigService.getBusinessConfigMap(userId,
                ALL_TOOL_CONFIG_LANGUAGE_TYPE);
        if (moduleConfigMapOfLang == null || moduleConfigMapOfLang.isEmpty()) {
            return;
        }
        final BusinessConfigElement languageConfig = moduleConfigMapOfLang.get(
                ALL_TOOL_CONFIG_LANGUAGE_TYPE);
        if (languageConfig == null || languageConfig.getStatus() == null) {
            return;
        }
        final RootAccount rootAccount = new RootAccount();
        if (languageConfig.getStatus() == 1) {
            rootAccount.setLang("en_US");
        } else if (languageConfig.getStatus() == 0) {
            rootAccount.setLang("zh_CN");
        }
        if (rootAccountId != null) {
            final TobBusinessAccount businessAccount =
                    businessAccountDb.getBusinessAccountById(rootAccountId);
            if (businessAccount != null) {
                rootAccount.setBusinessAccountName(businessAccount.getName());
                rootAccount.setObsUserId(LongCipher.DEFAULT.encrypt(businessAccount.getUserId()));
            }
        }
        h5Model.setRootAccount(rootAccount);
    }

    protected boolean checkUpgradeAccess(final String upgradeSuperFloorPlan,
            final String upgradePublicDecoration, String vrc) {
        if (upgradeSuperFloorPlan == null && upgradePublicDecoration == null) {
            return false;
        }
        if (upgradeSuperFloorPlan != null && upgradeSuperFloorPlan.equals(FALSE) &&
                upgradePublicDecoration != null && upgradePublicDecoration.equals(FALSE)) {
            return false;
        }
        return vrc != null && !VrcEnum.PUBLIC.equals(VrcEnum.of(vrc)) &&
                !VrcEnum.SUPER_FLOOR_PLAN_2.equals(VrcEnum.of(vrc));
    }

    protected String redirectSuperOrPublicUpgradeUrl(final String obsDesignId,
            final String upgradeSuperFloorPlan,
            final String upgradePublicDecoration, final HttpServletRequest request) {
        VrcEnum vrcCode = VrcEnum.YUNTU;
        Integer status = UPGRADE_AUTHORIZED_SUCCESS;
        if (upgradeSuperFloorPlan != null && upgradeSuperFloorPlan.equals(TRUE) &&
                upgradePublicDecoration != null && upgradePublicDecoration.equals(TRUE)) {
            vrcCode = null;
            status = UPGRADE_AUTHORIZED_FAILED;
        }
        if (status.equals(UPGRADE_AUTHORIZED_SUCCESS) && upgradeSuperFloorPlan != null &&
                upgradeSuperFloorPlan.equals(TRUE)) {
            vrcCode = VrcEnum.SUPER_FLOOR_PLAN_2;
        }
        if (status.equals(UPGRADE_AUTHORIZED_SUCCESS) && upgradePublicDecoration != null &&
                upgradePublicDecoration.equals(TRUE)) {
            vrcCode = VrcEnum.PUBLIC;
        }
        String host = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        if (org.apache.commons.lang3.StringUtils.isEmpty(host)) {
            host = toadProperties.getDomainHost();
        } else {
            host = "//" + host;
        }
        final StringBuilder url = new StringBuilder()
                .append(host)
                .append(UPGRADE_PATH)
                .append(obsDesignId)
                .append("&status=")
                .append(status);
        if (vrcCode != null) {
            url.append("&vrc=").append(vrcCode.getCode());
        }
        url.append("&redirect=");
        String redirectUrl = host + BIM_PATH;
        if (!org.apache.commons.lang3.StringUtils.isEmpty(request.getQueryString())) {
            redirectUrl = redirectUrl + "?" + request.getQueryString();
        }
        url.append(UrlEncoded.encodeString(redirectUrl));
        return url.toString();
    }

    protected boolean needRedirectBIM(final HttpServletRequest request, final Long userId,
            final Long rootAccountId) {
        final String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(originalHost) && originalHost.contains(
                COOHOM)) {
            return false;
        }
        final UserInfoData userInfoData = new UserInfoData();
        userInfoData.setRootAccountId(rootAccountId);
        userInfoData.setUserId(userId);
        String version =
                grayLunchService.getGrayVersion(API_PAGE_BIM_REDIRECT,
                        new HttpRequestMetaData(request, userInfoData));
        LOG.message("checkApiRedirectBIM from graylaunchmw")
                .with("userId", userId)
                .with("version", version)
                .info();
        if (version == null) {
            version = ALLOW;
        }
        final Boolean graylaunchSwitch = ALLOW.equals(version);
        if (rootAccountId != null) {
            final Boolean bimSwitch = saasConfigFacade.bimSwitchConfig(userId);
            LOG.message("checkApiRedirectBIM for rootAccount from saas-config")
                    .with("userId", userId)
                    .with("rootAccountId", rootAccountId)
                    .with("bimSwitch", bimSwitch)
                    .info();
            return bimSwitch && graylaunchSwitch;
        }
        return graylaunchSwitch;
    }

    protected String redirectUpdateUrl(final HttpServletRequest request, String obsDesignId) {
        String redirectPath =
                UPGRADE_PATH + obsDesignId;
        String host = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        if (org.apache.commons.lang3.StringUtils.isEmpty(host)) {
            host = toadProperties.getDomainHost();
        } else {
            host = "//" + host;
        }
        String redirectUrl = host + BIM_PATH;
        if (!org.apache.commons.lang3.StringUtils.isEmpty(request.getQueryString())) {
            redirectUrl = redirectUrl + "?" + request.getQueryString();
        }
        return host + redirectPath + "&redirect=" + UrlEncoded.encodeString(redirectUrl);
    }

    protected ToolPageResult redirectByUrl(String url) {
        try {
            new URI(url);
            return ToolPageResult.redirect(url);
        } catch (final URISyntaxException e) {
            LOG.message("redirectByUrl - error")
                    .with(e).error();
            return ToolPageResult.create(
                    org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR,
                    null, "redirect error");
        }
    }

    protected String redirectUpdateUrlWithTre(final HttpServletRequest request,
            final Long designId, final List<String> tres) {
        String host = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        if (org.apache.commons.lang3.StringUtils.isEmpty(host)) {
            host = toadProperties.getYunHost();
        } else {
            host = "//" + host;
        }
        String url = host +
                UPGRADE_PATH + LongCipher.DEFAULT.encrypt(designId);
        String redirectUrl = host + BIM_PATH + "?designid="
                + LongCipher.DEFAULT.encrypt(designId);
        if (tres != null && !tres.isEmpty()) {
            redirectUrl += "&tre=" + String.join("&tre=", tres);
        }
        return url + "&redirect=" + UrlEncoded.encodeString(redirectUrl);
    }

    protected ToolPageResult redirectToBIMPage(final HttpServletRequest request) {
        try {
            String host = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
            if (org.apache.commons.lang3.StringUtils.isEmpty(host)) {
                host = toadProperties.getYunHost();
            } else {
                host = "//" + host;
            }
            String url = host + BIM_PATH;
            if (!org.apache.commons.lang3.StringUtils.isEmpty(request.getQueryString())) {
                url = url + "?" + request.getQueryString();
            }
            new URI(url);
            return ToolPageResult.redirect(url);
        } catch (final URISyntaxException e) {
            LOG.message("redirectToBIMPage - error")
                    .with(e).error();
            return ToolPageResult.create(
                    org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR,
                    null, "redirect error");
        }
    }

    protected ToolPageResult redirectCoohomBIMPage(final HttpServletRequest request) {
        try {
            String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
            String host = "";
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(originalHost)) {
                host = "//" + originalHost;
            }
            String url = host + COOHOM_BIM_PATH + "?";

            final String queryString = request.getQueryString();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(queryString)) {
                url = url + request.getQueryString();
            }
            new URI(url);
            return ToolPageResult.redirect(url);
        } catch (final URISyntaxException e) {
            LOG.message("redirectCoohomBIMPage - error")
                    .with(e).error();
            return ToolPageResult.create(
                    org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR,
                    null, "redirect error");
        }
    }

    @SneakyThrows(URISyntaxException.class)
    protected ToolPageResult redirectDefaultPage(final HttpServletRequest request) {
        String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        String yunHost = toadProperties.getYunHost();
        String host = toadProperties.getHost();
        String loginHost = toadProperties.getLoginUrl();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(originalHost)) {
            yunHost = "//" + originalHost;
            host = "//" + originalHost;
            loginHost = "//" + originalHost;
        }
        if (request.getHeader(I18N_HEADER) != null) {
            return ToolPageResult.create(org.springframework.http.HttpStatus.FORBIDDEN, null,
                    "");
        }
        final String queryString = request.getQueryString();
        if (queryString == null) {
            return sendRedirect(
                    loginHost + genRedirectPath(yunHost + H5_PAGE_CLEAN_PATH,
                            request));
        }
        final Matcher matcher = PATTERN.matcher(queryString);
        String[] split = null;
        if (matcher.find()) {
            split = matcher.group().split("=");
            if (split.length < SPLIT_LENGTH) {
                return sendRedirect(
                        loginHost + genRedirectPath(yunHost + H5_PAGE_CLEAN_PATH,
                                request));
            }
        }
        if (split == null) {
            return sendRedirect(
                    loginHost + genRedirectPath(yunHost + H5_PAGE_CLEAN_PATH,
                            request));
        }
        final Long planId = projectDesignFacadeDb.getPlanId(LongCipher.DEFAULT.decrypt(split[1]));
        if (planId == null) {
            return sendRedirect(
                    loginHost + genRedirectPath(yunHost + H5_PAGE_CLEAN_PATH,
                            request));
        }
        return sendRedirect(host + "/pcenter/design/" + LongCipher.DEFAULT.encrypt(planId) +
                "/list");
    }

    @SneakyThrows(IOException.class)
    private String genRedirectPath(final String path, final HttpServletRequest request) {
        final String queryString = request.getQueryString();
        final String redirectPath =
                path + (queryString == null ? "" : "?" + queryString);
        return URLEncoder.encode(redirectPath, "UTF-8");
    }

    protected ToolPageResult sendRedirect(final String url)
            throws URISyntaxException {
        new URI(url);
        return ToolPageResult.redirect(url);
    }
} 