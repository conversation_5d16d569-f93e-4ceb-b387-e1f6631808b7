/*
 * ProjectNotifyServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.instdeco.plan.siteapicommon.exception.SiteApiException;
import com.qunhe.instdeco.plan.yun.core.client.apis.DecoProjectApi;
import com.qunhe.instdeco.plan.yuncoreapi.clients.DesignClient;
import com.qunhe.user.growth.floor.plan.cool.client.client.IncomingClient;
import com.qunhe.user.growth.floor.plan.cool.common.data.incoming.BindDrawingDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class ProjectNotifyServiceTest {
    @Mock
    IncomingClient incomingClient;
    @Mock
    DecoProjectApi decoProjectApi;
    @Mock
    DesignClient designClient;
    @Mock
    ToadProperties toadProperties;
    @InjectMocks
    ProjectNotifyService projectNotifyService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testNotifyProjectCreate_darenIncomingId_exists() throws SiteApiException {
        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .designId(35338021L)
                .userId(1111251217L)
                .darenIncomingId("3FO4MCRP8WYM")
                .build();
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(1111251217L);
        ProjectSequence projectSequence = new ProjectSequence();
        projectSequence.setDesignId(35338021L);
        projectNotifyService.notifyProjectCreate(toolProjectSaveParam, projectSequence,
                sessionUser);
        BindDrawingDto bindDrawingDto = new BindDrawingDto();
        bindDrawingDto.setDesignId(toolProjectSaveParam.getDesignId());
        bindDrawingDto.setIncomingId(LongCipher.DEFAULT.decrypt(toolProjectSaveParam.getDarenIncomingId()));
        Mockito.verify(incomingClient, times(1)).bindDrawingDesign(eq(bindDrawingDto));
    }

    @Test
    public void testNotifyProjectCreate_use_newYuncoreApi() throws SiteApiException {
        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .designId(35338021L)
                .userId(1111251217L)
                .build();
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(1111251217L);
        sessionUser.setUserType(1);
        ProjectSequence projectSequence = new ProjectSequence();
        projectSequence.setDesignId(35338021L);

        projectNotifyService.notifyProjectCreate(toolProjectSaveParam, projectSequence,
                sessionUser);
        Mockito.verify(decoProjectApi, times(1)).addDesignAttributeDefaultPrivate(toolProjectSaveParam.getUserId(),
                sessionUser.getUserType(), toolProjectSaveParam.getDesignId());
        Mockito.verify(designClient, times(0)).addDesignAttribute(toolProjectSaveParam.getUserId(),
                sessionUser.getUserType(), toolProjectSaveParam.getDesignId());
    }

    @Test
    public void testNotifySiteForDarenCreate() throws SiteApiException {
        doThrow(new SiteApiException()).when(incomingClient).bindDrawingDesign(any());
        projectNotifyService.notifySiteForDarenCreate(35338021L,null);
    }

    @Test
    public void testNotifySiteForDesignCreated() throws SiteApiException {
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(1111251217L);
        projectNotifyService.notifySiteForDesignCreated(35338021L,1111251217L,sessionUser);
    }
}
