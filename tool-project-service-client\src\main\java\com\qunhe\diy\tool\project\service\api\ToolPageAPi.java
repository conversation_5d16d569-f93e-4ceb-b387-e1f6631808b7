/*
 * ProjectAPi.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.api;

import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.constant.SoaConstant;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.rpc.common.utils.Pair;
import com.qunhe.rpc.proxy.annotation.ClientProperties;
import com.qunhe.rpc.proxy.annotation.Http;
import com.qunhe.rpc.proxy.annotation.ParamPairs;
import com.qunhe.rpc.proxy.annotation.Timeout;

import java.util.List;

/**
 * <AUTHOR>
 */
@ClientProperties(serviceVip = SoaConstant.SERVICE_VIP)
public interface ToolPageAPi {

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.BIM_TOOL_RPC_PAGE_KUJIALE, method = Http.HttpMethod.GET)
    ToolPageResult getBimKujialeToolPage(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.BIM_TOOL_RPC_PAGE_COOHOM, method = Http.HttpMethod.GET)
    ToolPageResult getBimCoohomToolPage(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.DIY_TOOL_RPC_PAGE_KUJIALE, method = Http.HttpMethod.GET)
    ToolPageResult getDiyKujialeToolPage(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.DIY_TOOL_RPC_PAGE_COOHOM, method = Http.HttpMethod.GET)
    ToolPageResult getDiyCoohomToolPage(@ParamPairs List<Pair<String, Object>> params);




}
