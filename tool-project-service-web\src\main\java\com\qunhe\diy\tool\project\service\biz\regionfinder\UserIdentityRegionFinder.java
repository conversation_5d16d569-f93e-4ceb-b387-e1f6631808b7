/*
 * UserIdentityRegionFinder.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.regionfinder;

import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFindResult;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFinderParam;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diybe.dms.data.Region;

/**
 * 决定一个方案的region
 *
 * <AUTHOR>
 * @date 2024/3/22
 */
@Component
@RequiredArgsConstructor
public class UserIdentityRegionFinder implements ProjectRegionFinder {

    private static final QHLogger LOG = QHLogger.getLogger(UserIdentityRegionFinder.class);

    private final UserInfoFacade userInfoFacade;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    @Override
    public @NotNull RegionFindResult findRegion(RegionFinderParam param) {
        try {
            return new RegionFindResult(userInfoFacade.isCoohomUser(param.getUserId()) ? Region.OVERSEAS :
                    Region.DOMESTIC, false);
        } catch (Exception e) {
            // catch异常，改用 vrc + 域名判断
            return fallbackToVrcAndHost(param, e);
        }

    }

    @NotNull
    private RegionFindResult fallbackToVrcAndHost(RegionFinderParam param, Exception e) {
        RegionFindResult regionFindResult;
        try {
            if (param.getVrc() != null && projectDesignFacadeDb.getCoohomVrcList().contains(param.getVrc())) {
                regionFindResult = new RegionFindResult(Region.OVERSEAS, true);
            } else {
                regionFindResult = new RegionFindResult(Region.DOMESTIC, true);
            }
            LOG.message("findRegion error, fallback to vrc check", e)
                    .with("param", param)
                    .with("result", regionFindResult)
                    .error();
            return regionFindResult;
        } catch (Exception ex) {
            // catch异常，改用域名判断
            regionFindResult = new RegionFindResult(checkCoohomUserByHost(param), true);
            LOG.message("findRegion error, fallback to host check", ex)
                    .with("param", param)
                    .with("result", regionFindResult)
                    .error();
            return regionFindResult;
        }
    }

    @NotNull
    private Region checkCoohomUserByHost(RegionFinderParam param) {
        return param.isCoohomOrigin() ? Region.OVERSEAS : Region.DOMESTIC;
    }


}
