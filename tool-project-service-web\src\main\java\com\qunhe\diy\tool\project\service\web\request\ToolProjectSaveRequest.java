/*
 * ProjectDesignCreateReq.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.qunhe.diy.tool.project.service.common.enums.ProjectBusinessEnum;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * 方案创建请求
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolProjectSaveRequest {

    private String obsUserId;
    /**
     * 楼盘 Id
     */
    private String obsCommId;
    private Double area;
    private Double realArea;
    private Double sourceArea;
    private Byte planType;
    private String name;
    private String vrc;
    private Boolean reworked = false;
    private String imgUrl;
    private ProjectBusinessEnum mProjectBusinessEnum;
    private Boolean recommend = false;
    private Long specId;
    private Long unitId;
    private Boolean designSaved;
    /**
     * 来源
     */
    private Boolean cadImported;
    private Boolean tutorial;
    private String darenIncomingId;

    private String levelName;

    private Integer createdAppId;

    public void setCreatedAppId(String obsCreateAppId) {
        if (StringUtils.isEmpty(obsCreateAppId)) {
            return;
        }
        this.createdAppId = (int) LongCipher.DEFAULT.decrypt(obsCreateAppId);
    }

}
