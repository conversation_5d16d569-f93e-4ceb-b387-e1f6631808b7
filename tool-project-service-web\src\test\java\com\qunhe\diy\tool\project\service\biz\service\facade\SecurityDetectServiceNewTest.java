/*
 * SecurityDetectServiceNewTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.mdw.security.detect.service.client.api.DetectApi;
import com.qunhe.mdw.security.detect.service.common.enums.FileTypeEnum;
import com.qunhe.mdw.security.detect.service.common.enums.FinalDecisionEnum;
import com.qunhe.mdw.security.detect.service.common.model.DetectRequest;
import com.qunhe.mdw.security.detect.service.common.model.DetectResult;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.web.standard.data.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Additional unit tests for SecurityDetectService
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SecurityDetectServiceNewTest {

    @Mock
    private DetectApi detectApi;

    @Mock
    private LongCrypt longCrypt;

    @InjectMocks
    private SecurityDetectService securityDetectService;

    @Before
    public void setUp() {
        // Setup common test data
    }

    @Test
    public void testSensitiveWordCheck_BlankContent() {
        // Test null content
        boolean result1 = securityDetectService.sensitiveWordCheck(12345L, null);
        assertTrue("Null content should return true", result1);

        // Test empty content
        boolean result2 = securityDetectService.sensitiveWordCheck(12345L, "");
        assertTrue("Empty content should return true", result2);

        // Test whitespace content
        boolean result3 = securityDetectService.sensitiveWordCheck(12345L, "   ");
        assertTrue("Whitespace content should return true", result3);

        // Verify no API calls were made
        verify(detectApi, never()).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_AcceptDecision() {
        // Setup
        Long userId = 12345L;
        String content = "This is normal content";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());
        detectResult.setReasonCode(0);

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("Accept decision should return true", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_RejectDecision() {
        // Setup
        Long userId = 12345L;
        String content = "This contains sensitive words";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.REJECT.getDecision());
        detectResult.setReasonCode(100);

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertFalse("Reject decision should return false", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_ApiFailure() {
        // Setup
        Long userId = 12345L;
        String content = "Test content";

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("500");
        apiResult.setM("Internal Server Error");
        apiResult.setD(null);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("API failure should return true", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_NullDetectResult() {
        // Setup
        Long userId = 12345L;
        String content = "Test content";

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(null);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("Null detect result should return true", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_RequestParameters() {
        // Setup
        Long userId = 12345L;
        String content = "Test content for parameter verification";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        securityDetectService.sensitiveWordCheck(userId, content);

        // Verify request parameters
        ArgumentCaptor<DetectRequest> requestCaptor = ArgumentCaptor.forClass(DetectRequest.class);
        verify(detectApi, times(1)).detect(requestCaptor.capture());

        DetectRequest capturedRequest = requestCaptor.getValue();
        assertEquals("Content should match", content, capturedRequest.getContent());
        assertEquals("GUID should match", "tool-project-service", capturedRequest.getGuid());
        assertEquals("Type should be TEXT", FileTypeEnum.TEXT.getName(), capturedRequest.getType());
    }

    @Test
    public void testSensitiveWordCheck_ReviewDecision() {
        // Setup
        Long userId = 12345L;
        String content = "Content under review";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.REVIEW.getDecision());
        detectResult.setReasonCode(50);

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("Review decision should return true (not rejected)", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheckFallback_Success() {
        // Setup
        Long userId = 12345L;
        String content = "Test content";
        Throwable exception = new RuntimeException("Test exception");

        // Execute
        boolean result = securityDetectService.sensitiveWordCheckFallback(userId, content, exception);

        // Verify
        assertTrue("Fallback should always return true", result);
    }

    @Test
    public void testSensitiveWordCheckFallback_NullParameters() {
        // Setup
        Throwable exception = new RuntimeException("Test exception");

        // Execute
        boolean result1 = securityDetectService.sensitiveWordCheckFallback(null, null, exception);
        boolean result2 = securityDetectService.sensitiveWordCheckFallback(12345L, null, exception);
        boolean result3 = securityDetectService.sensitiveWordCheckFallback(null, "content", exception);

        // Verify
        assertTrue("Fallback with null userId should return true", result1);
        assertTrue("Fallback with null content should return true", result2);
        assertTrue("Fallback with null userId should return true", result3);
    }

    @Test
    public void testSensitiveWordCheck_LongContent() {
        // Setup
        Long userId = 12345L;
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longContent.append("This is a very long content for testing purposes. ");
        }

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, longContent.toString());

        // Verify
        assertTrue("Long content should be processed correctly", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_SpecialCharacters() {
        // Setup
        Long userId = 12345L;
        String content = "Content with special chars: @#$%^&*()_+-=[]{}|;':\",./<>?";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("Special characters should be processed correctly", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }

    @Test
    public void testSensitiveWordCheck_UnicodeContent() {
        // Setup
        Long userId = 12345L;
        String content = "Unicode content: 你好世界 🌍 测试内容";

        DetectResult detectResult = new DetectResult();
        detectResult.setFinalDecision(FinalDecisionEnum.ACCEPT.getDecision());

        Result<DetectResult> apiResult = new Result<>();
        apiResult.setC("0");
        apiResult.setM("success");
        apiResult.setD(detectResult);

        when(detectApi.detect(any(DetectRequest.class))).thenReturn(apiResult);

        // Execute
        boolean result = securityDetectService.sensitiveWordCheck(userId, content);

        // Verify
        assertTrue("Unicode content should be processed correctly", result);
        verify(detectApi, times(1)).detect(any(DetectRequest.class));
    }
}
