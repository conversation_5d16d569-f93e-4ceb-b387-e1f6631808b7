/*
 * BusinessAccessServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.rpc.client.ClientException;
import com.qunhe.saas.commercialization.acl.sdk.arc.client.AccessPointClient;
import com.qunhe.saas.commercialization.acl.sdk.data.CheckAccessibleRequestBody;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.saas.design.client.DesignBimSwitchClient;
import com.qunhe.saas.design.client.data.DesignBimAccessDTO;
import com.qunhe.web.standard.data.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BusinessAccessServiceTest {

    @Mock
    private AccessPointClient mockCommercialAccessPointClient;
    @Mock
    private DesignBimSwitchClient mockDesignBimSwitchClient;

    @Spy
    @InjectMocks
    private BusinessAccessService businessAccessServiceUnderTest;

    @Test
    public void testCheckBimAccess() throws Exception {
        // Setup
        // Configure DesignBimSwitchClient.getBimAccess(...).
        final DesignBimAccessDTO designBimAccessDTO = new DesignBimAccessDTO();
        designBimAccessDTO.setBimAccess(false);
        designBimAccessDTO.setBimChoice(false);
        designBimAccessDTO.setBelongKey("belongKey");
        final Result<DesignBimAccessDTO> designBimAccessDTOResult = new Result<>("c", "m",
                designBimAccessDTO);
        when(mockDesignBimSwitchClient.getBimAccess(0L)).thenReturn(designBimAccessDTOResult);

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkBimAccess(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckBimAccess_DesignBimSwitchClientReturnsNoItem() throws Exception {
        // Setup
        when(mockDesignBimSwitchClient.getBimAccess(0L)).thenReturn(Result.ok());

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkBimAccess(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckBimAccess_DesignBimSwitchClientReturnsError() throws Exception {
        // Setup
        // Configure DesignBimSwitchClient.getBimAccess(...).
        final Result<DesignBimAccessDTO> designBimAccessDTOResult =
                Result.ok(DesignBimAccessDTO.denied());
        when(mockDesignBimSwitchClient.getBimAccess(0L)).thenReturn(designBimAccessDTOResult);

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkBimAccess(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckBimAccess_DesignBimSwitchClientThrowsClientException() throws Exception {
        // Setup
        when(mockDesignBimSwitchClient.getBimAccess(0L)).thenThrow(ClientException.class);

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkBimAccess(0L);

        // Verify the results
        assertThat(result).isFalse();
    }


    @Test
    public void testCheckAccess_GetAccessPointsReturnsEmpty() throws Exception {
        // Setup
        // Mock getProxy() to return the spy itself so that getAccessPoints can be called
        lenient().doReturn(businessAccessServiceUnderTest)
            .when(businessAccessServiceUnderTest)
            .getProxy();

        lenient().doReturn(Collections.emptyList())
            .when(businessAccessServiceUnderTest)
            .getAccessPoints(Collections.singletonList(0L), 0L);

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkAccess(0L, 0L);

        // Verify the results
        assertThat(result).isFalse();
        verify(businessAccessServiceUnderTest).getAccessPoints(Collections.singletonList(0L), 0L);
    }

    @Test
    public void testCheckAccess_GetAccessPointsThrowsException() throws Exception {
        // Setup
        // Mock getProxy() to return the spy itself so that getAccessPoints can be called
        lenient().doReturn(businessAccessServiceUnderTest)
            .when(businessAccessServiceUnderTest)
            .getProxy();

        Mockito.lenient().doThrow(new AccessAuthenticatorException("Test exception"))
            .when(businessAccessServiceUnderTest)
            .getAccessPoints(Collections.singletonList(0L), 0L);

        // Run the test
        final Boolean result = businessAccessServiceUnderTest.checkAccess(0L, 0L);

        // Verify the results
        assertThat(result).isFalse();
        verify(businessAccessServiceUnderTest).getAccessPoints(Collections.singletonList(0L), 0L);
    }

    @Test
    public void testGetAccessPoints_EmptyInputAccessPoints() throws Exception {
        final List<Long> result = businessAccessServiceUnderTest.getAccessPoints(Collections.emptyList(), 0L);
        assertThat(result).isEqualTo(Collections.emptyList());
        verifyNoInteractions(mockCommercialAccessPointClient);
    }

    @Test
    public void testGetAccessPoints_NullInputAccessPoints() throws Exception {
        final List<Long> result = businessAccessServiceUnderTest.getAccessPoints(null, 0L);
        assertThat(result).isEqualTo(Collections.emptyList());
        verifyNoInteractions(mockCommercialAccessPointClient);
    }

    @Test
    public void testGetAccessPoints_NullUserId() throws Exception {
        final List<Long> result = businessAccessServiceUnderTest.getAccessPoints(Arrays.asList(0L), null);
        assertThat(result).isEqualTo(Collections.emptyList());
        verifyNoInteractions(mockCommercialAccessPointClient);
    }

    @Test
    public void testGetAccessPoints() throws Exception {
        // Setup
        final List<Long> inputAccessPoints = Arrays.asList(0L);
        final Long userId = 0L;
        final List<Long> expectedOutputPoints = Arrays.asList(0L);

        ArgumentCaptor<CheckAccessibleRequestBody> captor =
                ArgumentCaptor.forClass(CheckAccessibleRequestBody.class);
        when(mockCommercialAccessPointClient.filterAccessPoint(captor.capture()))
                .thenReturn(expectedOutputPoints);

        // Run the test
        final List<Long> result = businessAccessServiceUnderTest.getAccessPoints(inputAccessPoints, userId);

        // Verify the results
        assertThat(result).isEqualTo(expectedOutputPoints);

        final CheckAccessibleRequestBody actualRequestBody = captor.getValue();
        assertThat(actualRequestBody.getAccessPoints()).isEqualTo(inputAccessPoints);
        assertThat(actualRequestBody.getUserId()).isEqualTo(userId);
        assertThat(actualRequestBody.getRelation()).isNull();
        assertThat(actualRequestBody.getOrgId()).isNull();
        assertThat(actualRequestBody.getIsAdmin()).isNull();
    }

    @Test
    public void testGetAccessPoints_AccessPointClientReturnsNoItems() throws Exception {
        // Setup
        final List<Long> inputAccessPoints = Arrays.asList(0L);
        final Long userId = 0L;

        ArgumentCaptor<CheckAccessibleRequestBody> captor =
                ArgumentCaptor.forClass(CheckAccessibleRequestBody.class);
        when(mockCommercialAccessPointClient.filterAccessPoint(captor.capture()))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = businessAccessServiceUnderTest.getAccessPoints(inputAccessPoints, userId);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());

        final CheckAccessibleRequestBody actualRequestBody = captor.getValue();
        assertThat(actualRequestBody.getAccessPoints()).isEqualTo(inputAccessPoints);
        assertThat(actualRequestBody.getUserId()).isEqualTo(userId);
        assertThat(actualRequestBody.getRelation()).isNull();
        assertThat(actualRequestBody.getOrgId()).isNull();
        assertThat(actualRequestBody.getIsAdmin()).isNull();
    }

    @Test
    public void testGetAccessPoints_AccessPointClientThrowsAccessAuthenticatorException()
            throws Exception {
        // Setup
        final List<Long> inputAccessPoints = Arrays.asList(0L);
        final Long userId = 0L;

        ArgumentCaptor<CheckAccessibleRequestBody> captor =
                ArgumentCaptor.forClass(CheckAccessibleRequestBody.class);
        when(mockCommercialAccessPointClient.filterAccessPoint(captor.capture()))
                .thenThrow(AccessAuthenticatorException.class);

        // Run the test
        assertThatThrownBy(() -> businessAccessServiceUnderTest.getAccessPoints(inputAccessPoints, userId))
                .isInstanceOf(AccessAuthenticatorException.class);

        final CheckAccessibleRequestBody actualRequestBody = captor.getValue();
        assertThat(actualRequestBody.getAccessPoints()).isEqualTo(inputAccessPoints);
        assertThat(actualRequestBody.getUserId()).isEqualTo(userId);
        assertThat(actualRequestBody.getRelation()).isNull();
        assertThat(actualRequestBody.getOrgId()).isNull();
        assertThat(actualRequestBody.getIsAdmin()).isNull();
    }
}
