/*
 * CommunityServiceNewTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.FphFloorplanService;
import com.qunhe.diy.tool.project.service.biz.service.facade.HasSwitchFacade;
import com.qunhe.diy.tool.project.service.common.constant.CommonConstant;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.house.property.floorplan.home.common.enums.AreaGradeEnum;
import com.qunhe.house.property.floorplan.home.common.model.AreaItemDTO;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Additional unit tests for CommunityService
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CommunityServiceNewTest {

    @Mock
    private CommunityDb communityDb;

    @Mock
    private SysDictAreaMapper sysDictAreaMapper;

    @Mock
    private GeneralProperties generalProperties;

    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;

    @Mock
    private FphFloorplanService fphFloorplanService;

    @Mock
    private HasSwitchFacade switchFacade;

    @InjectMocks
    private CommunityService communityService;

    @Before
    public void setUp() {
        // Setup common test data
    }

    @Test
    public void testGetOrCreateProjectCommunity_Success() {
        // Setup
        String province = "浙江省";
        String city = "杭州市";
        String communityName = "测试小区";
        Long areaId = 12345L;

        Community expectedCommunity = new Community();
        expectedCommunity.setCommId(1L);
        expectedCommunity.setName(communityName);
        expectedCommunity.setAreaId(areaId);

        when(communityDb.getAreaId(province, city)).thenReturn(areaId);
        when(communityDb.getOrCreateCommunity(areaId, communityName)).thenReturn(expectedCommunity);

        // Execute
        Community result = communityService.getOrCreateProjectCommunity(province, city, communityName);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Community name should match", communityName, result.getName());
        assertEquals("Area ID should match", areaId, result.getAreaId());
        verify(communityDb, times(1)).getAreaId(province, city);
        verify(communityDb, times(1)).getOrCreateCommunity(areaId, communityName);
    }

    @Test
    public void testGetOrCreateProjectCommunityByAreaIdAndName_Success() {
        // Setup
        Long areaId = 12345L;
        String communityName = "测试小区";

        Community expectedCommunity = new Community();
        expectedCommunity.setCommId(1L);
        expectedCommunity.setName(communityName);
        expectedCommunity.setAreaId(areaId);

        when(communityDb.getOrCreateCommunity(areaId, communityName)).thenReturn(expectedCommunity);

        // Execute
        Community result = communityService.getOrCreateProjectCommunityByAreaIdAndName(areaId, communityName);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Community name should match", communityName, result.getName());
        assertEquals("Area ID should match", areaId, result.getAreaId());
        verify(communityDb, times(1)).getOrCreateCommunity(areaId, communityName);
    }

    @Test
    public void testGetCommNameById_Success() {
        // Setup
        Long commId = 12345L;
        String expectedName = "测试小区名称";

        when(communityDb.getCommNameByCommId(commId)).thenReturn(expectedName);

        // Execute
        String result = communityService.getCommNameById(commId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Community name should match", expectedName, result);
        verify(communityDb, times(1)).getCommNameByCommId(commId);
    }

    @Test
    public void testGetCommNameById_Null() {
        // Setup
        Long commId = 12345L;

        when(communityDb.getCommNameByCommId(commId)).thenReturn(null);

        // Execute
        String result = communityService.getCommNameById(commId);

        // Verify
        assertNull("Result should be null", result);
        verify(communityDb, times(1)).getCommNameByCommId(commId);
    }

    @Test
    public void testGetSysDictArea_Success() {
        // Setup
        Long commId = 12345L;
        SysDictArea expectedArea = new SysDictArea();
        expectedArea.setAreaId(1L);
        expectedArea.setName("杭州市");

        when(communityDb.getSysDictArea(commId)).thenReturn(expectedArea);

        // Execute
        SysDictArea result = communityService.getSysDictArea(commId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Area name should match", "杭州市", result.getName());
        verify(communityDb, times(1)).getSysDictArea(commId);
    }

    @Test
    public void testGetSysDictArea_Null() {
        // Setup
        Long commId = 12345L;

        when(communityDb.getSysDictArea(commId)).thenReturn(null);

        // Execute
        SysDictArea result = communityService.getSysDictArea(commId);

        // Verify
        assertNull("Result should be null", result);
        verify(communityDb, times(1)).getSysDictArea(commId);
    }

    @Test
    public void testGetLocationParam_WithAreaId() {
        // Setup
        boolean isCoohom = false;
        Long designId = 12345L;
        Long userId = 67890L;
        String areaId = "330100";

        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setCommunityName("测试小区");
        projectDesign.setCommLogicAreaId(330100);
        projectDesign.setCommLogicProvinceId(330000);

        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(projectDesign);

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, designId, userId, areaId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Community name should match", "测试小区", result.getName());
        assertEquals("Area ID should match", (Object) Integer.valueOf(330100), (Object) result.getAreaId());
        assertEquals("Parent area ID should match", (Object) Integer.valueOf(330000), (Object) result.getSysParentAreaId());
        verify(projectDesignFacadeDb, times(1)).getProjectByDesignId(designId);
    }

    @Test
    public void testGetLocationParam_WithoutAreaId_CoohomUser() {
        // Setup
        boolean isCoohom = true;
        Long userId = 67890L;
        Byte sourceId = 6;

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default location", "未知小区", result.getName());
        // Verify no external service calls were made for Coohom users
        verify(fphFloorplanService, never()).getIpLocation(anyString());
    }

    @Test
    public void testGetLocationParam_SwitchDisabled() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = 6;

        when(generalProperties.getEnableFphIpLocation()).thenReturn(false);

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default location", "未知小区", result.getName());
        verify(generalProperties, times(1)).getEnableFphIpLocation();
        verify(fphFloorplanService, never()).getIpLocation(anyString());
    }

    @Test
    public void testGetLocationParam_IllegalSourceId() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = 1; // Not in legal source IDs

        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Arrays.asList(6, 7));

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default location", "未知小区", result.getName());
        verify(generalProperties, times(1)).getEnableFphIpLocation();
        verify(generalProperties, times(1)).getLegalSourceIdsForFphIpLocation();
        verify(fphFloorplanService, never()).getIpLocation(anyString());
    }

    @Test
    public void testGetLocationParam_UserNotInBucket() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = 6;

        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Arrays.asList(6, 7));
        when(switchFacade.isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId)).thenReturn(false);

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default location", "未知小区", result.getName());
        verify(switchFacade, times(1)).isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId);
        verify(fphFloorplanService, never()).getIpLocation(anyString());
    }

    @Test
    public void testGetLocationParam_ValidIpLocation() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = 6;
        String mockIp = "***********";

        AreaItemDTO areaItem = new AreaItemDTO();
        areaItem.setAreaGrade(AreaGradeEnum.CITY);
        areaItem.setAreaId(330100L);
        areaItem.setAreaName("杭州市");

        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Arrays.asList(6, 7));
        when(switchFacade.isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId)).thenReturn(true);
        when(fphFloorplanService.getIpLocation((String) isNull())).thenReturn(areaItem);

        Community mockCommunity = new Community();
        mockCommunity.setAreaId(330100L);
        mockCommunity.setName(CommonConstant.DEFAULT_COMMUNITY_NAME);

        when(communityDb.getCommunities(CommonConstant.DEFAULT_COMMUNITY_NAME, null))
                .thenReturn(new Community[]{mockCommunity});

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        verify(generalProperties, times(1)).getEnableFphIpLocation();
        verify(generalProperties, times(1)).getLegalSourceIdsForFphIpLocation();
        verify(switchFacade, times(1)).isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId);
        verify(fphFloorplanService, times(1)).getIpLocation((String) isNull());
    }

    @Test
    public void testGetLocationParam_NullSourceId() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = null;

        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Arrays.asList(6, 7));
        when(switchFacade.isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId)).thenReturn(true);

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        verify(generalProperties, times(1)).getEnableFphIpLocation();
        verify(generalProperties, times(1)).getLegalSourceIdsForFphIpLocation();
        verify(switchFacade, times(1)).isLegalUserIdOrAccountId(CommunityService.SWITCH_NAME, userId);
        verify(fphFloorplanService, times(1)).getIpLocation((String) isNull());
    }

    @Test
    public void testGetLocationParam_EmptyLegalSourceIds() {
        // Setup
        boolean isCoohom = false;
        Long userId = 67890L;
        Byte sourceId = 6;

        when(generalProperties.getEnableFphIpLocation()).thenReturn(true);
        when(generalProperties.getLegalSourceIdsForFphIpLocation()).thenReturn(Collections.emptyList());

        // Execute
        LocationParam result = communityService.getLocationParam(isCoohom, userId, sourceId);

        // Verify
        assertNotNull("Result should not be null", result);
        assertEquals("Should return default location", "未知小区", result.getName());
        verify(generalProperties, times(1)).getEnableFphIpLocation();
        verify(generalProperties, times(1)).getLegalSourceIdsForFphIpLocation();
        verify(fphFloorplanService, never()).getIpLocation(anyString());
    }
}
