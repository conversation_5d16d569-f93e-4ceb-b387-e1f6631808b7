/*
 * ToolProjectException.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.exception;

import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.web.standard.exception.BizzException;


/**
 * <AUTHOR>
 */
public class ToolProjectCreateException extends BizzException {

    private Long designId;
    private Long planId;
    private Long userId;

    public ToolProjectCreateException(ErrorCode errorCode, String message) {
        super(errorCode.getCode(), message);
    }

    public ToolProjectCreateException(ErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getMsg());
    }

    public ToolProjectCreateException(ErrorCode errorCode, Long planId, Long designId,
            Long userId) {
        super(errorCode.getCode(), errorCode.getMsg());
        this.designId = designId;
        this.planId = planId;
        this.userId = userId;

    }

    public Long getDesignId() {
        return designId;
    }

    public Long getPlanId() {
        return planId;
    }

    public Long getUserId() {
        return userId;
    }
}
