---
description:
globs:
alwaysApply: false
---
# 核心功能说明

## 方案创建管理
根据 design.md 文件，方案创建功能包含以下关键参数：

### 必要参数
- commId: 楼盘
- area: 面积
- realarea: 户型的真实面积
- userid: 用户id
- plantype: 户型类型
- name: 方案名称
- vrc: 工具版本 (从 header 获取)

### 可选参数
- modeldataid: 关联 FLOOR_PKLAN_HOME_DATA，LAYOUT_HOME_DATA
- designdataid: 管理 DESIGN_HOME_DATA
- recommend: 标识是否推荐到画户型首页
- reworked: 是否为被修改的户型
- uploadpic: cad 和 bitmap
- albumid: 相册 Id
- sourceid: 户型来源 (null 表示正常新建)

### 方案创建后处理
- 发送消息通知
  - notifySiteForDarenCreate
  - notifySiteForDesignCreated
- 更新用户数据计数
  - uicUserInfoClient.incrUserDataCount(UserDataCount.PLAN.value(), userId)

## 方案复制
支持复制现有方案创建新方案。

## 数据存储与检索
管理工具方案相关的数据存储与检索功能。
