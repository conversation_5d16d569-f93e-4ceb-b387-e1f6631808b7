/*
 * CommunityApi.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.api;

import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.constant.SoaConstant;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import com.qunhe.rpc.common.utils.Pair;
import com.qunhe.rpc.proxy.annotation.ClientProperties;
import com.qunhe.rpc.proxy.annotation.Http;
import com.qunhe.rpc.proxy.annotation.ParamPairs;
import com.qunhe.rpc.proxy.annotation.Timeout;
import com.qunhe.web.standard.data.Result;

import java.util.List;

/**
 * <AUTHOR>
 */
@ClientProperties(serviceVip = SoaConstant.SERVICE_VIP)
public interface CommunityApi {

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.COMMUNITY_GET_OR_CREATE_API, method = Http.HttpMethod.GET)
    Result<Community> getOrCreateProjectCommunity(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.COMMUNITY_NAME_BY_COMM_ID, method = Http.HttpMethod.GET)
    String getCommNameByCommId(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.SYSDICTAREA_BY_COMM_ID, method = Http.HttpMethod.GET)
    SysDictArea getSysDictAreaByCommId(@ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.COMMUNITY_DEFAULT_LOCATION, method = Http.HttpMethod.GET)
    Result<LocationParam> calculateIpLocation(@ParamPairs List<Pair<String, Object>> params);
}
