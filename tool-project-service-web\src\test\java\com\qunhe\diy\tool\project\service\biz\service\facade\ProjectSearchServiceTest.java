/*
 * ProjectSearchServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.tool.project.service.biz.exception.DesignSearchException;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.search.client.DesignTagClient;
import com.qunhe.projectmanagement.client.ProjectClient;
import com.qunhe.web.standard.data.Result;
import com.qunhe.web.standard.data.list.ListResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class ProjectSearchServiceTest {
    @Mock
    QHLogger LOG;
    @Mock
    ProjectClient projectClient;
    @Mock
    DesignTagClient designTagClient;
    @InjectMocks
    ProjectSearchService projectSearchService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCountProject()  {
        ListResult<ProjectDesign> listResult = new ListResult<>();
        listResult.setTotalCount(1);
        when(projectClient.pageGetProjectByUserIdWithCols(any())).thenReturn(listResult);
        long result = projectSearchService.countProject(1111251217L);
        Assert.assertEquals(1L, result);
    }

    @Test
    public void checkDesignTagSearchable() {
        Long designId = 222L;
        when(designTagClient.checkSearchable(designId)).thenReturn(Result.ok(true));
        boolean result = projectSearchService.checkDesignTagSearchable(designId);
        Assert.assertTrue(result);
    }

    @Test(expected = DesignSearchException.class)
    public void checkDesignTagSearchable_exception() {
        Long designId = 222L;
        when(designTagClient.checkSearchable(designId)).thenReturn(null);
        boolean result = projectSearchService.checkDesignTagSearchable(designId);
    }

    @Test(expected = DesignSearchException.class)
    public void checkDesignTagSearchable_exception1() {
        Long designId = 222L;
        when(designTagClient.checkSearchable(designId)).thenReturn(Result.error("error"));
        boolean result = projectSearchService.checkDesignTagSearchable(designId);
    }

    @Test(expected = DesignSearchException.class)
    public void checkDesignTagSearchable_exception2() {
        Long designId = 222L;
        when(designTagClient.checkSearchable(designId)).thenReturn(Result.ok(null));
        boolean result = projectSearchService.checkDesignTagSearchable(designId);
    }
}
