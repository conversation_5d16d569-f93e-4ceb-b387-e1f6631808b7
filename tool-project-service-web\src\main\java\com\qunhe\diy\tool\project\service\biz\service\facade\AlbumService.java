/*
 * AlbumService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.util.ProxyExtractor;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.instdeco.picservice.client.AlbumClient;
import com.qunhe.instdeco.picservice.data.Album;
import com.qunhe.log.QHLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException"})
public class AlbumService implements ProxyExtractor<AlbumService> {

    private static final QHLogger LOG = QHLogger.getLogger(AlbumService.class);

    private static final int DEFAULT_RETRY_TIMES = 3;

    private final AlbumClient albumClient;

    @Autowired
    public AlbumService(final AlbumClient albumClient) {
        this.albumClient = albumClient;
    }

    public Album getAlbum(final ToolProjectSaveParam projectDesignSaveData) {

        final Album album = new Album();
        album.setName(projectDesignSaveData.getName());
        album.setUserId(projectDesignSaveData.getUserId());
        try {
            getProxy().insertAlbumWithReties(album);
        } catch (Exception e) {
            // 插入失败，直接返回
            LOG.message("getAlbum failed", e).with("user", album.getUserId()).error();
        }
        return album;

    }

    @SentinelResource(value = "updateAlbum")
    @SuppressWarnings("PMD.AvoidCatchExceptionInSentinelResource")
    public void updateAlbumDesignId(final Album album, final Long designId) {
        if (album == null || album.getAlbumId() == null || designId == null) {
            return;
        }
        album.setDesignId(designId);
        try {
            albumClient.updateAlbum(album);
            LOG.message("update_album_trigger")
                    .with("designId", designId.toString())
                    .with("albumId", album.getAlbumId().toString())
                    .analyze();
        } catch (final Exception e) {
            // ignore
            LOG.message("updateAlbumDesignId failed", e)
                    .with("designId", designId)
                    .error();
        }
    }

    @Retryable(value = Exception.class, maxAttempts = 3)
    @SentinelResource(value = "insertAlbum")
    public void insertAlbumWithReties(final Album album) throws Exception {
        final Long result = albumClient.addAlbum(album);
        if (result == null) {
            throw new ToolProjectCreateException(ErrorCode.PARAM_ERROR);
        }
        album.setAlbumId(result);
    }
}
