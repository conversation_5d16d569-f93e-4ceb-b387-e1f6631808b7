/*
 * FloorPlanSourceConstants.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.constant;

/**
 * <AUTHOR>
 * sourceId 含义 http://confluence.qunhequnhe.com/pages/viewpage.action?pageId=144374541
 */
public class FloorPlanSourceConstants {

    /**
     * 表示用户上传的户型
     */

    public static final Byte HELP_DRAW_IMPORT_SOURCE = (byte) 5;

    /**
     * 表示CAD导入的户型
     */
    public static final Byte CAD_IMPORT_SOURCE = (byte) 6;

    /**
     * 表示临摹图导入的户型
     */
    public static final Byte COUNTER_DRAW_IMPORT_SOURCE = (byte) 7;

    /**
     * 表示通过户型库导入直接覆盖方案创建的户型
     */
    public static final Byte PLAN_LIB_SOURCE_ID = (byte) 8;

    /**
     * 表示通过复制教学方案创建的户型
     */
    public static final Byte TUTORIAL_PLAN_SOURCE = (byte) 9;

    /**
     * 表示新版户型达人创建的方案
     */
    public static final Byte DAREN_IMPORT_SOURCE = (byte) 10;

}
