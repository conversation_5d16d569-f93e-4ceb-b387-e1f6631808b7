/*
 * DegradeAuthPermissionEvaluator.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.hunter.constant.TraceKeys;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.param.AuthCheckParam;
import com.qunhe.project.platform.project.auth.param.OwnerCheckParam;
import com.qunhe.project.platform.project.auth.security.AuthPermissionEvaluator;
import com.qunhe.project.platform.project.auth.security.Authentication;
import com.qunhe.project.platform.project.auth.security.DefaultAuthPermissionEvaluator;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class DegradeAuthPermissionEvaluator implements AuthPermissionEvaluator {
    private static final QHLogger LOG = QHLogger.getLogger(DegradeAuthPermissionEvaluator.class);
    private final DefaultAuthPermissionEvaluator defaultAuthPermissionEvaluator;

    @SentinelResource(value = "projectAuth#hasAccess", fallback = "hasAccessFallback")
    @Override
    public boolean hasAccess(final Authentication authentication,
            final AuthCheckParam authCheckParam) {
        if (!defaultAuthPermissionEvaluator.hasAccess(authentication, authCheckParam)) {
            LOG.message("hasAccess failed.")
                    .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                    .with("traceId", SpanContextHolder.getTraceId())
                    .withPoJo(authCheckParam)
                    .info();
            return false;
        }
        return true;
    }

    public boolean hasAccessFallback(Authentication authentication, AuthCheckParam authCheckParam, Throwable e) {
        LOG.message("hasAccess fallback to true.")
                .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                .with("traceId", SpanContextHolder.getTraceId())
                .withPoJo(authCheckParam)
                .error();
        return true;
    }

    @Override
    public boolean isOwner(final Authentication authentication,
            final OwnerCheckParam ownerCheckParam) {
        return defaultAuthPermissionEvaluator.isOwner(authentication, ownerCheckParam);
    }

    @Override
    public boolean isAuthenticated(Authentication authentication){
        if (!defaultAuthPermissionEvaluator.isAuthenticated(authentication)) {
            LOG.message("isAuthenticated check failed.")
                    .with("upApi", HunterContext.getContext().get(TraceKeys.UP_API))
                    .with("traceId", SpanContextHolder.getTraceId())
                    .with("userId", authentication.userId())
                    .info();
            return false;
        }
        return true;
    }
}
