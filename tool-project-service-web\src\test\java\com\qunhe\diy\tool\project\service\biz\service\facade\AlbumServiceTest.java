/*
 * AlbumServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.instdeco.picservice.client.AlbumClient;
import com.qunhe.instdeco.picservice.data.Album;
import com.qunhe.rpc.client.ClientException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.aop.framework.ProxyHelper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class AlbumServiceTest {

    @Mock
    AlbumClient albumClient;
    @InjectMocks
    AlbumService albumService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ProxyHelper.setProxy(albumService);
    }

    @Test
    public void testGetAlbum() throws Exception {
        when(albumClient.addAlbum(any())).thenReturn(1L);
        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .name("test")
                .userId(35338021L)
                .build();
        Album album = albumService.getAlbum(toolProjectSaveParam);
        assertThat(album.getName()).isEqualTo("test");
    }

    @Test
    public void testUpdateAlbumDesignId() throws ClientException {
        when(albumClient.updateAlbum(any())).thenThrow(new ClientException("异常"));

        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .designId(35338021L)
                .albumId(1L)
                .build();
        Album album = albumService.getAlbum(toolProjectSaveParam);
        albumService.updateAlbumDesignId(album, 34335143L);

        assertThat(album.getDesignId()).isEqualTo(34335143L);
    }

    @Test
    public void testInsertAlbumWithReties() throws Exception {
        when(albumClient.addAlbum(any())).thenReturn(2L);

        Album album = new Album();
        album.setUserId(1111251217L);
        albumService.insertAlbumWithReties(album);
        assertThat(album.getAlbumId()).isEqualTo(2L);
    }
}