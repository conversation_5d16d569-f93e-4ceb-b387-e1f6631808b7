/*
 * KujialeBimPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.page.handler;

import com.google.common.collect.Sets;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.VrcMappingNotFoundException;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SynergyFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.context.ToolType;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diy.tool.project.service.web.page.DesignService;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.VrcRewriteService;
import com.qunhe.diy.tool.project.service.web.page.VrcService;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.utils.VrcUtils;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.DESIGN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.DIRECT_FINISH;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.LEVEL_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.OBS_SER_PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_URL;

/**
 * <AUTHOR>
 * 酷家乐平台的页面处理器实现
 */
@Component
public class KujialeBimPageHandler extends AbstractBimPageHandler {

    private static final QHLogger LOG = QHLogger.getLogger(KujialeBimPageHandler.class);
    private static final String KU_SPACE = "kukongjian";
    private static final String DEFAULT_HOME = "kujiale";
    private static final String KUJIALE_BIM_PATH = "/cloud/tool/h5/bim";
    private static final Pattern PATTERN = Pattern.compile("designid=3(?:F|f)\\w{10}");
    private static final int VALID_SPLIT_NUM = 2;
    private static final Set<String> MULTI_DESIGN_IGNORE_PARAMS = Sets.newHashSet(DESIGN_ID,
            OBS_SER_PLAN_ID, PLAN_NAME, LEVEL_ID, DIRECT_FINISH, REDIRECT_URL);

    public KujialeBimPageHandler(BusinessAccountDb businessAccountDb, DesignService designService,
            UserDb userDb, ProjectDesignFacadeDb projectDesignFacadeDb,
            SynergyFacade synergyFacade, VrcRewriteService vrcRewriteService,
            SessionConflictHandler sessionConflictHandler, ToadProperties toadProperties,
            SaaSConfigService saaSConfigService, ToolLinkConfigCache toolLinkConfigCache,
            UserInfoFacade userInfoFacade, BusinessAccessService businessAccessService,
            VrcService vrcService, AuthCheckService authCheckService) {
        super(businessAccountDb, designService, userDb, projectDesignFacadeDb, synergyFacade,
                vrcRewriteService, sessionConflictHandler, toadProperties, saaSConfigService,
                toolLinkConfigCache, userInfoFacade, businessAccessService, vrcService,
                authCheckService);
    }

    @Override
    protected ToolPageResult handleExistingDesign(HttpServletRequest request, BimPageParam param,
            Long userId, String obsDesignId, String levelId, H5Model h5Model) {
        final Long designId = LongCipher.DEFAULT.decrypt(obsDesignId);

        // 鉴权
        if (!authCheckService.checkAuthFromDesignId(userId, param.getOrderDesignId(),
                designId, param.isCooperate())) {
            return redirectDefaultPage(request);
        }

        // 区分多方案次方案，跳转至主方案承载页
        final Long mainDesignId = designService.getMainDesignId(designId, userId);
        if (mainDesignId != null && !designId.equals(mainDesignId)) {
            return redirectMultiDesignPage(request, mainDesignId);
        }

        final DiyDesignInfo yunDesign = projectDesignFacadeDb.getDiyDesignInfo(designId);

        if (yunDesign == null) {
            return ToolPageResult.create(org.springframework.http.HttpStatus.BAD_REQUEST, null, "design not exist");
        }

        // WARNING：planDb.getDiyDesignInfo 获取到的 vrc 有缓存，这里要去查最新的，避免vrc更新后(如bim切换到酷空间)，获取不到最新的值！
        final ProjectDesign projectDesignWithVrcAndParentId =
                projectDesignFacadeDb.getVrcAndParentId(yunDesign.getDesignId());
        String vrc = projectDesignWithVrcAndParentId == null
                ? null : projectDesignWithVrcAndParentId.getVrc();

        //特殊vrc版本进行修正
        try {
            vrc = vrcRewriteService.correctVrc(vrc, yunDesign.getPlanId(), param);
            h5Model.setVrc(vrc);
        } catch (VrcMappingNotFoundException e) {
            return ToolPageResult.create(org.springframework.http.HttpStatus.BAD_REQUEST, null, "update vrc error");
        }

        boolean isSynergy = synergyFacade.isSynergyDesign(designId, userId);

        fillDesignInfo(h5Model, obsDesignId, yunDesign, levelId, isSynergy,
                projectDesignWithVrcAndParentId == null ||
                        projectDesignWithVrcAndParentId.getCopyLogParentId() == null ? null
                        : Long.valueOf(projectDesignWithVrcAndParentId.getCopyLogParentId()));

        // 酷家乐平台特有的处理逻辑
        ToolPageResult redirectRes = checkDesignOwnerStatus(obsDesignId);
        if (redirectRes != null) {
            return redirectRes;
        }

        // 4.0类型的方案不支持发起协作，协作者需要跳转到错误提示页面
        boolean isInvalidVrcCoworker =
                isSynergy && VrcUtils.isYunDesign(yunDesign) && !userId.equals(
                        yunDesign.getUserId());
        if (isInvalidVrcCoworker) {
            return redirectToSynergyErrorPage();
        }

        // fop session 页面跳转
        URI redirectUri = sessionConflictHandler.checkSessionConflict(
                yunDesign.getDesignId(), userId,
                ToolTypeContextHolder.getToolType() == ToolType.FOP_ORDER);
        if (redirectUri != null) {
            return ToolPageResult.redirect(redirectUri.toString());
        }

        return null;
    }

    @Override
    protected ToolPageResult handleNewDesign(HttpServletRequest request, BimPageParam param,
            Long userId, Long rootAccountId) {
        // 若无bim权限，回列表页
        if (!checkAppAccess(param.getObsAppId(), userId)) {
            LOG.message("authItemOwner - auth failed because no BIM access")
                    .with("obsAppId", param.getObsAppId())
                    .with("userId", userId)
                    .warn();
            return redirectDefaultPage(request);
        }
        return null;
    }

    /**
     * 跳转至多方案主方案，302表示重定向已完成
     */
    private ToolPageResult redirectMultiDesignPage(final HttpServletRequest request,
            Long mainMultiDesignId) {
        try {
            final UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance()
                    .scheme(request.getScheme())
                    .host(request.getHeader(ORIGINAL_HOST_HEADER_NAME))
                    .queryParam(DESIGN_ID, LongCipher.DEFAULT.encrypt(mainMultiDesignId))
                    .path(KUJIALE_BIM_PATH);
            final Map<String, String[]> parameterMap = request.getParameterMap();
            if (parameterMap.containsKey(OBS_SER_PLAN_ID) || parameterMap.containsKey(PLAN_NAME)) {
                final ProjectDesign floorPlan = projectDesignFacadeDb.getProjectByDesignId(
                        mainMultiDesignId);
                if (floorPlan != null) {
                    uriComponentsBuilder.queryParam(OBS_SER_PLAN_ID,
                            LongCipher.DEFAULT.encrypt(floorPlan.getPlanId()));
                    uriComponentsBuilder.queryParam(PLAN_NAME, floorPlan.getDesignName());
                }
            }
            // ParameterMap is unmodifiable
            parameterMap.forEach((key, value) -> {
                if (MULTI_DESIGN_IGNORE_PARAMS.contains(key)) {
                    return;
                }
                uriComponentsBuilder.queryParam(key, value);
            });
            final UriComponents uriComponents = uriComponentsBuilder.build();
            final URI uri = uriComponents.toUri();
            return ToolPageResult.redirect(uri.toString());
        } catch (final Exception e) {
            LOG.message("redirectMultiDesignPage - error")
                    .with(e).error();
            return ToolPageResult.create(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, null, "redirect error");
        }
    }

    /**
     * 重定向到默认页面
     */
    private ToolPageResult redirectDefaultPage(final HttpServletRequest request) {
        String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        String host = toadProperties.getHost();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(originalHost)) {
            host = "//" + originalHost;
            //如果302是酷空间的，前缀保证是酷空间
            if (originalHost.contains(KU_SPACE)) {
                host = host.replace(DEFAULT_HOME, KU_SPACE);
                return sendRedirect(host);
            }
        }

        final String queryString = request.getQueryString();
        if (queryString == null) {
            return sendRedirect(toadProperties.getMyDesignUrl());
        }
        final Matcher matcher = PATTERN.matcher(queryString);
        String[] split = null;
        if (matcher.find()) {
            split = matcher.group().split("=");
            if (split.length < VALID_SPLIT_NUM) {
                return sendRedirect(toadProperties.getMyDesignUrl());
            }
        }
        if (split == null) {
            return sendRedirect(toadProperties.getMyDesignUrl());
        }
        final DiyDesignInfo designInfo =
                projectDesignFacadeDb.getDiyDesignInfo(LongCipher.DEFAULT.decrypt(split[1]));
        if (designInfo == null) {
            return sendRedirect(toadProperties.getMyDesignUrl());
        }
        return sendRedirect(
                host + "/pcenter/design/" + LongCipher.DEFAULT.encrypt(designInfo.getPlanId()) +
                        "/list");
    }

    private ToolPageResult sendRedirect(final String url) {
        try {
            new URI(url); // Validate syntax
            return ToolPageResult.redirect(url);
        } catch (URISyntaxException e) {
            LOG.message("sendRedirect - error")
                    .with("url", url)
                    .with(e)
                    .error();
            return ToolPageResult.create(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, null, "redirect error");
        }
    }
} 