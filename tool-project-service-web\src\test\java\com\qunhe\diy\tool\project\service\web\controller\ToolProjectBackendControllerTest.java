/*
 * ToolProjectBackendControllerTest.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.config.UtilConfig;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.BatchOperationService;
import com.qunhe.diy.tool.project.service.biz.service.ProjectHandoverService;
import com.qunhe.diy.tool.project.service.biz.service.facade.DowngradingService;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.convert.ParamConvertUtil;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectBackendController;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectService;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.interceptors.SessionUserContext;
import com.qunhe.project.platform.project.auth.client.ProjectAuthClient;
import com.qunhe.project.platform.project.auth.security.AuthSecurityConfiguration;
import com.qunhe.project.platform.project.auth.security.WebAuthenticationProvider;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@EnableAspectJAutoProxy
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = { UtilConfig.class, AuthSecurityConfiguration.class,
        WebAuthenticationProvider.class })
@AutoConfigureMockMvc
@WebMvcTest(ToolProjectBackendController.class)
@Import({ ToolProjectBackendController.class })
public class ToolProjectBackendControllerTest {
    static {
        System.setProperty("qunhe.terra.stage", "dev");
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Long TEST_USER_ID = 1111251217L;
    private static final Long TEST_PLAN_ID = 37591209L;
    private static final Long TEST_DESIGN_ID = 37482080L;
    private static final String OBS_PLAN_ID = LongCipher.DEFAULT.encrypt(TEST_PLAN_ID);
    private static final String OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(TEST_DESIGN_ID);

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ToadProperties toadProperties;

    @MockBean
    private ProjectAuthClient authClient;

    @MockBean
    private ToolProjectService toolProjectService;

    @MockBean
    private ProjectHandoverService projectHandoverService;

    @MockBean
    private ProjectDesignFacadeDb projectDesignFacadeDb;

    @MockBean
    private DowngradingService downgradingService;

    @MockBean
    private UserDb userDb;

    @MockBean
    private BatchOperationService batchOperationService;

    @MockBean
    private BatchOperationRecordDb batchOperationRecordDb;

    @MockBean
    private HomeDesignFacadeDb homeDesignFacade;

    @MockBean
    private ParamConvertUtil paramConvertUtil;

    @Before
    public void setUp() {
        // 中文注释: 为每个测试设置通用的用户会话和权限模拟
        UserDto userDto = new UserDto();
        userDto.setUserId(TEST_USER_ID);
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(TEST_USER_ID);
        SessionUserContext.setSessionUser(sessionUser);
        // 中文注释: 模拟通过会话获取用户信息
        when(userDb.getUserBySession()).thenReturn(userDto);
        // 中文注释: 模拟权限检查默认通过
        when(authClient.checkAuth(any())).thenReturn(Result.ok(true));
    }

    @Test
    public void testCreateProject_isAuthenticated_pass() throws Exception {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setDesignId(37482080L);
        toolProjectSaveParam.setPlanId(37483080L);
        toolProjectSaveParam.setPlanType((byte) 10);
        toolProjectSaveParam.setName("1234");
        toolProjectSaveParam.setReworked(false);
        toolProjectSaveParam.setSourceId((byte) 7);

        when(toolProjectService.create(toolProjectSaveParam, false)).thenReturn(null);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_CREATE_API)
                        .header("x-qh-id", "1111251217")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(toolProjectSaveParam)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        Assert.assertEquals("should equal", "0", result.getC());
    }

    @Test
    public void testCreateProject_failure_throwsException() throws Exception {
        // 中文注释: 测试创建项目失败，因为服务层抛出异常
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        // 中文注释: 模拟服务层抛出 ToolProjectCreateException 异常
        when(toolProjectService.create(any(ToolProjectSaveParam.class), anyBoolean()))
                .thenThrow(new ToolProjectCreateException(ErrorCode.PARAM_ERROR));

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_CREATE_API)
                        .header("x-qh-id", String.valueOf(TEST_USER_ID))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(toolProjectSaveParam)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回结果为参数错误的错误码
        Assert.assertEquals("断言返回的错误码与预期一致",
                String.valueOf(ErrorCode.PARAM_ERROR.getCode()), result.getC());
    }

    @Test
    public void testCopyProject_isCoohom_pass() throws Exception {
        // 中文注释: 测试从 Coohom 端复制项目成功
        ToolProjectCopyParam param = new ToolProjectCopyParam();
        ToolProjectHomeDesign expectedResult = new ToolProjectHomeDesign();
        // 中文注释: 模拟服务层成功复制项目
        when(toolProjectService.copyProjectDesign(any(ToolProjectCopyParam.class),
                anyBoolean())).thenReturn(expectedResult);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_COPY_API)
                        .header("x-qh-id", String.valueOf(TEST_USER_ID))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(param)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<ToolProjectHomeDesign> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, ToolProjectHomeDesign.class));
        // 中文注释: 断言操作成功
        Assert.assertEquals("断言返回码为0", "0", result.getC());
        // 中文注释: 断言返回数据不为空
        Assert.assertNotNull("断言返回的数据不为null", result.getD());
    }

    @Test
    public void testRecycleProject_recycleFailure_pass() throws Exception {
        // 中文注释: 测试回收项目失败的场景
        // 中文注释: 模拟服务层回收项目返回 false
        when(toolProjectService.recycleDesign(anyLong(), anyLong(), anyLong())).thenReturn(false);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_DELETE_API)
                        .header("x-qh-id", "1111251217")
                        .param("obsPlanId", OBS_PLAN_ID))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = new ObjectMapper().readValue(response, Result.class);
        // 中文注释: 断言返回数据为 false
        Assert.assertFalse("断言返回数据为false", (Boolean) result.getD());
        // 中文注释: 断言包含特定的失败信息
        Assert.assertEquals("断言返回特定的失败信息", "删除失败，找不到该方案，请确认方案状态",
                result.getM());
    }

    @Test
    public void testBatchRecycleProject_success() throws Exception {
        // 中文注释: 测试批量回收项目成功
        // 中文注释: 模拟批量操作大小限制
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(10);
        BatchDeleteDTO batchDeleteDTO = new BatchDeleteDTO();
        // 中文注释: 模拟服务层批量回收成功
        when(toolProjectService.batchRecycleDesign(anyLong(), any())).thenReturn(batchDeleteDTO);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_DELETE_API)
                        .param("designIds", "[1,2]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<BatchDeleteDTO> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, BatchDeleteDTO.class));
        // 中文注释: 断言操作成功
        Assert.assertEquals("断言返回码为0", "0", result.getC());
    }

    @Test
    public void testBatchRecycleProject_paramOverLimit() throws Exception {
        // 中文注释: 测试批量回收项目时，参数超过限制
        // 中文注释: 设置一个较小的批量操作限制
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(2);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_DELETE_API)
                        .param("designIds", "[1,2,3]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回参数超限的错误码
        Assert.assertEquals("断言返回参数超限错误码",
                String.valueOf(ErrorCode.BATCH_OPERATION_PARAM_OVER_LIMIT.getCode()),
                result.getC());
    }

    @Test
    public void testRecoverProject_recoverFailure_pass() throws Exception {
        // 中文注释: 测试恢复项目失败的场景
        // 中文注释: 模拟服务层恢复项目返回 false
        when(toolProjectService.recoverProjectDesign(anyLong(), anyLong(), anyLong())).thenReturn(
                false);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_RECOVER_API)
                        .param("obsPlanId", OBS_PLAN_ID))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回数据为 false
        Assert.assertFalse("断言返回数据为false", (Boolean) result.getD());
        // 中文注释: 断言包含特定的失败信息
        Assert.assertEquals("断言返回特定的失败信息", "恢复失败，找不到该方案，请确认方案状态",
                result.getM());
    }

    @Test
    public void testBatchRecoverProject_success() throws Exception {
        // 中文注释: 测试批量恢复项目成功
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(10);
        when(toolProjectService.batchRecoverProjectDesign(anyLong(), any())).thenReturn(true);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_RECOVER_API)
                        .param("designIds", "[1,2]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<Boolean> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, Boolean.class));
        // 中文注释: 断言操作成功
        Assert.assertTrue("断言操作成功", result.getD());
    }

    @Test
    public void testBatchRecoverProject_failure() throws Exception {
        // 中文注释: 测试批量恢复项目失败
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(10);
        when(toolProjectService.batchRecoverProjectDesign(anyLong(), any())).thenReturn(false);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_RECOVER_API)
                        .param("designIds", "[1,2]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<Boolean> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, Boolean.class));
        // 中文注释: 断言操作失败
        Assert.assertFalse("断言操作失败", result.getD());
        // 中文注释: 断言包含失败信息
        Assert.assertEquals("断言包含失败信息", "恢复失败，找不到该方案，请确认方案状态",
                result.getM());
    }

    @Test
    public void testBatchRecoverProject_paramOverLimit() throws Exception {
        // 中文注释: 测试批量恢复项目参数超限
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(2);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_RECOVER_API)
                        .param("designIds", "[1,2,3]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回参数超限错误
        Assert.assertEquals("断言返回参数超限错误",
                String.valueOf(ErrorCode.BATCH_OPERATION_PARAM_OVER_LIMIT.getCode()),
                result.getC());
    }

    @Test
    public void testBatchRecoverProject_throwsException() throws Exception {
        // 中文注释: 测试批量恢复项目时服务层抛出异常
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(10);
        when(toolProjectService.batchRecoverProjectDesign(anyLong(), any()))
                .thenThrow(new ToolProjectCreateException(ErrorCode.PARAM_ERROR));

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_RECOVER_API)
                        .param("designIds", "[1,2]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回参数错误
        Assert.assertEquals("断言返回参数错误", String.valueOf(ErrorCode.PARAM_ERROR.getCode()),
                result.getC());
    }

    @Test
    public void testRemoveProject_failure() throws Exception {
        // 中文注释: 测试彻底删除项目失败
        when(toolProjectService.deleteDesign(anyLong(), anyLong(), anyLong())).thenReturn(false);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_REMOVE_API)
                        .param("obsPlanId", OBS_PLAN_ID))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<Boolean> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, Boolean.class));
        // 中文注释: 断言操作失败
        Assert.assertFalse("断言操作失败", result.getD());
        // 中文注释: 断言包含失败信息
        Assert.assertEquals("断言包含失败信息", "彻底删除失败，找不到该方案，请确认方案状态",
                result.getM());
    }

    @Test
    public void testBatchRemoveProject_success() throws Exception {
        // 中文注释: 测试批量彻底删除项目成功
        BatchOperatorVO batchOperatorVO = new BatchOperatorVO();
        batchOperatorVO.setUserId(TEST_USER_ID);
        when(projectDesignFacadeDb.countDesignsByUserIdIgnoreRecycle(anyLong())).thenReturn(10);
        doReturn(1).when(batchOperationRecordDb).insertRecord(any(BatchOperationData.class));
        doNothing().when(batchOperationService).batchRemoveProjects(anyString(), anyLong(),
                any(BatchOperationData.class));

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_REMOVE_API)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(batchOperatorVO)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();
        final Result<String> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory().constructParametricType(Result.class, String.class));
        // 中文注释: 断言返回码为0
        Assert.assertEquals("断言返回码为0", "0", result.getC());
        // 中文注释: 断言返回的recordId不为空
        Assert.assertTrue("断言返回的recordId不为空",
                result.getD().contains(String.valueOf(TEST_USER_ID)));
    }

    @Test
    public void testBatchRemoveProject_originCountEmpty() throws Exception {
        // 中文注释: 测试批量彻底删除项目时，没有可删除的项目
        BatchOperatorVO batchOperatorVO = new BatchOperatorVO();
        batchOperatorVO.setUserId(TEST_USER_ID);
        // 中文注释: 模拟没有可删除的项目
        when(projectDesignFacadeDb.countDesignsByUserIdIgnoreRecycle(anyLong())).thenReturn(0);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_REMOVE_API)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(batchOperatorVO)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回操作前数据为空的错误码
        Assert.assertEquals("断言返回操作前数据为空错误",
                String.valueOf(ErrorCode.BATCH_OPERATION_ORIGIN_COUNT_EMPTY.getCode()),
                result.getC());
    }

    @Test
    public void testGetBatchOperateStatus_success() throws Exception {
        // 中文注释: 测试获取批量操作状态成功
        when(batchOperationService.getBatchOperateStatus(anyString())).thenReturn(
                BatchOperationStatusEnum.DONE);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .get(ApiConstant.PROJECT_BATCH_OPERATE_STATUS_API)
                        .param("userId", String.valueOf(TEST_USER_ID))
                        .param("recordId", "testRecordId"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<BatchOperationStatusEnum> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, BatchOperationStatusEnum.class));
        // 中文注释: 断言返回的状态为成功
        Assert.assertEquals("断言返回状态为成功", BatchOperationStatusEnum.DONE, result.getD());
    }

    @Test
    public void testGetBatchOperateStatus_paramInvalid() throws Exception {
        // 中文注释: 测试获取批量操作状态时参数无效
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .get(ApiConstant.PROJECT_BATCH_OPERATE_STATUS_API)
                        .param("userId", String.valueOf(TEST_USER_ID))
                        .param("recordId", ""))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回参数无效的错误码
        Assert.assertEquals("断言返回参数无效错误",
                String.valueOf(ErrorCode.BATCH_OPERATION_PARAM_INVALID.getCode()), result.getC());
    }

    @Test
    public void testGetBatchOperateStatus_throwsException() throws Exception {
        // 中文注释: 测试获取批量操作状态时服务层抛出异常
        when(batchOperationService.getBatchOperateStatus(anyString())).thenThrow(
                new RuntimeException("test exception"));
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .get(ApiConstant.PROJECT_BATCH_OPERATE_STATUS_API)
                        .param("userId", String.valueOf(TEST_USER_ID))
                        .param("recordId", "testRecordId"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = OBJECT_MAPPER.readValue(response, Result.class);
        // 中文注释: 断言返回获取状态错误的错误码
        Assert.assertEquals("断言返回获取状态错误",
                String.valueOf(ErrorCode.GET_BATCH_OPERATION_STATUS_ERROR.getCode()),
                result.getC());
    }

    @Test
    public void testBatchRollbackRemoveProject_success() throws Exception {
        // 中文注释: 测试批量回滚删除操作成功
        BatchOperatorVO batchOperatorVO = new BatchOperatorVO();
        batchOperatorVO.setUserId(TEST_USER_ID);
        List<Long> designIds = Arrays.asList(1L, 2L);
        when(batchOperationService.getBatchDealDesignIdsFromLinks(anyLong())).thenReturn(designIds);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_BATCH_ROLLBACK_API)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(OBJECT_MAPPER.writeValueAsString(batchOperatorVO)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<String> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory().constructParametricType(Result.class, String.class));
        // 中文注释: 断言返回码为0
        Assert.assertEquals("断言返回码为0", "0", result.getC());
    }

    @Test
    public void testHandoverProject_success() throws Exception {
        // 中文注释: 测试项目移交成功
        doNothing().when(projectHandoverService).handoverProject(anyLong(), any(), anyLong());
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .post(ApiConstant.PROJECT_HANDOVER_API)
                        .param("taskId", "123")
                        .param("toUserId", "456")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("[789]"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<Boolean> result = OBJECT_MAPPER.readValue(response,
                OBJECT_MAPPER.getTypeFactory()
                        .constructParametricType(Result.class, Boolean.class));
        // 中文注释: 断言操作成功
        Assert.assertTrue("断言操作成功", result.getD());
    }

    @Test
    public void testNeedDesignRevision_true() throws Exception {
        // 中文注释: 测试需要设计版本
        when(toolProjectService.getProjectDesignByDesignId(anyLong())).thenReturn(null);
        when(toolProjectService.needDesignRevision(any())).thenReturn(true);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .get(ApiConstant.PROJECT_NEED_DESIGN_REVISION)
                        .param("designId", String.valueOf(TEST_DESIGN_ID)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        // 中文注释: 断言返回true
        Assert.assertEquals("断言返回true", "true", response);
    }

    @Test
    public void testNeedDesignRevision_false() throws Exception {
        // 中文注释: 测试不需要设计版本
        when(toolProjectService.getProjectDesignByDesignId(anyLong())).thenReturn(null);
        when(toolProjectService.needDesignRevision(any())).thenReturn(false);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .get(ApiConstant.PROJECT_NEED_DESIGN_REVISION)
                        .param("designId", String.valueOf(TEST_DESIGN_ID)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        // 中文注释: 断言返回false
        Assert.assertEquals("断言返回false", "false", response);
    }

    @Test
    public void testModifyProject_hasWrite_pass() throws Exception {
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(1111136848L);
        SessionUserContext.setSessionUser(sessionUser);
        when(authClient.checkAuth(any())).thenReturn(Result.ok(true));

        ToolProjectModifyParam toolProjectModifyParam = new ToolProjectModifyParam();
        toolProjectModifyParam.setObsPlanId("3FO4M8E8TQFA");
        toolProjectModifyParam.setObsDesignId("3FO4M8EH7GFT");
        toolProjectModifyParam.setCommName("杭州");
        toolProjectModifyParam.setName("我是测试");
        toolProjectModifyParam.setAreaId("175");
        toolProjectModifyParam.setObsStdCommId("1");
        when(toolProjectService.modifyProjectInfo(any(), any())).thenReturn(
                Result.ok(true));
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .put(ApiConstant.PROJECT_INFO_API)
                        .header("x-qh-id", "1111136848")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(toolProjectModifyParam)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        final Result<?> result = new ObjectMapper().readValue(response, Result.class);
        Assert.assertEquals("", true, result.getD());
    }

}
