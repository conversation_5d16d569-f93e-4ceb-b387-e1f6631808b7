<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ starter-pom.xml
  ~ Copyright 2018 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tool-project-service-client</artifactId>
    <version>0.0.18-SNAPSHOT</version>

    <parent>
        <groupId>com.qunhe.diy</groupId>
        <artifactId>tool-project-service-parent</artifactId>
        <version>0.0.18-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.qunhe.diy</groupId>
            <artifactId>tool-project-service-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.utils</groupId>
            <artifactId>webstandard</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>4.3.20.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>4.3.20.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe</groupId>
            <artifactId>diymanage-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleware</groupId>
            <artifactId>toad-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.instdeco</groupId>
            <artifactId>exacloud-test</artifactId>
            <version>0.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qunhe.hunter</groupId>
            <artifactId>hunter-spring-web-servlet-filter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>qunhe-releases</id>
            <name>Qunhe Release Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>qunhe-snapshots</id>
            <name>Qunhe Snapshot Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
