/*
 * DesignBatchGetRequest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data.restapi;

import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.module.restapi.common.data.request.RestApiBatchRequest;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@ToString
public class RestApiLevelBatchUpdateRequest extends RestApiBatchRequest<LevelInfo> {


}
