/*
 * BusinessAccountFacadeTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.instdeco.businessaccount.sdk.client.BusinessAccountClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class BusinessAccountFacadeTest {

    @Mock
    private BusinessAccountClient mockBusinessAccountClient;

    @InjectMocks
    private BusinessAccountFacade businessAccountFacadeUnderTest;

    @Test
    public void testGetAccountId_BusinessAccountClientReturnsNull() {
        // Run the test
        final Long result = businessAccountFacadeUnderTest.getAccountId(0L);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetAccountIdFallBack() {
        assertThat(businessAccountFacadeUnderTest.getAccountIdFallBack(0L, new Exception("message"))).isNull();
    }
}
