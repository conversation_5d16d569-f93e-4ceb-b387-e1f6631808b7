/*
 * CommunityClient.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.client;

import com.qunhe.diy.tool.project.service.api.CommunityApi;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diy.tool.project.service.factory.ApiFactory;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import com.qunhe.rpc.common.builder.ParamPairsBuilder;
import com.qunhe.rpc.common.utils.Pair;
import com.qunhe.web.standard.data.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@DependsOn({ "tpsApiFactory" })
public class CommunityClient {

    public final CommunityApi communityApi;

    @Autowired
    public CommunityClient(final ApiFactory apiFactory) {
        this.communityApi = apiFactory.getApiInstance(CommunityApi.class);
    }

    public Result<Community> getOrCreateProjectCommunity(final String province,
            final String city, final String communityName) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(3)
                .append("province", province)
                .append("city", city)
                .append("communityName", communityName)
                .build();
        return communityApi.getOrCreateProjectCommunity(params);
    }

    public String getCommNameByCommId(final Long commId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("commid", commId)
                .build();
        return communityApi.getCommNameByCommId(params);
    }

    public SysDictArea getSysDictAreaByCommId(final Long commId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("commid", commId)
                .build();
        return communityApi.getSysDictAreaByCommId(params);
    }

    public Result<LocationParam> calculateIpLocation(final Long designId, final String areaId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("designid", designId)
                .append("areaid", areaId)
                .build();
        return communityApi.calculateIpLocation(params);
    }
}
