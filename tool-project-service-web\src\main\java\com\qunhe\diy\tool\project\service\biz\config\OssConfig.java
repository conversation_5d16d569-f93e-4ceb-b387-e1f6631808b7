/*
 * OssConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.diy.tool.project.service.common.util.CommonUtil;
import com.qunhe.middleware.terra.client.SimpleStorageFactory;
import com.qunhe.middleware.terra.client.qhossclient.QhOssClient;
import com.qunhe.middleware.terra.common.configurations.OSSConfiguration;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class OssConfig {
    private static final ConcurrentHashMap<String, QhOssClient> OSS_CLIENT_MAP =
            new ConcurrentHashMap<>();

    public static final String QHTBD = "qhtbd";

    public static final String SEPARATOR_SLASH = "/";

    public static final String COS_TASK_PREFIX = "/tps/batch_operate";

    public static final String JSON_SUFFIX = ".json";

    public static final String OCTET_STREAM = "application/octet-stream";

    public static final String QHTBD_CDN = "https://qhtbdoss.kujiale.com";

    private static final int DEFAULT_CONNECTION_TIMEOUT = 3000;

    private static final int DEFAULT_MAX_ERROR_RETRY = 1;

    private static final int DEFAULT_SOCKET_TIMEOUT = 5000;

    private static final OSSConfiguration DEFAULT_CONFIG = OSSConfiguration.builder()
            .connectionTimeout(DEFAULT_CONNECTION_TIMEOUT)
            .maxErrorRetry(DEFAULT_MAX_ERROR_RETRY)
            .socketTimeout(DEFAULT_SOCKET_TIMEOUT)
            .build();

    public static QhOssClient getBatchOperationCosClient() {
        return getOssClient(QHTBD, DEFAULT_CONFIG);
    }

    public static QhOssClient getOssClient(final String bucket, final OSSConfiguration config) {
        return OSS_CLIENT_MAP.computeIfAbsent(bucket,
                k -> SimpleStorageFactory.buildQhOssClientProxyByLogicBucket(bucket, config));
    }

    public static String generateCosKey(String type, String recordId) {
        String dateStr = CommonUtil.getCurrentTime();
        return String.join(SEPARATOR_SLASH, COS_TASK_PREFIX,
                type, dateStr, recordId);
    }

    public static String extractKeyFromCdnUrl(String prefix, final String key) {
        return key.replace(prefix, "");
    }
}
