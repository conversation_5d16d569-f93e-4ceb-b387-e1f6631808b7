/*
 * HandoverJobRspMessage.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.properties;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Function: 方案移交，用户的数据返回体
 *
 * <AUTHOR>
 */
@Data
@Builder
public class HandoverJobRspMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long taskId;
    private Integer category;
    private TaskInfo taskInfo;
}
