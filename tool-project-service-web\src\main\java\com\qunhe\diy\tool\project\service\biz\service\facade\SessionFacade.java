/*
 * SessionFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.service.facade;


import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diybe.diyservice.client.SessionClient;
import com.qunhe.diybe.diyservice.common.data.SessionExistRes;
import com.qunhe.diybe.diyservice.exception.DiyServiceException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SessionFacade {

    @Autowired
    private SessionClient sessionClient;

    @SentinelResource(value = "sessionExists")
    public boolean sessionExists(String obsDesignId, Long userId) throws DiyServiceException {
        return sessionClient.sessionExists(userId, obsDesignId);
    }

    @SentinelResource(value = "querySessionStatus")
    public SessionExistRes querySessionStatus(Long designId) throws DiyServiceException {
        return sessionClient.querySessionStatus(LongCipher.DEFAULT.encrypt(designId), null);
    }

}
