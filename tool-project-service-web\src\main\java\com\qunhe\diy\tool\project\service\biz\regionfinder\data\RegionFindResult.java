/*
 * RegionFindResult.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.regionfinder.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.qunhe.diybe.dms.data.Region;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegionFindResult {

    private Region region;

    private boolean isFallback;

}
