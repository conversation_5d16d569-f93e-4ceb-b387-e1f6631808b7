package com.qunhe.diy.tool.project.service.web.project;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.BatchOperationService;
import com.qunhe.diy.tool.project.service.biz.service.ProjectHandoverService;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ToolProjectBackendControllerTest {

    @InjectMocks
    private ToolProjectBackendController controller;

    @Mock
    private ToolProjectService toolProjectService;

    @Mock
    private ProjectHandoverService projectHandoverService;

    @Mock
    private BatchOperationRecordDb batchOperationRecordDb;

    @Mock
    private BatchOperationService batchOperationService;

    @Mock
    private ToadProperties toadProperties;

    @Mock
    private UserDb userDb;

    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;

    private static final Long DESIGN_ID_DECRYPTED = 123L;
    private static final String OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(DESIGN_ID_DECRYPTED);
    private static final Long PLAN_ID_DECRYPTED = 456L;
    private static final String OBS_PLAN_ID = LongCipher.DEFAULT.encrypt(PLAN_ID_DECRYPTED);

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        lenient().when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(100);
    }



    @Test
    public void testRecycleProject_WithDesignId() {
        // 使用designId测试项目回收
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(userDb.getUserBySession()).thenReturn(userDto);
        when(toolProjectService.recycleDesign(userId, null, DESIGN_ID_DECRYPTED)).thenReturn(true);

        Result<Boolean> result = controller.recycleProject(null, null, null, DESIGN_ID_DECRYPTED);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }

    @Test
    public void testRecycleProject_WithObsDesignId() {
        // 使用obsDesignId测试项目回收
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(toolProjectService.recycleDesign(userId, null, DESIGN_ID_DECRYPTED)).thenReturn(true);

        Result<Boolean> result = controller.recycleProject(null, OBS_DESIGN_ID, null, null);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }

    @Test
    public void testBatchRecycleProject_ExceedLimit() {
        // 测试批量回收超过限制的情况
        String designIds = "[1,2,3]";
        when(toadProperties.getBatchOperationParamSizeLimit()).thenReturn(2);

        Result<BatchDeleteDTO> result = controller.batchRecycleProject(designIds);

        assertNotNull("返回结果不应为null", result);
        assertFalse("结果应为失败状态", result.success());
        assertEquals("错误码应匹配", ErrorCode.BATCH_OPERATION_PARAM_OVER_LIMIT.getCode(),
                result.getC());
        verify(toolProjectService, never()).batchRecycleDesign(anyLong(), anyList());
    }

    @Test
    public void testRecoverProject_Success() throws ToolProjectCreateException {
        // 测试项目恢复成功的情况
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(userDb.getUserBySession()).thenReturn(userDto);
        when(toolProjectService.recoverProjectDesign(userId, null, DESIGN_ID_DECRYPTED)).thenReturn(
                true);

        Result<Boolean> result = controller.recoverProject(null, OBS_DESIGN_ID, null, null);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }


    @Test
    public void testBatchRecoverProject_Success() throws ToolProjectCreateException {
        // 测试批量恢复成功的情况
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(userDb.getUserBySession()).thenReturn(userDto);
        when(toolProjectService.batchRecoverProjectDesign(userId,
                Arrays.asList(1L, 2L))).thenReturn(true);

        String designIds = "[1,2]";
        Result<Boolean> result = controller.batchRecoverProject(designIds);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
        assertTrue("返回值应为true", result.getD());
        verify(toolProjectService, times(1)).batchRecoverProjectDesign(userId,
                Arrays.asList(1L, 2L));
    }

    @Test
    public void testBatchRecoverProject_EmptyList() {
        // 测试批量恢复空列表的情况
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(userDb.getUserBySession()).thenReturn(userDto);
        String designIds = "[]";
        Result<BatchDeleteDTO> result = controller.batchRecycleProject(designIds);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }

    @Test
    public void testRestoreProjectDesign_WithDesignId() throws ToolProjectCreateException {
        // 使用designId测试项目恢复修改时间
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(userDb.getUserBySession()).thenReturn(userDto);
        when(toolProjectService.restoreProjectDesign(userId, null, DESIGN_ID_DECRYPTED,
                false)).thenReturn(true);

        Result<Boolean> result = controller.restoreProjectDesign(null, OBS_DESIGN_ID, null, null,
                false);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }


    @Test
    public void testBatchRemoveProject_EmptyOriginCount() {
        // 测试批量删除时原始数量为空的情况
        BatchOperatorVO vo = new BatchOperatorVO();
        vo.setUserId(1L);
        when(projectDesignFacadeDb.countDesignsByUserIdIgnoreRecycle(1L)).thenReturn(0);

        Result<String> result = controller.batchRemoveProject(vo);

        assertNotNull("返回结果不应为null", result);
        assertFalse("结果应为失败状态", result.success());
        assertEquals("错误码应匹配", ErrorCode.BATCH_OPERATION_ORIGIN_COUNT_EMPTY.getCode(),
                result.getC());
        verify(batchOperationRecordDb, never()).insertRecord(any(BatchOperationData.class));
        verify(batchOperationService, never()).batchRemoveProjects(anyString(), anyLong(),
                any(BatchOperationData.class));
    }

    @Test
    public void testGetBatchOperateStatus_InvalidParams() {
        // 测试获取批量操作状态时参数无效的情况
        Result<BatchOperationStatusEnum> result = controller.getBatchOperateStatus(null, null);

        assertNotNull("返回结果不应为null", result);
        assertFalse("结果应为失败状态", result.success());
        assertEquals("错误码应匹配", ErrorCode.BATCH_OPERATION_PARAM_INVALID.getCode(),
                result.getC());
        verify(batchOperationService, never()).getBatchOperateStatus(anyString());
    }

    @Test
    public void testGetBatchOperateStatus_Success() {
        // 测试获取批量操作状态成功的情况
        when(batchOperationService.getBatchOperateStatus("testId")).thenReturn(
                BatchOperationStatusEnum.DONE);

        Result<BatchOperationStatusEnum> result = controller.getBatchOperateStatus(1L, "testId");

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
        assertEquals("状态应匹配", BatchOperationStatusEnum.DONE, result.getD());
        verify(batchOperationService, times(1)).getBatchOperateStatus("testId");
    }

    @Test
    public void testBatchRollbackRemoveProject_NoDesignIds() {
        // 测试批量回滚时无有效designIds的情况
        BatchOperatorVO vo = new BatchOperatorVO();
        vo.setUserId(1L);
        when(batchOperationService.getBatchDealDesignIdsFromLinks(1L)).thenReturn(
                Collections.emptyList());

        Result<String> result = controller.batchRollbackRemoveProject(vo);

        assertNotNull("返回结果不应为null", result);
        assertFalse("结果应为失败状态", result.success());
        assertEquals("错误码应匹配", ErrorCode.BATCH_OPERATION_ORIGIN_COUNT_EMPTY.getCode(),
                result.getC());
        verify(batchOperationRecordDb, never()).insertRecord(any(BatchOperationData.class));
        verify(batchOperationService, never()).batchRollBackProjects(anyLong(), anyString(),
                any(BatchOperationData.class), anyList());
    }


    @Test
    public void testRollbackRemoveProject_Success() throws ToolProjectCreateException {
        // 测试单个回滚成功的情况
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        when(toolProjectService.rollbackProjectDesign(userId, null,
                DESIGN_ID_DECRYPTED)).thenReturn(true);

        Result<Boolean> result = controller.rollbackRemoveProject(null, OBS_DESIGN_ID, null, null);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
    }

    @Test
    public void testRollbackRemoveProject_Exception() throws ToolProjectCreateException {
        // 测试单个回滚时抛出异常的情况
        Long userId = 1L;
        UserDto userDto = new UserDto();
        userDto.setUserId(userId);
        lenient().doThrow(new ToolProjectCreateException(ErrorCode.PARAM_ERROR))
                .when(toolProjectService).rollbackProjectDesign(userId, null, DESIGN_ID_DECRYPTED);

        Result<Boolean> result = controller.rollbackRemoveProject(null, OBS_DESIGN_ID, null, null);

        assertNotNull("返回结果不应为null", result);
    }

    @Test
    public void testHandoverProject_Success() {
        // 测试项目交接成功的情况
        Result<Boolean> result = controller.handoverProject(1L, Arrays.asList(1L, 2L), 3L);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为成功状态", result.success());
        assertTrue("返回值应为true", result.getD());
        verify(projectHandoverService, times(1)).handoverProject(3L, Arrays.asList(1L, 2L), 1L);
    }

    @Test
    public void testModifyProjectInfo_NullParam() {
        // 测试修改项目信息时参数为null的情况
        Result<Boolean> result = controller.modifyProjectInfo(null, null, null);

        assertNotNull("返回结果不应为null", result);
        assertFalse("结果应为失败状态", result.success());
        assertEquals("错误码应匹配", ErrorCode.PARAM_ERROR.getCode(), result.getC());
        verify(toolProjectService, never()).modifyProjectInfo(anyLong(),
                any(ToolProjectModifyParam.class));
    }

    @Test
    public void testNeedDesignRevision() {
        // 测试是否需要设计修订的情况
        ProjectDesign design = new ProjectDesign();
        when(toolProjectService.getProjectDesignByDesignId(1L)).thenReturn(design);
        when(toolProjectService.needDesignRevision(design)).thenReturn(true);

        Boolean result = controller.needDesignRevision(null, null, 1L);

        assertNotNull("返回结果不应为null", result);
        assertTrue("结果应为true", result);
        verify(toolProjectService, times(1)).getProjectDesignByDesignId(1L);
        verify(toolProjectService, times(1)).needDesignRevision(design);
    }
}