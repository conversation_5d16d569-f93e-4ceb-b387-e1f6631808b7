/*
 * ToolTypeContextHolder.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.context;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ToolTypeContextHolder {

    private static final ThreadLocal<ToolType> TOOL_TYPE_HOLDER = new ThreadLocal<>();

    private static final ThreadLocal<String> VRC_HOLDER = new ThreadLocal<>();

    private static final ThreadLocal<ToolType> NEW_TOOL_TYPE_HOLDER = new ThreadLocal<>();

    public static void setToolType(final ToolType toolType) {
        TOOL_TYPE_HOLDER.set(toolType);
    }

    public static void setNewToolType(final ToolType toolType) {
        NEW_TOOL_TYPE_HOLDER.set(toolType);
    }

    public static void clear() {
        TOOL_TYPE_HOLDER.remove();
        VRC_HOLDER.remove();
        NEW_TOOL_TYPE_HOLDER.remove();
    }

    public static ThreadLocal<String> getVrcHolder() {
        return VRC_HOLDER;
    }

    public static ToolType getToolType() {
        return TOOL_TYPE_HOLDER.get();
    }

    public static boolean isYunDesign() {
        final ToolType toolType = TOOL_TYPE_HOLDER.get();
        if (toolType == null) {
            return false;
        }
        return toolType.equals(ToolType.YUN_DESIGN);
    }

    public static boolean isYunDesignCustom() {
        final ToolType toolType = TOOL_TYPE_HOLDER.get();
        final ToolType newToolTYpe = NEW_TOOL_TYPE_HOLDER.get();
        return Objects.equals(newToolTYpe, ToolType.CUSTOM_ORDER) || Objects.equals(toolType,
                ToolType.YUN_DESIGN_CUSTOM_VERIFY);
    }
}
