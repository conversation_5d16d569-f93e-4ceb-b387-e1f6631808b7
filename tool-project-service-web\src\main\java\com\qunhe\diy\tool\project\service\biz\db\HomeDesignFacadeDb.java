/*
 * HomeDesignFacadeDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.diy.tool.project.service.biz.annotation.MultiLevelRevision;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectLevelDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.client.HomeDesignClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.log.QHLogger;
import com.qunhe.middleware.toad.client.common.JsonUtils;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class HomeDesignFacadeDb {

    private static final QHLogger LOG = QHLogger.getLogger(HomeDesignFacadeDb.class);

    private final HomeDesignClient homeDesignClient;

    @Autowired
    public HomeDesignFacadeDb(final HomeDesignClient homeDesignClient) {
        this.homeDesignClient = homeDesignClient;
    }

    @MultiLevelRevision(planId = "#projectSequence.planId", designId = "#projectSequence.designId")
    @SentinelResource(value = "createHomeDesign")
    public HomeDesignData createHomeDesign(final ProjectSequence projectSequence,
            final ToolProjectSaveParam toolProjectSaveParam) throws DiyManageServiceException {
        return homeDesignClient.getOrCreateHomeDesign(projectSequence.getPlanId(),
                projectSequence.getDesignId(), toolProjectSaveParam.getUserId(),
                toolProjectSaveParam.getLevelName());
    }

    @SentinelResource(value = "batchGetHomeDesignData")
    @SneakyThrows(DiyManageServiceException.class)
    public Map<Long, HomeDesignData> batchGetHomeDesignData(final List<Long> designIds) {
        return homeDesignClient.batchGetHomeDesignData(
                JsonUtils.objectToJson(
                        designIds)).stream().collect(
                Collectors.toMap(a -> a.getDesignId(), a -> a, (a, b) -> a));
    }

    public HomeDesignData getOrCreateHomeDesign(Long planId, Long designId, Long userId) {
        try {
            return homeDesignClient.getOrCreateHomeDesign(planId, designId, userId);
        } catch (DiyManageServiceException e) {
            LOG.message("getOrCreateHomeDesign error", e)
                    .with("planId", planId)
                    .with("designId", designId)
                    .with("userId", userId)
                    .error();
        }
        return null;
    }

    @SentinelResource(value = "copyHomeDesign", blockHandler = "copyHomeDesignFallBack")
    public HomeDesignData copyHomeDesignIgnoreRecycles(final ProjectDesign srcProjectDesign,
            final ToolProjectHomeDesign dstProjectDesign)
            throws DiyManageServiceException {
        return homeDesignClient.copyHomeDesignIgnoreRecycles(srcProjectDesign.getPlanId(),
                srcProjectDesign.getDesignId(), dstProjectDesign.getPlanId(),
                dstProjectDesign.getDesignId());
    }

    public HomeDesignData copyHomeDesignFallBack(final ProjectDesign srcProjectDesign,
            final ToolProjectHomeDesign dstProjectDesign, final BlockException e)
            throws DiyManageServiceException {
        LOG.message("copyHomeDesignFallBack")
                .with("srcDesignId", srcProjectDesign.getDesignId())
                .with("srcPlanId", srcProjectDesign.getPlanId())
                .with("dstDesignId", dstProjectDesign.getDesignId())
                .with("dstPlanId", dstProjectDesign.getPlanId())
                .error();
        throw new DiyManageServiceException("copyHomeDesign error");
    }

    public List<ToolProjectLevelDesign> generateLevelDesign(final HomeDesignData homeDesignData) {
        final List<ToolProjectLevelDesign> levels = new LinkedList<>();
        int start = -1;
        for (final String levelId : homeDesignData.getUndergroundLevels()) {
            levels.add(0, new ToolProjectLevelDesign(start--, levelId));
        }
        start = 1;
        for (final String levelId : homeDesignData.getOvergroundLevels()) {
            levels.add(new ToolProjectLevelDesign(start++, levelId));
        }
        return levels;
    }

    @SentinelResource(value = "batchCreateOrUpdateLevels")
    public LevelBatchUpdateResponse batchCreateOrUpdateLevels(final Long designId,
            final List<LevelInfo> levelInfos) throws DiyManageServiceException {
        return homeDesignClient.batchCreateOrUpdateLevels(designId, levelInfos);
    }

    public HomeDesignData getHomeDesign(final Long designId) {
        return homeDesignClient.getHomeDesign(designId);
    }

    public boolean saveHomeDesign(final HomeDesignData homeDesignData) {
        return homeDesignClient.saveHomeDesignData(homeDesignData);
    }

}
