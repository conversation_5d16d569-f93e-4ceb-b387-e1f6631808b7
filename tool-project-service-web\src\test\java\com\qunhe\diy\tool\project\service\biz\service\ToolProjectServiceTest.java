/*
 * ToolProjectServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.FloorPlanMetaDataDb;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.regionfinder.ProjectRegionFinder;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFindResult;
import com.qunhe.diy.tool.project.service.biz.service.facade.AlbumService;
import com.qunhe.diy.tool.project.service.biz.service.facade.DowngradingService;
import com.qunhe.diy.tool.project.service.biz.service.facade.ProjectNotifyService;
import com.qunhe.diy.tool.project.service.biz.service.facade.ProjectSearchService;
import com.qunhe.diy.tool.project.service.biz.service.facade.RedisService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SecurityDetectService;
import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.DesignSearchableStatus;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.CommunityInfo;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.project.ToolProjectService;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.data.Region;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.i18n.locale.context.FileMessageSource;
import com.qunhe.instdeco.picservice.data.Album;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.projectmanagement.client.data.QueryByDesignIdParam;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.crypt.LongCrypt;
import com.qunhe.utils.uniqueid.UniqueIdGenerator;
import com.qunhe.web.standard.data.Result;
import jersey.repackaged.com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class ToolProjectServiceTest {
    private static final String DEFAULT_COMMUNITY_NAME = "未知小区";

    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    HomeDesignFacadeDb homeDesignFacadeDb;
    @Mock
    UniqueIdGenerator uniqueIdGenerator;
    @Mock
    ProjectNotifyService projectNotifyService;
    @Mock
    UserDb userDb;
    @Mock
    GeneralProperties generalProperties;
    @Mock
    DowngradingService downgradingService;
    @Mock
    SaaSConfigService saaSConfigService;
    @Mock
    ProjectSearchService projectSearchService;
    @Mock
    AlbumService albumService;
    @Mock
    RedisService redisService;
    @Mock
    ProjectAuthService projectAuthService;
    @Mock
    TrialUserFacade trialUserFacade;
    @Mock
    CommunityService communityService;
    @Mock
    SecurityDetectService securityDetectService;
    @Mock
    LongCrypt longCrypt;
    @Mock
    CommunityDb communityDb;
    @Mock
    SysDictAreaMapper sysDictAreaMapper;
    @Mock
    FloorPlanMetaDataDb floorPlanMetaDataDb;
    @Mock
    ToadProperties toadProperties;
    @Mock
    LevelDesignFacadeDb levelDesignFacadeDb;

    @Mock
    ProjectClient projectClient;

    @InjectMocks
    ToolProjectService toolProjectService;

    @Mock
    private ProjectRegionFinder projectRegionFinder;
    
    @Mock
    private FileMessageSource fileMessageSource;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateProjectDesign() throws Exception {
        Long designId = 1L;
        ProjectSequence projectSequence = new ProjectSequence();
        projectSequence.setDesignId(designId);
        when(projectDesignFacadeDb.createProject(any())).thenReturn(projectSequence);
        when(projectDesignFacadeDb.deleteProject(anyLong())).thenReturn(0);
        when(generalProperties.getCountCheck()).thenReturn(Boolean.TRUE);
        when(generalProperties.getCountLimit()).thenReturn(5L);
        when(saaSConfigService.checkProjectCountPointAccess(anyLong())).thenReturn(Boolean.TRUE);
        when(projectSearchService.countProject(anyLong())).thenReturn(0L);
        UserDto user = UserDto.builder().userId(1111251217L).build();
        when(userDb.getUser(anyLong())).thenReturn(user);
        Album album = new Album();
        album.setDesignId(designId);
        when(albumService.getAlbum(any())).thenReturn(album);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setDesignId(designId);

        ToolProjectSaveParam toolProjectSaveParam = ToolProjectSaveParam.builder()
                .userId(1111251217L)
                .designId(designId)
                .albumId(11L)
                .area(-0.01)
                .srcArea(-0.1)
                .realArea(-0.1)
                .reworked(true)
                .recommend(false)
                .name("未命名户型图")
                .planType((byte) 10)
                .sourceId((byte) 7)
                .build();
        when(homeDesignFacadeDb.createHomeDesign(projectSequence,toolProjectSaveParam)).thenReturn(homeDesignData);
        final LocationParam locationParam =
                LocationParam.builder().areaId(39).sysParentAreaId(39).name(DEFAULT_COMMUNITY_NAME).build();
        when(communityService.getLocationParam(false, 1111251217L, (byte)7)).thenReturn(locationParam);

        Community community = new Community();
        community.setCommId(161265L);
        community.setAreaId(39L);
        community.setName(DEFAULT_COMMUNITY_NAME);
        when(communityService.getOrCreateProjectCommunityByAreaIdAndName(39L, DEFAULT_COMMUNITY_NAME)).thenReturn(community);
        when(projectRegionFinder.findRegion(any())).thenReturn(new RegionFindResult(Region.DOMESTIC, false));
        when(trialUserFacade.isTrialUser()).thenReturn(true);
        final ToolProjectHomeDesign result =
                toolProjectService.create(toolProjectSaveParam, false);
        Assert.assertEquals("should return true",new ToolProjectHomeDesign(designId, null, new LinkedList<>()), result);
        Mockito.verify(projectAuthService, times(1)).saveUserAuth(toolProjectSaveParam.getUserId(),
                projectSequence.getDesignId());

        Assert.assertEquals("should return true",new ToolProjectHomeDesign(designId, null, new LinkedList<>()), result);
        Mockito.verify(albumService, Mockito.times(1)).updateAlbumDesignId(album,designId);
    }

    @Test
    public void testCopyProjectDesign() throws Exception {
        Long designId = 2L;
        ProjectSequence projectSequence = new ProjectSequence();
        projectSequence.setDesignId(designId);
        when(projectDesignFacadeDb.createProject(any())).thenReturn(projectSequence);
        ProjectDesign projectDesignDemo = new ProjectDesign();
        projectDesignDemo.setDesignId(1L);
        projectDesignDemo.setDeleted(0);
        projectDesignDemo.setUserId(2L);
        projectDesignDemo.setPlanType((byte)1);
        projectDesignDemo.setDesignSaved(0);
        projectDesignDemo.setRecommend(0);
        projectDesignDemo.setReworked(1);
        when(projectDesignFacadeDb.getProject((QueryByDesignIdParam) any())).thenReturn(projectDesignDemo);
        when(projectDesignFacadeDb.deleteProject(anyLong())).thenReturn(0);
        when(generalProperties.getCountCheck()).thenReturn(true);
        when(generalProperties.getCountLimit()).thenReturn(5L);
        when(saaSConfigService.checkProjectCountPointAccess(anyLong())).thenReturn(true);
        when(projectSearchService.countProject(anyLong())).thenReturn(0L);
        Album album = new Album();
        album.setDesignId(1L);
        when(albumService.getAlbum(any())).thenReturn(album);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setDesignId(designId);
        when(homeDesignFacadeDb.createHomeDesign(any(),any())).thenReturn(homeDesignData);
        when(homeDesignFacadeDb.copyHomeDesignIgnoreRecycles(any(),any())).thenReturn(homeDesignData);

        final Community community = new Community();
        community.setCommId(161265L);
        community.setAreaId(39L);
        community.setName(DEFAULT_COMMUNITY_NAME);
        when(communityService.getOrCreateProjectCommunityByAreaIdAndName(39L, DEFAULT_COMMUNITY_NAME)).thenReturn(community);

        ToolProjectCopyParam toolProjectCopyParam = ToolProjectCopyParam.builder()
                .srcDesignId(1L)
                .dstUserId(981501710L)
                .includeDeleted(false)
                .name("小测验方案--户型Boom-副本")
                .planType(10)
                .build();
        when(projectRegionFinder.findRegion(any())).thenReturn(new RegionFindResult(Region.DOMESTIC, false));
        ToolProjectHomeDesign result = toolProjectService.copyProjectDesign(toolProjectCopyParam,
                false);
        Assert.assertEquals("should return true",new ToolProjectHomeDesign(designId,null,new LinkedList<>()), result);
        Mockito.verify(albumService, Mockito.times(1)).updateAlbumDesignId(album,designId);
        Assert.assertEquals("should return true",new ToolProjectHomeDesign(designId,null,new LinkedList<>()), result);
    }

    @Test
    public void testRecycleDesign_designId_is_null() {
        Long userId = 2L;
        Long planId = 3L;
        Long designId = 4L;
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(designId);
        projectDesign.setPlanId(planId);
        projectDesign.setDeleted(0);
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(projectDesign);
        when(projectDesignFacadeDb.recycleDesign(userId, designId)).thenReturn(1);

        Boolean result = toolProjectService.recycleDesign(userId, planId, null);
        assertTrue(result);
        verify(projectAuthService, times(1)).recycleUserAuth(userId, designId);

    }

    @Test
    public void testRecycleDesign_planId_is_null() {
        Long userId = 2L;
        Long planId = 3L;
        Long designId = 4L;
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(designId);
        projectDesign.setPlanId(planId);
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(projectDesign);
        when(projectDesignFacadeDb.recycleDesign(userId, designId)).thenReturn(1);

        Boolean result = toolProjectService.recycleDesign(userId, null, designId);
        assertTrue("should return true",result);
        verify(projectAuthService, times(1)).recycleUserAuth(userId, designId);

    }

    @Test
    public void testRecoverProjectDesign() throws Exception {
        when(projectDesignFacadeDb.recoverProject(anyLong())).thenReturn(Integer.valueOf(1));
        when(projectDesignFacadeDb.getPlanId(anyLong())).thenReturn(35338021L);
        when(generalProperties.getCountCheck()).thenReturn(true);
        when(generalProperties.getCountLimit()).thenReturn(5L);
        when(saaSConfigService.checkProjectCountPointAccess(anyLong())).thenReturn(Boolean.TRUE);
        when(projectSearchService.countProject(anyLong())).thenReturn(0L);

        Boolean result = toolProjectService.recoverProjectDesign(1111251217L, null, 35338021L);
        Assert.assertEquals("should return true", true, result);
    }

    @Test
    public void testRecoverProjectDesign_designId_is_null() throws Exception {
        Long userId = 2L;
        Long planId = 3L;
        Long designId = 4L;
        when(projectDesignFacadeDb.getDesignId(planId)).thenReturn(designId);
        when(projectDesignFacadeDb.recoverProject(planId)).thenReturn(1);

        Boolean result = toolProjectService.recoverProjectDesign(userId, planId, null);
        assertTrue(result);
        verify(projectAuthService, times(1)).recoverUserAuth(userId, designId);
    }

    @Test
    public void testRestoreProjectDesign() throws Exception {
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(37901185L);
        projectDesign.setDeleted(0);

        when(projectDesignFacadeDb.getProject(37902185L)).thenReturn(projectDesign);
        Boolean recycleResult = toolProjectService.recycleDesign(1111251217L, 37902185L, null);
        Assert.assertEquals("should return true",false, recycleResult);

        final Boolean restoreResult = toolProjectService.restoreProjectDesign(1111251217L,
                37902185L, null, false);
        Assert.assertEquals("should return true",false, restoreResult);
    }


    @Test
    public void testDeleteDesign() {
        ProjectDesign projectDesignDemo = new ProjectDesign();
        projectDesignDemo.setDesignId(35338021L);
        projectDesignDemo.setDeleted(0);
        projectDesignDemo.setUserId(1111251217L);
        when(projectDesignFacadeDb.getProject(anyLong())).thenReturn(projectDesignDemo);
        when(projectDesignFacadeDb.deleteProject(anyLong())).thenReturn(1);
        when(projectDesignFacadeDb.getPlanId(anyLong())).thenReturn(Long.valueOf(1));
        when(generalProperties.getCountCheck()).thenReturn(Boolean.TRUE);
        when(generalProperties.getCountLimit()).thenReturn(5L);

        Boolean result = toolProjectService.deleteDesign(1111251217L, 35159076L, 35338021L);
        Assert.assertEquals("should return true",true, result);
    }

    @Test
    public void testRollbackProjectDesign() throws Exception {
        when(projectDesignFacadeDb.getPlanId(anyLong())).thenReturn(Long.valueOf(1));
        when(projectDesignFacadeDb.recoverDeleteProject(anyLong())).thenReturn(0);
        when(generalProperties.getCountCheck()).thenReturn(Boolean.TRUE);
        when(generalProperties.getCountLimit()).thenReturn(5L);
        when(saaSConfigService.checkProjectCountPointAccess(anyLong())).thenReturn(Boolean.TRUE);
        when(projectSearchService.countProject(anyLong())).thenReturn(0L);


        Boolean result = toolProjectService.rollbackProjectDesign(1111251217L, 35338021L, 35338021L);
        Assert.assertEquals("should return true",false, result);
    }

    @Test
    public void testBatchRecycleDesign_one_noAuth_one_success() throws Exception {
        Long userId = 4L;
        Long designId1 = 2L;
        Long designId2 = 3L;
        List<Long> designIds = Lists.newArrayList(designId1, designId2);
        when(projectAuthService.hasDeletedAccess(null ,null, null, designId1)).thenReturn(true);
        when(projectAuthService.hasDeletedAccess(null ,null, null, designId2)).thenReturn(false);
        when(projectDesignFacadeDb.batchRecycleDesign(userId, designIds)).thenReturn(1);

        BatchDeleteDTO batchDeleteDTO = toolProjectService.batchRecycleDesign(userId, designIds);

        Assert.assertEquals("should return true",1, batchDeleteDTO.getNoDeleteAuthDesignIds().size());
        Assert.assertEquals("should return true",designId2, batchDeleteDTO.getNoDeleteAuthDesignIds().get(0));

        verify(projectAuthService, times(1)).batchRecoverUserAuth(userId, designIds);

    }

    @Test
    public void testBatchRecoverDesign() throws Exception {
        Long userId = 4L;
        Long designId1 = 2L;
        Long designId2 = 3L;
        List<Long> designIds = Lists.newArrayList(designId1, designId2);
        when(projectDesignFacadeDb.batchRecoverProject(designIds)).thenReturn(1);

        boolean res = toolProjectService.batchRecoverProjectDesign(userId, designIds);

        assertTrue(res);

        verify(projectAuthService, times(1)).batchRecoverUserAuth(userId, designIds);

    }

    @Test
    public void testModifyProjectInfo_success() {
        Long userId = 4L;
        Long designId = 39551688L;
        Long planId = 39551686L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setName("qqqq 12");
        modifyParam.setCommName("佟馨家园D区");
        modifyParam.setObsDesignId("3FO4MMQ8OH27");
        modifyParam.setObsPlanId("3FO4MMQ8N58S");
        modifyParam.setAreaId("36");

        when(securityDetectService.sensitiveWordCheck(anyLong(), anyString())).thenReturn(true);
        when(longCrypt.decrypt(modifyParam.getObsDesignId())).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(true);


        ProjectDesign design = new ProjectDesign();
        design.setDesignId(designId);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(design);
        when(longCrypt.decrypt(modifyParam.getObsPlanId())).thenReturn(planId);

        CommunityInfo communityInfo = new CommunityInfo();
        communityInfo.setHiden(0);
        communityInfo.setAlbumId(123L);
        communityInfo.setCommId(456L);
        communityInfo.setSysParentAreaId(1);
        when(communityDb.getCommFullInfos(modifyParam.getCommName(),
                Integer.parseInt(modifyParam.getAreaId()))).thenReturn(communityInfo);
        when(sysDictAreaMapper.getPidAndFullNameById(1)).thenReturn(communityInfo);

        when(projectDesignFacadeDb.updateProjectWithModifiedTime(true, design)).thenReturn(1);

        final Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);

        verify(projectDesignFacadeDb, times(1)).updateProjectWithModifiedTime(true, design);
        assertTrue("modify info success", result.success());
    }

    @Test
    public void testModifyProjectInfo_auth_fail() {
        Long userId = 4L;
        Long designId = 39551688L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setName("qqqq 12");
        modifyParam.setCommName("佟馨家园D区");
        modifyParam.setObsDesignId("3FO4MMQ8OH27");
        modifyParam.setObsPlanId("3FO4MMQ8N58S");
        modifyParam.setAreaId("36");

        when(securityDetectService.sensitiveWordCheck(anyLong(), anyString())).thenReturn(true);
        when(longCrypt.decrypt(modifyParam.getObsDesignId())).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(false);

        final Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);

        Assert.assertFalse("auth check failed, result unsuccess", result.success());
    }

    @Test
    public void testGetDesignSearchableStatus_designNotExist() {
        Long designId = 222L;
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(null);
        DesignSearchableStatus designSearchableStatus = toolProjectService.getDesignSearchableStatus(designId);
        Assert.assertEquals("design should exist", DesignSearchableStatus.DESIGN_NOT_EXIST, designSearchableStatus);
    }

    @Test
    public void testGetDesignSearchableStatus_design_is_searchable() {
        Long designId = 222L;
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(new ProjectDesign());
        when(projectSearchService.checkDesignTagSearchable(designId)).thenReturn(true);
        DesignSearchableStatus designSearchableStatus = toolProjectService.getDesignSearchableStatus(designId);
        Assert.assertEquals("design can be searched", DesignSearchableStatus.TAG_CAN_BE_SEARCHED,
                designSearchableStatus);
    }

    @Test
    public void testGetDesignSearchableStatus_design_is_not_searchable() {
        Long designId = 222L;
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(new ProjectDesign());
        when(projectSearchService.checkDesignTagSearchable(designId)).thenReturn(false);
        DesignSearchableStatus designSearchableStatus = toolProjectService.getDesignSearchableStatus(designId);
        Assert.assertEquals("design can be searched", DesignSearchableStatus.TAG_CAN_NOT_BE_SEARCHED,
                designSearchableStatus);
    }

    @Test(expected = ToolProjectCreateException.class)
    public void createProjectDesign_with_emoji() throws Exception {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setName("👍");
        toolProjectSaveParam.setUserId(1L);
        toolProjectService.create(toolProjectSaveParam, false);
    }

    @Test(expected = ToolProjectCreateException.class)
    public void createProjectDesign_name_too_long() throws Exception {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setName(" ");
        toolProjectSaveParam.setUserId(1L);
        for (int i = 0; i < 110; i++) {
            toolProjectSaveParam.setName(toolProjectSaveParam.getName() +"a");
        }
        toolProjectService.create(toolProjectSaveParam, false);
    }

    @Test(expected = ToolProjectCreateException.class)
    public void createProjectDesign_trial_user_forbidden() throws Exception {
        ToolProjectSaveParam toolProjectSaveParam = new ToolProjectSaveParam();
        toolProjectSaveParam.setName(" ");
        toolProjectSaveParam.setUserId(1L);
        when(trialUserFacade.isTrialUser()).thenReturn(true);
        when(generalProperties.isDisableTrialUser()).thenReturn(true);
        toolProjectService.create(toolProjectSaveParam, false);
    }

    @Test
    public void deleteDesign() throws Exception {
        final Long userId = 11L;
        Long planId = 2L;
        Long designId = null;
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(3L);
        projectDesign.setDeleted(0);
        projectDesign.setUserId(userId);
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(projectDesign);
        when(projectDesignFacadeDb.deleteProject(projectDesign.getDesignId())).thenReturn(1);
        Boolean res = toolProjectService.deleteDesign(userId, planId, designId);
        assertTrue("delete design failed", res);
    }

    @Test
    public void testNeedDesignRevision() {
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setVrc(VrcEnum.VILLA.getCode());
        boolean result = toolProjectService.needDesignRevision(projectDesign);
        assertFalse("needDesignRevision toad is false, but result is true", result);
        when(toadProperties.isMultiLevelRevisionEnable()).thenReturn(true);
        Set<String> vrcSet = new HashSet<>();
        vrcSet.add(VrcEnum.VILLA.getCode());
        when(toadProperties.getMultiLevelVrcSet()).thenReturn(vrcSet);
        result = toolProjectService.needDesignRevision(projectDesign);
        assertTrue("needDesignRevision return false", result);
    }

    @Test
    public void testGetProjectDesignByDesignId() {
        // 准备测试数据
        Long designId = 123L;
        ProjectDesign expectedDesign = new ProjectDesign();
        expectedDesign.setDesignId(designId);
        
        // mock依赖方法
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(expectedDesign);
        
        // 执行测试方法
        ProjectDesign result = toolProjectService.getProjectDesignByDesignId(designId);
        
        // 验证结果
        Assert.assertEquals("通过designId获取的ProjectDesign对象应该与预期相同", expectedDesign, result);
        verify(projectDesignFacadeDb, times(1)).getProjectByDesignId(designId);
    }
    
    @Test
    public void testGetProjectDesignByPlanId() {
        // 准备测试数据
        Long planId = 456L;
        ProjectDesign expectedDesign = new ProjectDesign();
        expectedDesign.setPlanId(planId);
        
        // mock依赖方法
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(expectedDesign);
        
        // 执行测试方法
        ProjectDesign result = toolProjectService.getProjectDesignByPlanId(planId);
        
        // 验证结果
        Assert.assertEquals("通过planId获取的ProjectDesign对象应该与预期相同", expectedDesign, result);
        verify(projectDesignFacadeDb, times(1)).getProject(planId);
    }
    
    @Test
    public void testGetProjectDesignByLevelId() {
        // 准备测试数据
        String levelId = "level123";
        Long designId = 789L;
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(designId);
        ProjectDesign expectedDesign = new ProjectDesign();
        expectedDesign.setDesignId(designId);
        
        // mock依赖方法
        when(levelDesignFacadeDb.getLevelDesign(levelId)).thenReturn(levelDesignData);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(expectedDesign);
        
        // 执行测试方法
        ProjectDesign result = toolProjectService.getProjectDesignByLevelId(levelId);
        
        // 验证结果
        Assert.assertEquals("通过levelId获取的ProjectDesign对象应该与预期相同", expectedDesign, result);
        verify(levelDesignFacadeDb, times(1)).getLevelDesign(levelId);
        verify(projectDesignFacadeDb, times(1)).getProjectByDesignId(designId);
    }
    
    @Test
    public void testGetProjectDesignByLevelId_levelDesignNull() {
        // 准备测试数据
        String levelId = "nonExistentLevel";
        
        // mock依赖方法
        when(levelDesignFacadeDb.getLevelDesign(levelId)).thenReturn(null);
        
        // 执行测试方法
        ProjectDesign result = toolProjectService.getProjectDesignByLevelId(levelId);
        
        // 验证结果
        Assert.assertNull("当levelDesign不存在时，应返回null", result);
        verify(levelDesignFacadeDb, times(1)).getLevelDesign(levelId);
        verify(projectDesignFacadeDb, never()).getProjectByDesignId(anyLong());
    }
    
    @Test
    public void testFillDefaultProjectName_withNameProvided() {
        // 准备测试数据
        String providedName = "测试方案";
        ToolProjectSaveParam param = new ToolProjectSaveParam();
        param.setName(providedName);
        
        // 执行测试方法
        toolProjectService.fillDefaultProjectName(param);
        
        // 验证结果
        Assert.assertEquals("当已提供名称时，不应修改名称", providedName, param.getName());
    }
    
    @Test
    public void testFillDefaultProjectName_withoutNameProvided() {
        // 准备测试数据
        ToolProjectSaveParam param = new ToolProjectSaveParam();
        String defaultName = "默认方案名称";
        
        // mock依赖方法
        when(fileMessageSource.getMessage(anyString(), any(), any(), any())).thenReturn(defaultName);
        
        // 执行测试方法
        toolProjectService.fillDefaultProjectName(param);
        
        // 验证结果
        Assert.assertEquals("当未提供名称时，应使用默认名称", defaultName, param.getName());
    }
    
    @Test
    public void testFillDefaultProjectName_withException() {
        // 准备测试数据
        ToolProjectSaveParam param = new ToolProjectSaveParam();
        
        // mock依赖方法抛出异常
        when(fileMessageSource.getMessage(anyString(), any(), any(), any())).thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试方法
        toolProjectService.fillDefaultProjectName(param);
        
        // 验证结果
        Assert.assertEquals("当获取默认名称出现异常时，应使用硬编码的默认名称", "未命名方案", param.getName());
    }
    
    @Test
    public void testRecycleDesign_exception() {
        // 准备测试数据
        Long userId = 100L;
        Long planId = 200L;
        Long designId = 300L;
        
        // mock依赖方法抛出异常
        when(projectDesignFacadeDb.recycleDesign(userId, designId)).thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试方法
        Boolean result = toolProjectService.recycleDesign(userId, planId, designId);
        
        // 验证结果
        Assert.assertFalse("当回收设计出现异常时，应返回false", result);
    }
    
    @Test
    public void testBatchRecycleDesign_exception() {
        // 准备测试数据
        Long userId = 100L;
        List<Long> designIds = Lists.newArrayList(200L, 300L);
        
        // mock依赖方法抛出异常
        when(projectDesignFacadeDb.batchRecycleDesign(userId, designIds)).thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试方法
        BatchDeleteDTO result = toolProjectService.batchRecycleDesign(userId, designIds);
        
        // 验证结果
        Assert.assertFalse("当批量回收设计出现异常时，应返回false", result.getDeleteResult());
    }
    
    @Test
    public void testRecoverProjectDesign_exception() throws Exception {
        // 准备测试数据
        Long userId = 100L;
        Long planId = 200L;
        Long designId = 300L;
        
        // mock依赖方法
        when(generalProperties.getCountCheck()).thenReturn(true);
        when(saaSConfigService.checkProjectCountPointAccess(userId)).thenReturn(false);
        when(projectSearchService.countProject(userId)).thenReturn(0L);
        when(projectDesignFacadeDb.getPlanId(designId)).thenReturn(planId);
        when(projectDesignFacadeDb.recoverProject(planId)).thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试方法
        Boolean result = toolProjectService.recoverProjectDesign(userId, null, designId);
        
        // 验证结果
        Assert.assertFalse("当恢复设计出现异常时，应返回false", result);
    }
    
    @Test
    public void testBatchRecoverProjectDesign_exception() throws Exception {
        // 准备测试数据
        Long userId = 100L;
        List<Long> designIds = Lists.newArrayList(200L, 300L);
        
        // mock依赖方法
        when(generalProperties.getCountCheck()).thenReturn(true);
        when(saaSConfigService.checkProjectCountPointAccess(userId)).thenReturn(false);
        when(projectSearchService.countProject(userId)).thenReturn(0L);
        when(projectDesignFacadeDb.batchRecoverProject(designIds)).thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试方法
        Boolean result = toolProjectService.batchRecoverProjectDesign(userId, designIds);
        
        // 验证结果
        Assert.assertFalse("当批量恢复设计出现异常时，应返回false", result);
    }
    
    @Test
    public void testRestoreProjectDesign_nullPlanIdAndDesignId() throws Exception {
        // 执行测试方法
        Boolean result = toolProjectService.restoreProjectDesign(1L, null, null, false);
        
        // 验证结果
        Assert.assertFalse("当planId和designId都为null时，应返回false", result);
    }
    
    @Test
    public void testRestoreProjectDesign_success() throws Exception {
        // 准备测试数据
        Long userId = 100L;
        Long planId = 200L;
        Long designId = 300L;
        
        // mock依赖方法
        when(generalProperties.getCountCheck()).thenReturn(true);
        when(saaSConfigService.checkProjectCountPointAccess(userId)).thenReturn(false);
        when(projectSearchService.countProject(userId)).thenReturn(0L);
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDesignId(designId);
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(projectDesign);
        when(projectDesignFacadeDb.restoreProject(userId, designId, false)).thenReturn(1);
        
        // 执行测试方法
        Boolean result = toolProjectService.restoreProjectDesign(userId, planId, null, false);
        
        // 验证结果
        Assert.assertTrue("当成功恢复设计时，应返回true", result);
    }
    
    @Test
    public void testDeleteDesign_projectDesignNull() {
        // 准备测试数据
        Long userId = 100L;
        Long planId = 200L;
        Long designId = 300L;
        
        // mock依赖方法
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(null);
        
        // 执行测试方法
        Boolean result = toolProjectService.deleteDesign(userId, planId, null);
        
        // 验证结果
        Assert.assertTrue("当ProjectDesign为null时，应返回true", result);
    }
    
    @Test
    public void testDeleteDesign_notDeletedAndNotSameUser() {
        // 准备测试数据
        Long userId = 100L;
        Long planId = 200L;
        Long designId = 300L;
        
        // mock依赖方法
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setDeleted(0);
        projectDesign.setUserId(101L); // 不同的用户ID
        when(projectDesignFacadeDb.getProject(planId)).thenReturn(projectDesign);
        
        // 执行测试方法
        Boolean result = toolProjectService.deleteDesign(userId, planId, null);
        
        // 验证结果
        Assert.assertTrue("当ProjectDesign未删除但用户ID不匹配时，应返回true", result);
    }
    
    @Test
    public void testModifyProjectInfo_emptyObsDesignId() {
        // 准备测试数据
        Long userId = 100L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        // 不设置ObsDesignId
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当ObsDesignId为空时，应返回错误", result.success());
    }
    
    @Test
    public void testModifyProjectInfo_sensitiveWordCheck() {
        // 准备测试数据
        Long userId = 100L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId("someId");
        modifyParam.setName("敏感词测试");
        modifyParam.setCommName("测试社区");
        
        // mock依赖方法
        when(securityDetectService.sensitiveWordCheck(eq(userId), anyString())).thenReturn(false);
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当包含敏感词时，应返回错误", result.success());
    }
    
    @Test
    public void testModifyProjectInfo_projectDesignNull() {
        // 准备测试数据
        Long userId = 100L;
        Long designId = 200L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId("someId");
        
        // mock依赖方法
        when(securityDetectService.sensitiveWordCheck(eq(userId), anyString())).thenReturn(true);
        when(longCrypt.decrypt("someId")).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(true);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(null);
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当ProjectDesign为null时，应返回错误", result.success());
    }
    
    @Test
    public void testModifyProjectInfo_communityInfoNull() {
        // 准备测试数据
        Long userId = 100L;
        Long designId = 200L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId("someId");
        modifyParam.setAreaId("36");
        modifyParam.setCommName("测试社区");
        
        // mock依赖方法
        when(securityDetectService.sensitiveWordCheck(eq(userId), anyString())).thenReturn(true);
        when(longCrypt.decrypt("someId")).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(true);
        ProjectDesign projectDesign = new ProjectDesign();
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(projectDesign);
        when(communityDb.getCommFullInfos(modifyParam.getCommName(), Integer.valueOf(modifyParam.getAreaId()))).thenReturn(null);
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当CommunityInfo为null时，应返回错误", result.success());
    }
    
    @Test
    public void testModifyProjectInfo_updateFailed() {
        // 准备测试数据
        Long userId = 100L;
        Long designId = 200L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId("someId");
        modifyParam.setAreaId("36");
        modifyParam.setCommName("测试社区");
        
        // mock依赖方法
        when(securityDetectService.sensitiveWordCheck(eq(userId), anyString())).thenReturn(true);
        when(longCrypt.decrypt("someId")).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(true);
        ProjectDesign projectDesign = new ProjectDesign();
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenReturn(projectDesign);
        CommunityInfo communityInfo = new CommunityInfo();
        communityInfo.setCommId(456L);
        when(communityDb.getCommFullInfos(modifyParam.getCommName(), Integer.valueOf(modifyParam.getAreaId()))).thenReturn(communityInfo);
        when(projectDesignFacadeDb.updateProjectWithModifiedTime(true, projectDesign)).thenReturn(0);
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当更新失败时，应返回错误", result.success());
    }
    
    @Test
    public void testModifyProjectInfo_illegalArgumentException() {
        // 准备测试数据
        Long userId = 100L;
        Long designId = 200L;
        ToolProjectModifyParam modifyParam = new ToolProjectModifyParam();
        modifyParam.setObsDesignId("someId");
        
        // mock依赖方法
        when(securityDetectService.sensitiveWordCheck(eq(userId), anyString())).thenReturn(true);
        when(longCrypt.decrypt("someId")).thenReturn(designId);
        when(projectAuthService.checkAuth(userId, designId)).thenReturn(true);
        when(projectDesignFacadeDb.getProjectByDesignId(designId)).thenThrow(new IllegalArgumentException("测试异常"));
        
        // 执行测试方法
        Result<Boolean> result = toolProjectService.modifyProjectInfo(userId, modifyParam);
        
        // 验证结果
        Assert.assertFalse("当发生IllegalArgumentException时，应返回错误", result.success());
    }
}