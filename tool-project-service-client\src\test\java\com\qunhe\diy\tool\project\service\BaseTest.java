/*
 * BaseTest.java
 * Copyright 2018 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.qunhe.diy.tool.project.service.api.ToolProjectAPi;
import com.qunhe.diy.tool.project.service.client.ToolProjectClient;
import com.qunhe.diy.tool.project.service.common.constant.SoaConstant;
import com.qunhe.diy.tool.project.service.factory.ApiFactory;
import com.qunhe.hunter.http.HeaderOnlyHttpServletRequest;
import com.qunhe.rpc.proxy.AdvancedDynamicProxy;
import exacloud.AbstractRpcClientTest;

/**
 * <AUTHOR>
 */
public abstract class BaseTest extends AbstractRpcClientTest {
    protected static ToolProjectClient toolProjectCLient;

    @Override
    protected void initRpcClient() {
        toolProjectCLient =
                new ToolProjectClient(new ApiFactory(mApiRegistry));
    }

    @Override
    protected String[] subscribedVips() {
        return new String[]{ SoaConstant.SERVICE_VIP };
    }

    @Override
    protected void setServiceVersion() {
        final String[] vips = this.subscribedVips();
        final StringBuilder builder = new StringBuilder();
        final int var4 = vips.length;

        for (final String vip : vips) {
            builder.append(vip).append(":maokutest#");
        }

        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }

        System.setProperty("service.version.default", builder.toString());
    }

    public static void injectUserIdToRequest(Long userId) {
        final HeaderOnlyHttpServletRequest headerOnlyHttpServletRequest =
                new HeaderOnlyHttpServletRequest();
        headerOnlyHttpServletRequest.addHeader("x-qh-id", userId.toString());
        RequestContextHolder.setRequestAttributes(
                new ServletRequestAttributes(headerOnlyHttpServletRequest));
    }

}
