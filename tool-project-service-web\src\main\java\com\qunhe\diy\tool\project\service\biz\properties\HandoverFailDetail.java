/*
 * HandoverFailDetail.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.properties;

import lombok.Builder;
import lombok.Data;

/**
 * Function:
 *
 * <AUTHOR>
 */
@Data
@Builder
public class HandoverFailDetail {
    private String id;
    private Integer type;
    private String name;
    private String Reason;
    private String userId;
}
