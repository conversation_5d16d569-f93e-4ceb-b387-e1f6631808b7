---
description:
globs:
alwaysApply: false
---
# 技术栈说明

## 基础框架
- Spring Boot: 应用基础框架
- Maven: 依赖管理工具

## 中间件与服务
- TDDL: 分布式数据库
  - 支持多数据源配置
- SOA: 面向服务架构
  - 版本: ${soa.version}
- Pilot: 应用部署平台
  - 用于应用部署和环境管理
- Hunter: 内部服务框架
  - 版本: ${hunter.version}

## 配置管理
- Toad: 配置中心
  - 环境差异配置托管
- _appconfig/cicd.yml: 应用启动参数配置

## 其他依赖组件
- Diyrender: 渲染引擎
  - 版本: ${diyrender.version}
- Mybatis: ORM框架
  - 版本: ${mybatis-tddl-starter.version}
- Redis (Spore): 缓存服务

## 文档链接
- Pilot: http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/
- SOA: http://manual.k8s-new.qunhequnhe.com/soa/1.0.0/
- Hunter: http://coops.gitlabpages.qunhequnhe.com/hunter-doc/
- TDDL: http://manual.k8s-new.qunhequnhe.com/tddl-doc/1.0.0/
- Toad: http://coops.gitlabpages.qunhequnhe.com/toad-doc/
- Spore: http://manual.k8s-new.qunhequnhe.com/redis-doc/1.0.0/spore/usage
