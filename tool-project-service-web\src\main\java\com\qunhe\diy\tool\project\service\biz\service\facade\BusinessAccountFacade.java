/*
 * BusinessAccountFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.diy.tool.project.service.biz.exception.BusinessAccountRuntimeException;
import com.qunhe.instdeco.businessaccount.sdk.client.BusinessAccountClient;
import com.qunhe.instdeco.businessaccount.sdk.data.TobBusinessAccount;
import com.qunhe.instdeco.businessaccount.sdk.fields.BusinessAccountField;
import com.qunhe.instdeco.plan.openapi.client.OpenApiAuthClient;
import com.qunhe.instdeco.plan.openapi.data.auth.PartnerUserInfo;
import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;

import static com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb.DEFAULT_FIELDS;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class BusinessAccountFacade {
    private static final QHLogger LOG = QHLogger.getLogger(BusinessAccountFacade.class);

    /**
     * <a href="http://confluence.qunhequnhe.com/pages/viewpage.action?pageId=***********">businessaccount-sdk 接口文档</a>
     */
    private final BusinessAccountClient businessAccountClient;

    private final OpenApiAuthClient openApiAuthClient;

    /**
     * 根据 userId 获取商家账号 id
     */
    @SentinelResource(value = "BusinessAccountFacade#getAccountId",
            fallback = "getAccountIdFallBack")
    public Long getAccountId(final Long userId) {
        final TobBusinessAccount businessAccount = businessAccountClient.getByUserId(
                userId, Collections.singleton(BusinessAccountField.ACCOUNT_ID));
        if (businessAccount == null) {
            LOG.message("getAccountId - businessAccount is null")
                    .with("userId", userId)
                    .warn();
            return null;
        }
        return businessAccount.getAccountId();
    }

    public Long getAccountIdFallBack(final Long userId, final Throwable e) {
        LOG.message("getAccountIdFallBack", e)
                .with("userId", userId)
                .warn();
        return null;
    }

    /**
     * 根据 userId 获取商家主账号 id
     */
    @SentinelResource(value = "BusinessAccountFacade#getRootAccountIdByUserId",
            blockHandler = "getRootAccountIdByUserIdFallBack")
    public Long getRootAccountIdByUserId(final Long userId) {
        try {
            final TobBusinessAccount businessAccount = businessAccountClient.getRootAccountByUserId(
                    userId, Collections.singleton(BusinessAccountField.ACCOUNT_ID));
            if (businessAccount == null) {
                LOG.message("getRootAccountIdByUserId - businessAccount is null")
                        .with("userId", userId)
                        .warn();
                return null;
            }
            return businessAccount.getAccountId();
        } catch (final Exception e) {
            LOG.message("getRootAccountIdByUserId - error", e)
                    .with("userId", userId)
                    .error();
            throw new BusinessAccountRuntimeException("getRootAccountIdByUserId - error", e);
        }
    }

    public Long getRootAccountIdByUserIdFallBack(final Long userId, final BlockException e) {
        LOG.message("getRootAccountIdByUserIdFallBack", e)
                .with("userId", userId)
                .warn();
        return null;
    }

    @SentinelResource(value = "BusinessAccountFacade#getRootAccountIdByUserIdForC",
            blockHandler = "getRootAccountIdByUserIdForCFallBack")
    public Long getRootAccountIdByUserIdForC(final Long userId) {
        try {
            final PartnerUserInfo userInfo = openApiAuthClient.getApiUserInfo(userId);
            if (userInfo == null) {
                LOG.message("getRootAccountIdByUserIdForC - userInfo is null")
                        .with("userId", userId)
                        .warn();
                return null;
            }
            return userInfo.getPartnerAccountId();
        } catch (final Exception e) {
            LOG.message("getRootAccountIdByUserIdForC - error", e)
                    .with("userId", userId)
                    .error();
            throw new BusinessAccountRuntimeException("getRootAccountIdByUserIdForC - error", e);
        }
    }

    public Long getRootAccountIdByUserIdForCFallBack(final Long userId, final BlockException e) {
        LOG.message("getRootAccountIdByUserIdForCFallBack", e)
                .with("userId", userId)
                .warn();
        return null;
    }

    /**
     * 根据 accountId 获取商家账号信息
     */
    @SentinelResource(value = "BusinessAccountFacade#getBusinessAccountById",
            blockHandler = "getBusinessAccountByIdFallBack")
    public TobBusinessAccount getBusinessAccountById(final Long accountId) {
        try {
            return businessAccountClient.getByAccountId(accountId, DEFAULT_FIELDS);
        } catch (final Exception e) {
            LOG.message("getBusinessAccountById - error", e)
                    .with("accountId", accountId)
                    .error();
            throw new BusinessAccountRuntimeException("getBusinessAccountById - error", e);
        }
    }

    public TobBusinessAccount getBusinessAccountByIdFallBack(final Long accountId,
            final BlockException e) {
        LOG.message("getBusinessAccountByIdFallBack", e)
                .with("accountId", accountId)
                .warn();
        return null;
    }
}
