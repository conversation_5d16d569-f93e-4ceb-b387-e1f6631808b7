/*
 * VrcUtilsTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.utils;

import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.PlanType;
import com.qunhe.diybe.dms.data.VrcEnum;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class VrcUtilsTest {

    private MockedStatic<ToolTypeContextHolder> mockedContextHolder;

    @Before
    public void setUp() {
        mockedContextHolder = Mockito.mockStatic(ToolTypeContextHolder.class);
    }

    @After
    public void tearDown() {
        if (mockedContextHolder != null) {
            mockedContextHolder.close();
        }
    }

    @Test
    public void testGetVrcWithDefault_WithValidVrc() {
        // Setup
        ThreadLocal<String> mockVrcHolder = new ThreadLocal<>();
        mockVrcHolder.set("V0150R0001");

        mockedContextHolder.when(ToolTypeContextHolder::getVrcHolder)
                .thenReturn(mockVrcHolder);

        // Execute
        String result = VrcUtils.getVrcWithDefault();

        // Verify
        assertEquals("Should return the VRC from context", "V0150R0001", result);
    }

    @Test
    public void testGetVrcWithDefault_WithBlankVrc() {
        // Setup
        ThreadLocal<String> mockVrcHolder = new ThreadLocal<>();
        mockVrcHolder.set("");

        mockedContextHolder.when(ToolTypeContextHolder::getVrcHolder)
                .thenReturn(mockVrcHolder);

        // Execute
        String result = VrcUtils.getVrcWithDefault();

        // Verify
        assertEquals("Should return BIM VRC as default", VrcEnum.BIM.getCode(), result);
    }

    @Test
    public void testGetVrcWithDefault_WithNullVrc() {
        // Setup
        ThreadLocal<String> mockVrcHolder = new ThreadLocal<>();
        mockVrcHolder.set(null);

        mockedContextHolder.when(ToolTypeContextHolder::getVrcHolder)
                .thenReturn(mockVrcHolder);

        // Execute
        String result = VrcUtils.getVrcWithDefault();

        // Verify
        assertEquals("Should return BIM VRC as default", VrcEnum.BIM.getCode(), result);
    }

    @Test
    public void testGetVrcWithDefault_WithCustomDefault() {
        // Setup
        ThreadLocal<String> mockVrcHolder = new ThreadLocal<>();
        mockVrcHolder.set(null);

        mockedContextHolder.when(ToolTypeContextHolder::getVrcHolder)
                .thenReturn(mockVrcHolder);

        // Execute
        String result = VrcUtils.getVrcWithDefault(VrcEnum.YUNTU_FPI);

        // Verify
        assertEquals("Should return YUNTU_FPI VRC as custom default", VrcEnum.YUNTU_FPI.getCode(),
                result);
    }

    @Test
    public void testGetVrcWithDefault_WithCustomDefault_ValidVrc() {
        // Setup
        ThreadLocal<String> mockVrcHolder = new ThreadLocal<>();
        mockVrcHolder.set("V0150R0002");

        mockedContextHolder.when(ToolTypeContextHolder::getVrcHolder)
                .thenReturn(mockVrcHolder);

        // Execute
        String result = VrcUtils.getVrcWithDefault(VrcEnum.YUNTU_FPI);

        // Verify
        assertEquals("Should return the VRC from context", "V0150R0002", result);
    }

    @Test
    public void testIsYunDesign_WithYunDesignType() {
        // Setup
        DiyDesignInfo designInfo = new DiyDesignInfo();
        designInfo.setPlanType((byte) 1); // Assuming 1 maps to YUN_DESIGN

        // Mock PlanType.getProjectType to return YUN_DESIGN
        try (MockedStatic<PlanType> mockedPlanType = Mockito.mockStatic(PlanType.class)) {
            mockedPlanType.when(() -> PlanType.getProjectType((byte) 1))
                    .thenReturn(PlanType.ProjectType.YUN_DESIGN);

            // Execute
            boolean result = VrcUtils.isYunDesign(designInfo);

            // Verify
            assertTrue("Should return true for YUN_DESIGN type", result);
        }
    }

    @Test
    public void testIsYunDesign_WithNonYunDesignType() {
        // Setup
        DiyDesignInfo designInfo = new DiyDesignInfo();
        designInfo.setPlanType((byte) 0); // Assuming 0 maps to non-YUN_DESIGN

        // Mock PlanType.getProjectType to return non-YUN_DESIGN
        try (MockedStatic<PlanType> mockedPlanType = Mockito.mockStatic(PlanType.class)) {
            mockedPlanType.when(() -> PlanType.getProjectType((byte) 0))
                    .thenReturn(PlanType.ProjectType.BIM);

            // Execute
            boolean result = VrcUtils.isYunDesign(designInfo);

            // Verify
            assertFalse("Should return false for non-YUN_DESIGN type", result);
        }
    }

    @Test
    public void testIsYunDesign_WithNullPlanType() {
        // Setup
        DiyDesignInfo designInfo = new DiyDesignInfo();
        designInfo.setPlanType(null);

        // Execute
        boolean result = VrcUtils.isYunDesign(designInfo);

        // Verify
        assertFalse("Should return false for null plan type", result);
    }

    @Test(expected = Exception.class)
    public void testIsYunDesign_WithNullDesignInfo() {
        // Execute & Verify
        VrcUtils.isYunDesign(null);
        fail("Should throw NullPointerException for null design info");
    }
}
