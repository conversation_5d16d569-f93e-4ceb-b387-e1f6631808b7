/*
 * ToolLevelServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.LevelDesignFacadeDb;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelDesignCreateResponse;
import com.qunhe.diybe.dms.data.LevelDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.rcs.data.ErrorCodeEnum;
import com.qunhe.rcs.data.Module;
import com.qunhe.rcs.data.exception.RevisionControlException;
import com.qunhe.rcsapi.clients.RevisionControlClientV2;
import com.qunhe.utils.uniqueid.NotEnoughUniqueIdException;
import com.qunhe.utils.uniqueid.UniqueIdGenerationUnrecoverableException;
import com.qunhe.utils.uniqueid.UniqueIdGenerator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
@RunWith(MockitoJUnitRunner.class)
public class ToolLevelServiceTest {

    @Mock
    private LevelDesignFacadeDb levelDesignFacadeDb;
    @Mock
    private HomeDesignFacadeDb homeDesignFacadeDb;
    @Mock
    private UniqueIdGenerator uniqueIdGenerator;
    @Mock
    private RevisionControlClientV2 revisionControlClientV2;

    @InjectMocks
    private ToolLevelService toolLevelService;

    @Test
    public void testCreateLevelDesignWithEmptyLevelInfos() throws Exception {
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(Collections.singletonList("111111"));
        LevelDesignCreateResponse result = toolLevelService.createLevel(
                homeDesignData, 111L, true, true, "currentLevelId", "levelName");
        assertEquals(null, 2, result.getStatus());
        verify(levelDesignFacadeDb, never()).getLevelDesign("currentLevelId");
    }

    @Test
    public void testCreateLevelDesign_Upward_Insert_NullSource()
            throws UniqueIdGenerationUnrecoverableException, NotEnoughUniqueIdException {
        when(uniqueIdGenerator.generateCaseInsensetiveUniqueId()).thenReturn("newLevel");
        when(levelDesignFacadeDb.getLevelDesign("ML5WH4VMDSYG2AABAAAAABA8")).thenReturn(new LevelDesignData());

        HomeDesignData homeDesignData = new HomeDesignData();
        List<String> overGroundsLevelIds = new ArrayList<>();
        overGroundsLevelIds.add("ML5WH4VMDSYG2AABAAAAABA8");
        homeDesignData.setOvergroundLevels(overGroundsLevelIds);
        homeDesignData.setId(36314530L);
        homeDesignData.setDesignId(36314530L);
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("ML5WH4VMDSYG2AABAAAAABA8");
        levelInfo.setIndex(1);
        levelInfo.setName("1F");
        List<LevelInfo> levelInfos = new ArrayList<>();
        levelInfos.add(levelInfo);
        homeDesignData.setLevelInfos(levelInfos);
        //操作：插入 方向： 向上
        LevelDesignCreateResponse response =
                toolLevelService.createLevel(homeDesignData, 1111251217L, true, true,
                        "ML5WH4VMDSYG2AABAAAAABA8", "inserted");
        assertEquals(null, 0, response.getStatus());
        verify(levelDesignFacadeDb, times(1)).getLevelDesign("ML5WH4VMDSYG2AABAAAAABA8");
        //校验levelinfos中插入的新楼层信息
        LevelInfo insertLevelInfo = levelInfos.get(1);
        assertEquals(null, 2, (int) insertLevelInfo.getIndex());
        assertEquals(null, "newLevel", insertLevelInfo.getLevelId());
        assertEquals(null, "inserted", insertLevelInfo.getName());
        assertEquals(null, 2, homeDesignData.getOvergroundLevels().size());

        //操作：插入 方向：向下
        levelInfos.clear();
        levelInfos.add(levelInfo);
        homeDesignData.setLevelInfos(levelInfos);
        response =
                toolLevelService.createLevel(homeDesignData, 1111251217L, false, true,
                        "ML5WH4VMDSYG2AABAAAAABA8", "inserted");
        assertEquals(null, 0, response.getStatus());
        assertEquals(null, -1, (int) levelInfos.get(0).getIndex());
        assertEquals(null, "newLevel", levelInfos.get(0).getLevelId());
        assertEquals(null, 1, homeDesignData.getUndergroundLevels().size());

        //操作：不插入 方向：向上
        levelInfos.clear();
        levelInfos.add(levelInfo);
        homeDesignData.setLevelInfos(levelInfos);
        response =
                toolLevelService.createLevel(homeDesignData, 1111251217L, true, false,
                        "ML5WH4VMDSYG2AABAAAAABA8", "inserted");
        assertEquals(null, 0, response.getStatus());
        assertEquals(null, 2, (int) levelInfos.get(1).getIndex());
    }

    @Test
    public void testUpdateLevelInfosWithNullHomeDesign() throws Exception {
        boolean result = toolLevelService.updateLevelInfos(null, 1L, 1L, 1L);
        assertFalse(null, result);
    }

    @Test
    public void testUpdateLevelInfosInvalid() throws Exception {
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setDesignId(36751024L);
        homeDesignData.setUndergroundLevels(null);
        homeDesignData.setOvergroundLevels(Collections.singletonList("MNHIUIFMDSBY4AABAAAAAAA8"));
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("MNHIUIFMDSBY4AABAAAAAAA8");
        levelInfo.setIndex(2);
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));

        when(homeDesignFacadeDb.getOrCreateHomeDesign(anyLong(), anyLong(), anyLong())).thenReturn(homeDesignData);
        LevelInfo levelInfo2 = new LevelInfo();
        boolean result = toolLevelService.updateLevelInfos(Collections.singletonList(levelInfo2),1L,1L,1L);
        assertFalse(null, result);
        verify(homeDesignFacadeDb, never()).saveHomeDesign(homeDesignData);
    }

    @Test
    public void testUpdateLevelInfosValid() throws Exception {
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setDesignId(36751024L);
        homeDesignData.setUndergroundLevels(new ArrayList<>());
        homeDesignData.setOvergroundLevels(Arrays.asList("MNHIUIFMDSBY4AABAAAAAAA8","MN5HNY5MDSK6YAABAAAAACY8"));
        LevelInfo levelInfo = new LevelInfo();
        levelInfo.setLevelId("MNHIUIFMDSBY4AABAAAAAAA8");
        levelInfo.setIndex(2);
        homeDesignData.setLevelInfos(Collections.singletonList(levelInfo));

        when(homeDesignFacadeDb.getOrCreateHomeDesign(anyLong(), anyLong(), anyLong())).thenReturn(homeDesignData);
        LevelInfo levelInfo2 = new LevelInfo();
        levelInfo2.setLevelId("MNHIUIFMDSBY4AABAAAAAAA8");
        levelInfo2.setIndex(1);
        LevelInfo levelInfo3 = new LevelInfo();
        levelInfo3.setLevelId("MN5HNY5MDSK6YAABAAAAACY8");
        levelInfo3.setIndex(2);
        boolean result = toolLevelService.updateLevelInfos(Arrays.asList(levelInfo2, levelInfo3),1L,1L,
                1L);
        assertFalse(null, result);
    }

    @Test
    public void testDeleteLevelDesignWithNullLevelData() {
        boolean result = toolLevelService.deleteLevel("levelId", 111L);
        Assert.assertNull(null, levelDesignFacadeDb.getLevelDesign("levelId"));
        assertFalse(null, result);
        verify(levelDesignFacadeDb, times(2)).getLevelDesign("levelId");
    }

    @Test
    public void testDeleteLevelDesignWithNullOverLevels() {
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(111L);
        levelDesignData.setLevelId("111111");
        levelDesignData.setPlanId(111L);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(null);
        when(levelDesignFacadeDb.getLevelDesign(anyString())).thenReturn(
                levelDesignData);
        when(homeDesignFacadeDb.getHomeDesign(anyLong())).thenReturn(homeDesignData);

        boolean result = toolLevelService.deleteLevel("111111", 1L);
        assertFalse(null, result);
        verify(homeDesignFacadeDb, never()).saveHomeDesign(homeDesignData);
    }

    @Test
    public void testDeleteLevelDesignWithHomeDesignFalse() throws Exception {
        LevelDesignData levelDesignData = new LevelDesignData();
        levelDesignData.setDesignId(111L);
        levelDesignData.setLevelId("111111");
        levelDesignData.setPlanId(111L);
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(Collections.singletonList("111111"));
        when(levelDesignFacadeDb.getLevelDesign(anyString())).thenReturn(
                levelDesignData);
        when(homeDesignFacadeDb.getHomeDesign(anyLong())).thenReturn(homeDesignData);

        boolean result = toolLevelService.deleteLevel("111111", 1L);
        assertFalse(null, result);
    }

    @Test
    public void recoverLevel() {
        Long designId = 111L;
        String levelId = "MN5HNY5MDSK6YAABAAAAACY8";
        Long userId = 111L;
        Integer levelIndex = 0;
        String levelName = "楼层二";

        boolean result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        LevelDesignData levelDesign = new LevelDesignData();
        levelDesign.setDesignId(1L);
        when(levelDesignFacadeDb.getLevelDesign(levelId)).thenReturn(levelDesign);
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        levelDesign.setDesignId(111L);
        when(levelDesignFacadeDb.getLevelDesign(levelId)).thenReturn(levelDesign);
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(Collections.singletonList("MN5HNY5MDSK6YAABAAAAACY7"));
        homeDesignData.setUndergroundLevels(Collections.singletonList("MN5HNY5MDSK6YAABAAAAACY6"));
        when(homeDesignFacadeDb.getHomeDesign(designId)).thenReturn(homeDesignData);
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        levelIndex = 1;
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        levelIndex = -3;
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

        levelIndex = 3;
        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse(null, result);

    }

    @Test
    public void testRecoverLevel() {
        // Setup
        Long designId = 1L;
        String levelId = "MN5HNY5MDSK6YAABAAAAACY8";
        Long userId = 111L;
        Integer levelIndex = 2;
        String levelName = "楼层二";

        // Configure LevelDesignFacadeDb.getLevelDesign(...).
        LevelDesignData levelDesign = new LevelDesignData();
        levelDesign.setDesignId(1L);
        when(levelDesignFacadeDb.getLevelDesign(levelId)).thenReturn(levelDesign);

        // Configure HomeDesignFacadeDb.getHomeDesign(...).
        HomeDesignData homeDesignData = new HomeDesignData();
        List<String> overgroundLevelIds = new ArrayList<>();
        overgroundLevelIds.add("MN5HNY5MDSK6YAABAAAAACY5");
        overgroundLevelIds.add("MN5HNY5MDSK6YAABAAAAACY6");
        List<String> undergroundLevelIds = new ArrayList<>();
        undergroundLevelIds.add("MN5HNY5MDSK6YAABAAAAACY7");
        homeDesignData.setOvergroundLevels(overgroundLevelIds);
        homeDesignData.setUndergroundLevels(undergroundLevelIds);
        when(homeDesignFacadeDb.getHomeDesign(designId)).thenReturn(homeDesignData);

        // Configure HomeDesignFacadeDb.saveHomeDesign(...).
        when(homeDesignFacadeDb.saveHomeDesign(any(HomeDesignData.class))).thenReturn(true);

        // Run the test
        boolean result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertTrue("level recover fail", result);

        overgroundLevelIds.add("MN5HNY5MDSK6YAABAAAAACY8");
        when(homeDesignFacadeDb.getHomeDesign(designId)).thenReturn(homeDesignData);

        result = toolLevelService.recoverLevel(designId, levelId, userId, levelIndex, levelName);
        assertFalse("recover level is exist", result);
    }

    @Test
    public void saveMultiLevelRevision() throws RevisionControlException {
        Long designId = 111L;
        Long userId = 111L;

        toolLevelService.saveMultiLevelRevision(designId, userId);
        byte[] data = {};
        String actionId = System.currentTimeMillis() + "-" + designId;
        verify(revisionControlClientV2, never()).saveRevision(data, designId.toString(), Module.HOME_DESIGN_DATA,
                actionId, false, null, userId);

        HomeDesignData homeDesignData = new HomeDesignData();
        when(homeDesignFacadeDb.getHomeDesign(designId)).thenReturn(homeDesignData);
        toolLevelService.saveMultiLevelRevision(designId, userId);
    }

    @Test
    public void testCreateLevel_NumLimitExceed() throws UniqueIdGenerationUnrecoverableException, NotEnoughUniqueIdException {
        // Setup
        when(uniqueIdGenerator.generateCaseInsensetiveUniqueId()).thenReturn("newLevelId");
        
        HomeDesignData homeDesignData = new HomeDesignData();
        List<String> overgroundLevels = new ArrayList<>();
        List<String> undergroundLevels = new ArrayList<>();
        List<LevelInfo> levelInfos = new ArrayList<>();
        
        // Add initial level
        String initialLevelId = "initialLevel";
        overgroundLevels.add(initialLevelId);
        LevelInfo initialLevel = new LevelInfo();
        initialLevel.setLevelId(initialLevelId);
        initialLevel.setIndex(1);
        levelInfos.add(initialLevel);
        
        // Add more than LEVEL_NUM_LIMIT levels
        for (int i = 0; i < 101; i++) {
            overgroundLevels.add("level" + i);
            LevelInfo levelInfo = new LevelInfo();
            levelInfo.setLevelId("level" + i);
            levelInfo.setIndex(i + 2);
            levelInfos.add(levelInfo);
        }
        
        homeDesignData.setOvergroundLevels(overgroundLevels);
        homeDesignData.setUndergroundLevels(undergroundLevels);
        homeDesignData.setLevelInfos(levelInfos);
        
        LevelDesignData sourceLevelDesign = new LevelDesignData();

        // Test
        LevelDesignCreateResponse response = toolLevelService.createLevel(
            homeDesignData, 1L, true, false, null, "New Level");
            
        // Verify
        assertEquals("should equal", LevelDesignCreateResponse.CreateStatus.LEVEL_NO_EXIST.getStatus(),
                response.getStatus());
        verify(levelDesignFacadeDb, never()).saveLevelDesign(any());
    }


    @Test
    public void testUpdateLevelInfos_DefaultLevelChanged() {
        // Setup
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(Collections.singletonList("defaultLevel"));
        
        LevelInfo defaultLevel = new LevelInfo();
        defaultLevel.setLevelId("defaultLevel");
        defaultLevel.setIndex(1);
        homeDesignData.setLevelInfos(Collections.singletonList(defaultLevel));
        
        when(homeDesignFacadeDb.getOrCreateHomeDesign(anyLong(), anyLong(), anyLong()))
            .thenReturn(homeDesignData);
            
        // Try to update default level's index
        LevelInfo updatedLevel = new LevelInfo();
        updatedLevel.setLevelId("defaultLevel");
        updatedLevel.setIndex(2); // Changed from 1 to 2
        
        // Test
        boolean result = toolLevelService.updateLevelInfos(
            Collections.singletonList(updatedLevel), 1L, 1L, 1L);
            
        // Verify
        assertFalse("should be false", result);
        verify(homeDesignFacadeDb, never()).saveHomeDesign(any());
    }

    @Test
    public void testDeleteLevel_DefaultLevel() {
        // Setup
        LevelDesignData levelDesign = new LevelDesignData();
        levelDesign.setDesignId(1L);
        levelDesign.setLevelId("defaultLevel");
        
        HomeDesignData homeDesignData = new HomeDesignData();
        homeDesignData.setOvergroundLevels(Collections.singletonList("defaultLevel"));
        
        when(levelDesignFacadeDb.getLevelDesign("defaultLevel")).thenReturn(levelDesign);
        when(homeDesignFacadeDb.getHomeDesign(1L)).thenReturn(homeDesignData);
        
        // Test
        boolean result = toolLevelService.deleteLevel("defaultLevel", 1L);
        
        // Verify
        assertFalse("should be false", result);
        verify(homeDesignFacadeDb, never()).saveHomeDesign(any());
    }

    @Test
    public void testSaveMultiLevelRevision_Exception() throws RevisionControlException {
        // Setup
        HomeDesignData homeDesignData = new HomeDesignData();
        when(homeDesignFacadeDb.getHomeDesign(1L)).thenReturn(homeDesignData);
        when(revisionControlClientV2.saveModuleRevision(
            anyString(), any(), any(), anyString(), anyBoolean(), anyLong()))
            .thenThrow(new RevisionControlException(ErrorCodeEnum.MONGO_ERROR));
        
        // Test
        toolLevelService.saveMultiLevelRevision(1L, 1L);
        
        // Verify the exception was handled and logged
        verify(revisionControlClientV2).saveModuleRevision(
            eq("1"), any(), eq(Module.HOME_DESIGN_DATA), anyString(), eq(false), eq(1L));
    }

    /**
     * 反射测试 insertLevelAboveLevel 的所有分支
     */
    @Test
    public void testInsertLevelAboveLevel_AllBranches() throws Exception {
        ToolLevelService service = new ToolLevelService(levelDesignFacadeDb, homeDesignFacadeDb, uniqueIdGenerator, revisionControlClientV2);
        java.lang.reflect.Method method = ToolLevelService.class.getDeclaredMethod("insertLevelAboveLevel", List.class, com.qunhe.diybe.dms.data.LevelInfo.class, boolean.class, String.class, String.class);
        method.setAccessible(true);

        // 分支1 upward=true, index>0
        List<LevelInfo> infos1 = new ArrayList<>();
        LevelInfo info1 = new LevelInfo(2, "id1", "name1");
        infos1.add(info1);
        method.invoke(service, infos1, info1, true, "newName1", "newId1");
        assertEquals("should equal",2, infos1.size());

        // 分支2 upward=true, index<=0
        List<LevelInfo> infos2 = new ArrayList<>();
        LevelInfo info2 = new LevelInfo(0, "id2", "name2");
        infos2.add(info2);
        method.invoke(service, infos2, info2, true, "newName2", "newId2");
        assertEquals("should equal",2, infos2.size());

        // 分支3 upward=false, index>1
        List<LevelInfo> infos3 = new ArrayList<>();
        LevelInfo info3 = new LevelInfo(2, "id3", "name3");
        infos3.add(info3);
        method.invoke(service, infos3, info3, false, "newName3", "newId3");
        assertEquals("should equal",2, infos3.size());

        // 分支4 upward=false, index<=1
        List<LevelInfo> infos4 = new ArrayList<>();
        LevelInfo info4 = new LevelInfo(1, "id4", "name4");
        infos4.add(info4);
        method.invoke(service, infos4, info4, false, "newName4", "newId4");
        assertEquals("should equal", 2, infos4.size());
    }

    /**
     * 反射测试 recoverLevelWithIndex 的所有分支
     */
    @Test
    public void testRecoverLevelWithIndex_AllBranches() throws Exception {
        ToolLevelService service = new ToolLevelService(levelDesignFacadeDb, homeDesignFacadeDb, uniqueIdGenerator, revisionControlClientV2);
        java.lang.reflect.Method method = ToolLevelService.class.getDeclaredMethod("recoverLevelWithIndex", com.qunhe.diybe.dms.data.HomeDesignData.class, String.class, Integer.class, String.class);
        method.setAccessible(true);

        // 分支1 levelInfo!=null, index>1
        HomeDesignData data1 = new HomeDesignData();
        LevelInfo info1 = new LevelInfo(2, "id1", "name1");
        data1.setLevelInfos(new ArrayList<>(Collections.singletonList(info1)));
        method.invoke(service, data1, "newId1", 2, "newName1");
        assertEquals("", 2, data1.getLevelInfos().size());

        // 分支2 levelInfo!=null, index<=1
        HomeDesignData data2 = new HomeDesignData();
        LevelInfo info2 = new LevelInfo(1, "id2", "name2");
        data2.setLevelInfos(new ArrayList<>(Collections.singletonList(info2)));
        method.invoke(service, data2, "newId2", 1, "newName2");
        assertEquals("",2, data2.getLevelInfos().size());

        // 分支3 levelInfo==null, levelIndex>0
        HomeDesignData data3 = new HomeDesignData();
        data3.setLevelInfos(new ArrayList<>());
        method.invoke(service, data3, "newId3", 3, "newName3");
        assertEquals("",1, data3.getLevelInfos().size());

        // 分支4 levelInfo==null, levelIndex<=0
        HomeDesignData data4 = new HomeDesignData();
        data4.setLevelInfos(new ArrayList<>());
        method.invoke(service, data4, "newId4", -1, "newName4");
        assertEquals("", 1, data4.getLevelInfos().size());
    }

}
