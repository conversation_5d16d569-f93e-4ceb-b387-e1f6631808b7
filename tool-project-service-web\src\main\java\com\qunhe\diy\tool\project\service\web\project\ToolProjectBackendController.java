/*
 * ToolProjectBackendController.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.qunhe.diy.tool.project.service.biz.annotation.BatchOperationCheck;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.BatchOperationService;
import com.qunhe.diy.tool.project.service.biz.service.ProjectHandoverService;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationTypeEnum;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.common.util.CommonUtil;
import com.qunhe.diy.tool.project.service.web.utils.RequestUtils;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.instdeco.plan.annotations.LoginApiInterceptor;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.security.PreAuthorize;
import com.qunhe.rpc.proxy.annotation.Timeout;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException"})
public class ToolProjectBackendController {

    private final static QHLogger LOG = QHLogger.getLogger(
            ToolProjectBackendController.class);

    private static final int RECORD_ID_MD5_LENGTH = 16;

    private final ToolProjectService toolProjectService;

    private final ProjectHandoverService projectHandoverService;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final BatchOperationService batchOperationService;

    private final BatchOperationRecordDb batchOperationRecordDb;

    private final ToadProperties toadProperties;

    private final UserDb userDb;

    @PostMapping(value = ApiConstant.PROJECT_CREATE_API)
    @PreAuthorize("isAuthenticated()")
    public Result<ToolProjectHomeDesign> createProject(@RequestBody
    ToolProjectSaveParam toolProjectSaveParam, HttpServletRequest request)
            throws Exception {
        try {
            ToolProjectHomeDesign toolProjectHomeDesign = toolProjectService.create(toolProjectSaveParam,
                    RequestUtils.isCoohom(request));
            return Result.ok(toolProjectHomeDesign);
        } catch (ToolProjectCreateException e) {
            return Result.error(e.getError().getC(), e.getError().getM());
        }
    }

    @PostMapping(value = ApiConstant.PROJECT_COPY_API)
    @PreAuthorize("isAuthenticated()")
    public Result<ToolProjectHomeDesign> copyProject(@RequestBody
            ToolProjectCopyParam toolProjectCopyParam, final HttpServletRequest request)
            throws Exception {
        ToolProjectHomeDesign toolProjectHomeDesign = toolProjectService.copyProjectDesign(toolProjectCopyParam,
                RequestUtils.isCoohom(request));
        return Result.ok(toolProjectHomeDesign);
    }

    @PostMapping(value = ApiConstant.PROJECT_DELETE_API)
    @PreAuthorize("@projectAuthService.hasDeletedAccess(#obsPlanId, #obsDesignId, #planId," +
            "#designId)")
    @Timeout(2000)
    public Result<Boolean> recycleProject(
            @RequestParam(value = "obsPlanId", required = false) final String obsPlanId,
            @RequestParam(value =
                    "obsDesignId", required = false) final String obsDesignId,
            @RequestParam(value = "planId", required = false) Long planId, @RequestParam(value =
            "designId", required = false) Long designId) {
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = LongCipher.DEFAULT.decrypt(obsDesignId);
        }
        Long userId = UserDb.getUserIdBySession();
        Boolean result = toolProjectService.recycleDesign(userId,
                planId, designId);
        if (Boolean.TRUE.equals(result)) {
            return Result.ok(true);
        }

        return Result.ok(false).setM("删除失败，找不到该方案，请确认方案状态");
    }

    @LoginApiInterceptor
    @PostMapping(value = ApiConstant.PROJECT_BATCH_DELETE_API)
    public Result<BatchDeleteDTO> batchRecycleProject(
            @RequestParam(value = "designIds") String designIds) {
        List<Long> designIdList = JsonMapper.getLongs(designIds);
        if (designIdList.size() > toadProperties.getBatchOperationParamSizeLimit()) {
            return Result.error(ErrorCode.BATCH_OPERATION_PARAM_OVER_LIMIT);
        }
        BatchDeleteDTO batchDeleteDTO = toolProjectService.batchRecycleDesign(
                userDb.getUserBySession().getUserId(), designIdList);
        return Result.ok(batchDeleteDTO);
    }

    /**
     * 恢复回收站方案，不更新modifiedTime
     */
    @PostMapping(value = ApiConstant.PROJECT_RECOVER_API)
    @PreAuthorize("@projectAuthService.hasDeletedAccess(#obsPlanId, #obsDesignId, " +
            "#planId, #designId)")
    @Timeout(2000)
    public Result<Boolean> recoverProject(
            @RequestParam(value = "obsPlanId", required = false) final String obsPlanId,
            @RequestParam(value =
                    "obsDesignId", required = false) final String obsDesignId,
            @RequestParam(value = "planId", required = false) Long planId,
            @RequestParam(value = "designId", required = false) Long designId) {
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = LongCipher.DEFAULT.decrypt(obsDesignId);
        }
        Long userId = UserDb.getUserIdBySession();
        Boolean result;
        try {
            result = toolProjectService.recoverProjectDesign(userId,
                    planId, designId);
        } catch (ToolProjectCreateException e) {
            return Result.error(e.getError().getC(), e.getError().getM());
        }
        if (Boolean.TRUE.equals(result)) {
            return Result.ok(true);
        }
        return Result.ok(false).setM("恢复失败，找不到该方案，请确认方案状态");
    }

    @LoginApiInterceptor
    @PostMapping(value = ApiConstant.PROJECT_BATCH_RECOVER_API)
    public Result<Boolean> batchRecoverProject(
            @RequestParam(value = "designIds") String designIds) {
        try {
            List<Long> designIdList = JsonMapper.getLongs(designIds);
            if (designIdList.size() > toadProperties.getBatchOperationParamSizeLimit()) {
                return Result.error(ErrorCode.BATCH_OPERATION_PARAM_OVER_LIMIT);
            }
            Boolean result =
                    toolProjectService.batchRecoverProjectDesign(
                            userDb.getUserBySession().getUserId(),
                            designIdList);
            if (Boolean.TRUE.equals(result)) {
                return Result.ok(true);
            }
            return Result.ok(false).setM("恢复失败，找不到该方案，请确认方案状态");
        } catch (ToolProjectCreateException e) {
            return Result.error(e.getError().getC(), e.getError().getM());
        }
    }


    /**
     * 恢复回收站方案，会更新modifiedTime
     * @param planId planId或designId至少传入一个
     * @param needCheckUserId 是否需要检验方案的userid字段是否为1
     */
    @PostMapping(value = ApiConstant.PROJECT_RESTORE_API)
    @PreAuthorize("@projectAuthService.hasDeletedAccess(#obsPlanId, #obsDesignId, " +
            "#planId, #designId)")
    @Timeout(2000)
    public Result<Boolean> restoreProjectDesign(
            @RequestParam(value = "obsPlanId", required = false) final String obsPlanId,
            @RequestParam(value =
                    "obsDesignId", required = false) final String obsDesignId,
            @RequestParam(value = "planId", required = false) Long planId,
            @RequestParam(value = "designId", required = false) Long designId,
            @RequestParam(value = "needCheckUserId", defaultValue = "false")
                    boolean needCheckUserId) {
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = LongCipher.DEFAULT.decrypt(obsDesignId);
        }
        Long userId = UserDb.getUserIdBySession();
        Boolean result;
        try {
            result = toolProjectService.restoreProjectDesign(userId, planId,
                    designId, needCheckUserId);
        } catch (ToolProjectCreateException e) {
            return Result.error(e.getError().getC(), e.getError().getM());
        }
        if (Boolean.TRUE.equals(result)) {
            return Result.ok(true);
        }
        return Result.ok(false).setM("恢复失败，找不到该方案，请确认方案状态");
    }

    @PostMapping(value = ApiConstant.PROJECT_REMOVE_API)
    @PreAuthorize("@projectAuthService.hasDeletedAccess(#obsPlanId, #obsDesignId, #planId," +
            "#designId)")
    @Timeout(2000)
    public Result<Boolean> removeProject(
            @RequestParam(value = "obsPlanId", required = false) final String obsPlanId,
            @RequestParam(value =
                    "obsDesignId", required = false) final String obsDesignId,
            @RequestParam(value = "planId", required = false) Long planId, @RequestParam(value =
            "designId", required = false) Long designId) {
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = LongCipher.DEFAULT.decrypt(obsDesignId);
        }
        Long userId = UserDb.getUserIdBySession();
        Boolean result = toolProjectService.deleteDesign(userId,
                planId, designId);
        if (Boolean.TRUE.equals(result)) {
            return Result.ok(true);
        }
        return Result.ok(false).setM("彻底删除失败，找不到该方案，请确认方案状态");
    }

    @BatchOperationCheck
    @LoginApiInterceptor
    @PostMapping(value = ApiConstant.PROJECT_BATCH_REMOVE_API)
    public Result<String> batchRemoveProject(
            @RequestBody final BatchOperatorVO batchOperatorVO) {
        //获取本次请求预处理的方案数（含回收站方案）
        final Long tarUserId = batchOperatorVO.getUserId();
        final int originCount = projectDesignFacadeDb.countDesignsByUserIdIgnoreRecycle(tarUserId);
        if (originCount == 0) {
            LOG.message("no undeleted design to delete")
                    .error();
            return Result.error(ErrorCode.BATCH_OPERATION_ORIGIN_COUNT_EMPTY);
        }
        final Long userId = UserDb.getUserIdBySession();
        //timeStamp + userId 生成本次请求id
        final String recordId =
                userId + "-" + DigestUtils.md5Hex(CommonUtil.getCurrentTime()).substring(
                        RECORD_ID_MD5_LENGTH);
        final BatchOperationData batchOperationData = BatchOperationData.builder()
                .userId(tarUserId)
                .recordId(recordId)
                .operator(batchOperatorVO.toString())
                .originCount(originCount)
                .description(batchOperatorVO.getDescription())
                .build();
        //新增批量操作记录
        batchOperationRecordDb.insertRecord(batchOperationData);
        batchOperationService.batchRemoveProjects(recordId, tarUserId, batchOperationData);
        return Result.ok(recordId);
    }

    /**
     * @param userId 被操作人id
     * @param recordId 请求id
     */
    @GetMapping(value = ApiConstant.PROJECT_BATCH_OPERATE_STATUS_API)
    public Result<BatchOperationStatusEnum> getBatchOperateStatus(
            @RequestParam(value = "userId") final Long userId,
            @RequestParam(value = "recordId") final String recordId) {
        if (userId == null || StringUtils.isBlank(recordId)) {
            return Result.error(ErrorCode.BATCH_OPERATION_PARAM_INVALID);
        }
        final BatchOperationStatusEnum statusEnum;
        try {
            statusEnum = batchOperationService.getBatchOperateStatus(recordId);
            return Result.ok(statusEnum);
        } catch (Exception e) {
            //系统异常处理
            LOG.message("getBatchOperateStatus error")
                    .with("recordId", recordId)
                    .error();
            return Result.error(ErrorCode.GET_BATCH_OPERATION_STATUS_ERROR);
        }
    }

    @BatchOperationCheck
    @LoginApiInterceptor
    @PostMapping(value = ApiConstant.PROJECT_BATCH_ROLLBACK_API)
    public Result<String> batchRollbackRemoveProject(
            @RequestBody final BatchOperatorVO batchOperatorVO) {
        //统计对目标用户进行删除操作的方案，将多次删除请求进行整合
        final Long tarUserId = batchOperatorVO.getUserId();
        final List<Long> distinctDesignIdsFromCos =
                batchOperationService.getBatchDealDesignIdsFromLinks(tarUserId);
        if (CollectionUtils.isEmpty(distinctDesignIdsFromCos)) {
            LOG.message("batchRollBackProject - no valid designIds to roll back")
                    .with("targetUserId", tarUserId)
                    .error();
            return Result.error(ErrorCode.BATCH_OPERATION_ORIGIN_COUNT_EMPTY);
        }

        final Long userId = UserDb.getUserIdBySession();
        //timeStamp + userId 生成本次请求id
        final String recordId =
                userId + "-" + DigestUtils.md5Hex(CommonUtil.getCurrentTime()).substring(
                        RECORD_ID_MD5_LENGTH);
        final BatchOperationData batchOperationData = BatchOperationData.builder()
                .userId(tarUserId)
                .recordId(recordId)
                .operator(batchOperatorVO.toString())
                .originCount(distinctDesignIdsFromCos.size())
                .rollback(BatchOperationTypeEnum.BATCH_ROLLBACK.getCode())
                .description(batchOperatorVO.getDescription())
                .build();
        //新增批量操作记录
        batchOperationRecordDb.insertRecord(batchOperationData);
        batchOperationService.batchRollBackProjects(tarUserId, recordId, batchOperationData,
                distinctDesignIdsFromCos);
        return Result.ok(recordId);
    }

    @PostMapping(value = ApiConstant.PROJECT_ROLLBACK_API)
    @PreAuthorize("@projectAuthService.hasDeletedAccess(#obsPlanId, #obsDesignId, #planId, " +
            "#designId)")
    @Timeout(2000)
    public Result<Boolean> rollbackRemoveProject(
            @RequestParam(value = "obsPlanId", required = false) final String obsPlanId,
            @RequestParam(value =
                    "obsDesignId", required = false) final String obsDesignId,
            @RequestParam(value = "planId", required = false) Long planId, @RequestParam(value =
            "designId", required = false) Long designId) {
        //TODO 想想看还有什么好的写法能够优化这段代码，总觉得写的太繁琐了。
        if (StringUtils.isNotBlank(obsPlanId)) {
            planId = LongCipher.DEFAULT.decrypt(obsPlanId);
        }
        if (StringUtils.isNotBlank(obsDesignId)) {
            designId = LongCipher.DEFAULT.decrypt(obsDesignId);
        }
        Long userId = UserDb.getUserIdBySession();
        Boolean result;
        try {
            result = toolProjectService.rollbackProjectDesign(userId,
                    planId, designId);
        } catch (ToolProjectCreateException e) {
            return Result.error(e.getError().getC(), e.getError().getM());
        }
        if (Boolean.TRUE.equals(result)) {
            return Result.ok(true);
        }
        return Result.ok(false).setM("回滚方案失败，找不到该方案，请确认方案状态");
    }

    @PostMapping(value = ApiConstant.PROJECT_HANDOVER_API)
    public Result<Boolean> handoverProject(
            @RequestParam(value = "taskId") final Long taskId,
            @RequestBody final List<Long> fromUserIds,
            @RequestParam(value = "toUserId") final Long toUserId) {
        projectHandoverService.handoverProject(toUserId, fromUserIds, taskId);
        return Result.ok(true);

    }

    /**
     * 返回方案名称是否修改成功
     * @see <a href="https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80599470733"></a>
     */
    @PutMapping(value = ApiConstant.PROJECT_INFO_API)
    @PreAuthorize("hasWrite(#modifyParam.obsDesignId)")
    @LoginApiInterceptor
    public Result<Boolean> modifyProjectInfo(
            final HttpServletRequest request, final HttpServletResponse response,
            @RequestBody final ToolProjectModifyParam modifyParam) {
        if (Objects.isNull(modifyParam)) {
            return Result.error(ErrorCode.PARAM_ERROR);
        }
        return toolProjectService.modifyProjectInfo(UserDb.getUserIdBySession(), modifyParam);
    }

    @LoginApiInterceptor
    @GetMapping(ApiConstant.PROJECT_NEED_DESIGN_REVISION)
    public Boolean needDesignRevision(
            final HttpServletRequest request, final HttpServletResponse response,
            @RequestParam Long designId) {
        final ProjectDesign projectDesign = toolProjectService.getProjectDesignByDesignId(designId);
        return toolProjectService.needDesignRevision(projectDesign);
    }

}