/*
 * HandoverDesignMessage.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.properties;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Function: 方案广播消息
 *
 * <AUTHOR>
 */
@Data
@Builder
public class HandoverDesignMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    private String taskId;
    private Long toUserId;
    private Long fromUserId;
    private String designId;

}
