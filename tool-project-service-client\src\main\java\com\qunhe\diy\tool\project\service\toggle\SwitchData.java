/*
 * SwitchData.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.toggle;

import java.util.HashSet;
import java.util.Set;

/**
 * 工具页面开关的配置数据
 *
 * <AUTHOR>
 * @date 2025/5/20
 */
public class SwitchData {

    /**
     * 是否包含SKA账户
     */
    private boolean includeSka;

    /**
     * 用户百分比，0-100
     */
    private int percentage;

    /**
     * 企业用户百分比，0-100
     */
    private int kaPercentage;

    /**
     * 白名单用户列表
     */
    private Set<Long> whiteUserList = new HashSet<>();

    /**
     * 黑名单用户列表
     */
    private Set<Long> blackUserList = new HashSet<>();

    /**
     * 白名单企业列表
     */
    private Set<Long> whiteAccountList = new HashSet<>();

    /**
     * 黑名单企业列表
     */
    private Set<Long> blackAccountList = new HashSet<>();

    /**
     * SKA企业列表
     */
    private Set<Long> skaAccountList = new HashSet<>();

    public boolean isIncludeSka() {
        return includeSka;
    }

    public void setIncludeSka(boolean includeSka) {
        this.includeSka = includeSka;
    }

    public int getPercentage() {
        return percentage;
    }

    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    public int getKaPercentage() {
        return kaPercentage;
    }

    public void setKaPercentage(int kaPercentage) {
        this.kaPercentage = kaPercentage;
    }

    public Set<Long> getWhiteUserList() {
        return whiteUserList;
    }

    public void setWhiteUserList(Set<Long> whiteUserList) {
        this.whiteUserList = whiteUserList;
    }

    public Set<Long> getBlackUserList() {
        return blackUserList;
    }

    public void setBlackUserList(Set<Long> blackUserList) {
        this.blackUserList = blackUserList;
    }

    public Set<Long> getWhiteAccountList() {
        return whiteAccountList;
    }

    public void setWhiteAccountList(Set<Long> whiteAccountList) {
        this.whiteAccountList = whiteAccountList;
    }

    public Set<Long> getBlackAccountList() {
        return blackAccountList;
    }

    public void setBlackAccountList(Set<Long> blackAccountList) {
        this.blackAccountList = blackAccountList;
    }

    public Set<Long> getSkaAccountList() {
        return skaAccountList;
    }

    public void setSkaAccountList(Set<Long> skaAccountList) {
        this.skaAccountList = skaAccountList;
    }

    @Override
    public String toString() {
        return "SwitchData{" +
                "includeSka=" + includeSka +
                ", percentage=" + percentage +
                ", kaPercentage=" + kaPercentage +
                ", whiteUserList=" + whiteUserList +
                ", blackUserList=" + blackUserList +
                ", whiteAccountList=" + whiteAccountList +
                ", blackAccountList=" + blackAccountList +
                ", skaAccountList=" + skaAccountList +
                '}';
    }
}
