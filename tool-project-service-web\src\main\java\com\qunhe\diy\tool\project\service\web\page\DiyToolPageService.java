/*
 * ToolPageService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.diy.YunDesignDb;
import com.qunhe.diy.tool.project.service.web.page.handler.CoohomDiyPageHandler;
import com.qunhe.diy.tool.project.service.web.page.handler.KujialeDiyPageHandler;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.stream.Collectors;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@RequiredArgsConstructor
public class DiyToolPageService {

    private static final QHLogger LOG = QHLogger.getLogger(DiyToolPageService.class);

    public static final String H5_PAGE_PATH = "/tool/h5/diy?designid=";

    public static final String APP_ID_HEADER = "x-qh-appid";

    public static final String DEFAULT_APP_ID = "3FO4K4VYBAIK";

    public static final String COOHOM_DIY_APP_ID = "3FO4K4VY9XP6";

    private final YunDesignDb yunDesignDb;
    private final UserInfoFacade userInfoFacade;
    private final ToadProperties toadProperties;
    private final KujialeDiyPageHandler kujialeDiyPageHandler;
    private final CoohomDiyPageHandler coohomDiyPageHandler;

    public ToolPageResult handlePage(HttpServletRequest request,
            HttpServletResponse response, DiyPageParam param)
            throws AccessAuthenticatorException, YunDesignException, IOException {

        Long userId = param.getUserId();

        LOG.message("handle diy page")
                .with("userId", userId)
                .with("param", param)
                .with("queryString", request.getQueryString())
                .info();

        if (param.getObsDesignId() == null && param.getObsPlanId() != null) {
            final Long createdDesignId = yunDesignDb.getOrCreateDesign(param.getObsPlanId(),
                    userId);
            return redirect(createdDesignId, request);
        }

        final String actualAppId = getAppId(request, param.getObsAppId(), userId);
        final Long appIdNum = LongCipher.DEFAULT.decrypt(actualAppId);

        if (param.isFromCoohom()) {
            return coohomDiyPageHandler.handlePage(request, response, param, actualAppId, appIdNum);
        } else {
            return kujialeDiyPageHandler.handlePage(request, response, param, actualAppId,
                    appIdNum);
        }
    }


    public String getAppId(final HttpServletRequest request, final String inputAppId,
            final Long userId) {
        if (StringUtils.isNotBlank(inputAppId)) {
            return inputAppId;
        }
        final String appIdFromHeader = request.getHeader(APP_ID_HEADER);
        if (StringUtils.isNotBlank(appIdFromHeader)) {
            return appIdFromHeader;
        }
        final boolean isCoohomUser = userInfoFacade.isCoohomUser(userId);
        return isCoohomUser ? COOHOM_DIY_APP_ID : DEFAULT_APP_ID;
    }

    @SneakyThrows(URISyntaxException.class)
    private ToolPageResult redirect(final Long designId, final HttpServletRequest request) {
        String host = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        if (StringUtils.isEmpty(host)) {
            host = toadProperties.getYunHost();
        } else {
            host = "//" + host;
        }

        String url = host + H5_PAGE_PATH + LongCipher.DEFAULT.encrypt(designId) +
                "&" +
                removePlanIdFromQuery(request.getQueryString());

        new URI(url);
        return ToolPageResult.redirect(url);
    }

    private String removePlanIdFromQuery(final String query) {
        if (query == null) {
            return "";
        }
        return Arrays.stream(query.split("&"))
                .filter(str -> !str.startsWith("planid"))
                .collect(Collectors.joining("&"));
    }


}
