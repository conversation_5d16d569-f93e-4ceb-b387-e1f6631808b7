/*
 * LoginCheckAdvice.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.advice;

import com.qunhe.diy.tool.project.service.biz.exception.NotLoggedInException;
import com.qunhe.instdeco.plan.annotations.LoginApiInterceptor;
import com.qunhe.interceptors.utils.SessionUserUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Aspect
@Order(4)
public class LoginCheckAdvice {
    @Pointcut("@annotation(com.qunhe.instdeco.plan.annotations.LoginApiInterceptor)")
    public void pointCut() {
        // nothing
    }

    @Before("pointCut()")
    public void before(final JoinPoint joinPoint) throws Throwable {
        final Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        LoginApiInterceptor loginInterceptor = AnnotationUtils.
                findAnnotation(method, LoginApiInterceptor.class);
        if (loginInterceptor == null) {
            return;
        }

        final Long userId = SessionUserUtil.getUserId().orElse(null);
        if (Objects.isNull(userId)) {
            throw new NotLoggedInException("Not Login");
        }

    }

}