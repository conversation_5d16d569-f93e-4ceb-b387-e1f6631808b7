/*
 * OssConfigTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.diy.tool.project.service.common.enums.BatchOperationTypeEnum;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 */
public class OssConfigTest {

    @Test
    public void testGenerateCosKey() {
        final BatchOperationTypeEnum type = BatchOperationTypeEnum.BATCH_DELETE;
        final String recordId = "record1";
        final String cosKey = OssConfig.generateCosKey(type.getDesc(), recordId).replaceAll("/\\d" +
                "{14}", "");
        Assert.assertEquals("coskey concat with '/' ", "/tps/batch_operate/batch_delete/record1",
                cosKey);
    }

    @Test
    public void testExtractKeyFromCdnUrl() {
        final String prefix = "https://qhtbdoss.kujiale.com";
        final String cosUrl = "https://qhtbdoss.kujiale" +
                ".com/tps/batch_operate/batch_delete/record1.json";
        final String cosKey = OssConfig.extractKeyFromCdnUrl(prefix, cosUrl);
        Assert.assertEquals("extract from cosUrl", "/tps/batch_operate/batch_delete/record1.json",
                cosKey);
    }
}
