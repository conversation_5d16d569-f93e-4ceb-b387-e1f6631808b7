/*
 * RequestUtils.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 *
 */

public class RequestUtils {
    public static final String COOHOM = "coohom";
    public static final String ORIGINAL_HOST_HEADER_NAME = "original-host";

    /**
     * 判断请求是否来自coohom
     */
    public static boolean isCoohom(HttpServletRequest request) {
        final String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
        return StringUtils.isNotEmpty(originalHost) && originalHost.contains(COOHOM);
    }

    public static boolean isCoohom() {
        RequestAttributes attr = RequestContextHolder.getRequestAttributes();
        if (attr instanceof ServletRequestAttributes) {
            return isCoohom(((ServletRequestAttributes) attr).getRequest());
        }

        return false;
    }
}
