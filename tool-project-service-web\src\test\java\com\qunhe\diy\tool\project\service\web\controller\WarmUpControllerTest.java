/*
 * WarmUpControllerTest.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.controller;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

import com.qunhe.diy.tool.project.service.web.project.ToolProjectService;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectModifyParam;
import com.qunhe.diy.tool.project.service.web.config.WarmUpConfig;

@RunWith(MockitoJUnitRunner.class)
@WebMvcTest(WarmUpController.class)
public class WarmUpControllerTest {


    @Mock
    private ToolProjectService mockToolProjectService;
    @Mock
    private WarmUpConfig mockWarmUpConfig;

    @InjectMocks
    private WarmUpController warmUpController;


    @Test
    public void testWarmUp() throws Exception {
        warmUpController = new WarmUpController(mockToolProjectService, mockWarmUpConfig);
        // Setup
        when(mockWarmUpConfig.getIterationCount()).thenReturn(1);
        when(mockWarmUpConfig.getUserId()).thenReturn(0L);

        // Configure ToolProjectService.createProjectDesign(...).
        when(mockWarmUpConfig.getTimeoutMills()).thenReturn(0);

        // Run the test
        warmUpController.warmUp();

        // Verify the results
        verify(mockToolProjectService).modifyProjectInfo(eq(0L), any(ToolProjectModifyParam.class));
    }


}
