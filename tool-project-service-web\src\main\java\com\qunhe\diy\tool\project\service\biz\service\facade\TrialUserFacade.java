/*
 * TrialUserFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;


import com.qunhe.diybe.dms.project.data.TrialDesignAttribute;
import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.project.data.TrialDesignStatus;
import com.qunhe.user.growth.uic.passport.utils.UicUserSessionUtil;

/**
 * 游客逻辑封装
 * <AUTHOR>
 * @date 2023/8/7
 */
@Component
@RequiredArgsConstructor
public class TrialUserFacade {

    private static final QHLogger LOG = QHLogger.getLogger(TrialUserFacade.class);

    private final ProjectClient projectClient;

    public boolean isTrialUser() {
        return UicUserSessionUtil.isTrialUser();
    }

    @SentinelResource(value = "markTrialDesignCreated", fallback = "markTrialDesignCreatedFallback")
    public void markTrialDesignCreated(Long designId) {
        projectClient.upsertTrialDesignStatus(designId, TrialDesignStatus.CREATED);
    }

    public void markTrialDesignCreatedFallback(Long designId, Throwable e) {
        LOG.message("markTrialDesignCreated fall back", e)
                .with("designId", designId)
                .error();
    }

    @SentinelResource(value = "isTrialDesign", fallback = "isTrialDesignFallback")
    public boolean isTrialDesign(Long designId) {
        TrialDesignAttribute trialDesignAttribute = projectClient.getTrialDesignAttributeByDesignId(designId);
        return trialDesignAttribute != null && trialDesignAttribute.getStatus() == TrialDesignStatus.CREATED.getValue();
    }

    public boolean isTrialDesignFallback(Long designId, Throwable e) {
        LOG.message("isTrialDesign fall back to false", e)
                .with("designId", designId)
                .error();
        return false;
    }

    @SentinelResource(value = "markTrialDesignUpdated", fallback = "markTrialDesignUpdatedFallback")
    public void markTrialDesignUpdated(Long designId) {
        projectClient.upsertTrialDesignStatus(designId, TrialDesignStatus.UPDATED);
    }

    public void markTrialDesignUpdatedFallback(Long designId, Throwable e) {
        LOG.message("markTrialDesignUpdated fall back", e)
                .with("designId", designId)
                .error();
    }


}
