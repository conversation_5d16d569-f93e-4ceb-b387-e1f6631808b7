/*
 * GrayLunchService.java
 * Copyright 2018 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.diy;

import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.instdeco.plan.graylaunchmw.db.GrayLaunchDb;
import com.qunhe.interceptors.utils.SessionUserUtil;
import com.qunhe.utils.HunterLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 *
 */
@Service("tpsGrayLunchService")
@RequiredArgsConstructor
public class GrayLunchService {

    private static final HunterLogger LOG = HunterLogger.getLogger(GrayLunchService.class);

    private final GrayLaunchDb mGrayLaunchDb;

    public boolean checkVersion(final String rule, final String expect,
            final HttpRequestMetaData httpRequestMetaData) {
        final Map<String, String> headerMap = SessionUserUtil.getUserHeaders();
        if (headerMap.isEmpty()) {
            LOG.message("checkVersion - could not get user info from session")
                    .warn();
            return false;
        }
        final String version = mGrayLaunchDb.getGrayVersion(rule, httpRequestMetaData);
        return Objects.equals(expect, version);
    }

    public String getGrayVersion(final String decorationServiceName,
            final HttpRequestMetaData httpRequestMetaData) {
        return mGrayLaunchDb.getGrayVersion(decorationServiceName, httpRequestMetaData);
    }
}
