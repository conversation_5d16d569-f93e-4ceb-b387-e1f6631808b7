/*
 * ToolType.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.context;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum ToolType {

    H5_DIY("diy"),
    YUN_DESIGN("yundesign"),
    YUN_DESIGN_CUSTOM_VERIFY("yundesign-custom-verify"),
    CUSTOM_ORDER("custom-order"),
    FOP_ORDER("fop-order"),
    OTHER("other");

    private final String type;

    private static final Map<String, ToolType> TYPE_MAP;

    ToolType(final String type) {
        this.type = type;
    }

    static {
        final ImmutableMap.Builder<String, ToolType> builder = ImmutableMap
                .builder();
        for (final ToolType value : values()) {
            builder.put(value.type, value);
        }
        TYPE_MAP = builder.build();
    }

    public static ToolType typeOf(final String type) {
        if (TYPE_MAP.containsKey(type)) {
            return TYPE_MAP.get(type);
        }
        return OTHER;
    }

}
