/*
 * VrcRewriteService.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.exception.VrcMappingNotFoundException;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.utils.HunterLogger;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Service
@RequiredArgsConstructor
public class VrcRewriteService {

    private static final HunterLogger LOG = HunterLogger.getLogger(VrcRewriteService.class);

    private static final String DEFAULT_APP_ID = "3FO4K4VYBAIK";

    private static final String APP_STAGE = "APP_STAGE";

    private final VrcService vrcService;

    private final VrcRewriteProperties vrcRewriteProperties;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;


    /**
     * 更正vrcCode
     * vrc = V0150R0000(fpi导入)，根据其首次进入的工具版本对vrc修正（国内业务）
     * vrc = V0150R0701(CoohomAI)，如果uri参数指定appid是coohom5.0正式版，则修正成coohom5.0的vrc（不可逆）
     *
     * @param vrc
     * @param planId
     * @param param
     */
    public String correctVrc(String vrc, Long planId, BimPageParam param) {
        if (StringUtils.isBlank(vrc)) {
            return vrc;
        }
        String obsAppId = param.getObsAppId();
        if (StringUtils.isBlank(obsAppId)) {
            LOG.message("can't get valid appid, fallback to default bim appid:1").warn();
            obsAppId = DEFAULT_APP_ID;
        }

        VrcRewriteProperties.RewriteVrcInfo rewriteInfo = vrcRewriteProperties.getRewriteInfo(vrc);
        if (rewriteInfo == null) {
            return vrc;
        }

        if (rewriteInfo.isNeedAppIdMatch() && !obsAppId.equals(rewriteInfo.getAppId())) {
            LOG.message("appid not match, dont rewrite vrc")
                    .with("obsAppId", obsAppId)
                    .with("rewriteInfo", rewriteInfo)
                    .warn();
            return vrc;
        }

        String dstVrcCode = vrcService.getCorrectVrc(obsAppId, vrc);

        //非法的vrc或appid或stage导致查表失败，抛出400
        if (StringUtils.isEmpty(dstVrcCode)) {
            LOG.message("invalid vrc or appid or stage")
                    .with("vrc", vrc)
                    .with("obsAppId", obsAppId)
                    .with("stage", System.getenv(APP_STAGE))
                    .error();
            throw new VrcMappingNotFoundException(planId, vrc, obsAppId);
        }
        projectDesignFacadeDb.updateVrc(planId, dstVrcCode);
        LOG.message("update vrc success.")
                .with("dstVrcCode", dstVrcCode)
                .info();
        return dstVrcCode;
    }


}
