/*
 * ProjectBusinessEnum.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * 方案创建上游业务枚举
 */
@Getter
@RequiredArgsConstructor
public enum ProjectBusinessEnum {
    FRONT_NEW("前端创建方案"),
    BACKEND_NEW("后端创建方案"),
    BACKEND_FPI("后端fpi创建方案"),
    BACKEND_CAD("后端cad创建方案"),
    BACKEND_BITMAP("后端临摹图创建方案"),
    BACKEND_COPY("后端复制"),
    BACKEND_UPDATE("后端更新"),
    ;

    private final String desc;

}
