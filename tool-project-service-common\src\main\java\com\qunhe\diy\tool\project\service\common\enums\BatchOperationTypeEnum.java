/*
 * BatchOperationTypeEnum.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.enums;

/**
 * <AUTHOR>
 */
public enum BatchOperationTypeEnum {
    BATCH_DELETE(0, "batch_delete"),
    BATCH_ROLLBACK(1, "batch_rollback");

    private final String desc;
    private final int code;

    public String getDesc() {
        return desc;
    }

    public int getCode() {
        return code;
    }

    BatchOperationTypeEnum(final int code, final String desc) {
        this.code = code;
        this.desc = desc;
    }
}
