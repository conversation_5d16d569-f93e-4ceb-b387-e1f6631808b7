
# tool-project-service旨在管理工具方案
---
## 方案创建 - projectdesign
### 接口设计
req: ProjectSaveReq 只保留前端需要传的参数
commId:楼盘 
area:面积
realarea:户型的真实面积
userid：用户id 
plantype：户型类型
name:方案名称 多语言的方案名成从业务方传过来传过来tps不应该做默认的方案名称
vrc:工具版本 从header里拿


方案创建不必传 但工具方案实际在用的字段
modeldataid: 关联FLOOR_PKLAN_HOME_DATA，LAYOUT_HOME_DATA
designdataid: 管理DESIGN_HOME_DATA
recommend:标识是否推荐到画户型首页
reworked: 是否为被修改的户型
uploadpic：cad和bitmap给
albumid:相册Id
sourceid：户型来源 null表示正常新建
vrc:从header里拿到

--- 非project_design表数据 这个不应该在tps做 应该业务方自己保存
designStage:updateDesignStage户型装修流程阶段
addOrUpdateBackgroundSetting
insertFloorPlanSuggestion
sampleroom

方案创建完以后 发送消息
notifySiteForDarenCreate
notifySiteForDesignCreated
uicUserInfoClient.incrUserDataCount(UserDataCount.PLAN.value(), userId);

---
generateI18nStyleCover 封面图生成不由tps做
pics：户型预览图 不由tps做
floorplanimages 八张图不由tps做

----
## client
1 create project
2 copy project

