/*
 * ApiConstans.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.constant;

/**
 * <AUTHOR>
 */
public class ApiConstant {

    public final static String API_PREFIX = "/tps/api";
    public final static String API_ENCRYPT_PREFIX = "/tps/api/c";
    public final static String PROJECT_CREATE_API = API_PREFIX + "/project/create";
    public final static String PROJECT_COPY_API = API_PREFIX + "/project/copy";
    public final static String PROJECT_DELETE_API = API_PREFIX + "/project/delete";
    public final static String PROJECT_RECOVER_API = API_PREFIX + "/project/recover";
    public final static String PROJECT_RESTORE_API = API_PREFIX + "/project/restore";
    public final static String PROJECT_REMOVE_API = API_PREFIX + "/project/remove";
    public final static String PROJECT_ROLLBACK_API = API_PREFIX + "/project/rollback";
    public final static String PROJECT_HANDOVER_API = API_PREFIX + "/project/handover";
    public final static String PROJECT_INFO_API = API_PREFIX + "/project/info";

    public final static String COMMUNITY_GET_OR_CREATE_API = API_PREFIX + "/community";
    public final static String COMMUNITY_DEFAULT_LOCATION = API_PREFIX + "/community/location";
    public final static String COMMUNITY_NAME_BY_COMM_ID = API_PREFIX + "/community/commname-by-id";
    public final static String SYSDICTAREA_BY_COMM_ID = API_PREFIX + "/community/sysdictarea-by-id";


    public final static String PROJECT_CREATE_ENCRYPT_API = API_ENCRYPT_PREFIX + "/project/create";
    public final static String PROJECT_BATCH_DELETE_API = API_PREFIX + "/project/batchDelete";
    public final static String PROJECT_BATCH_RECOVER_API = API_PREFIX + "/project/batchRecover";
    public final static String PROJECT_BATCH_REMOVE_API = API_PREFIX + "/project/batchRemove";
    public final static String PROJECT_BATCH_ROLLBACK_API = API_PREFIX + "/project/batchRollback";
    public final static String PROJECT_BATCH_OPERATE_STATUS_API = API_PREFIX + "/project" +
            "/batchOperate/status";

    public final static String PROJECT_SEARCHABLE_STATUS = API_ENCRYPT_PREFIX + "/project/searchable-status";

    public final static String PROJECT_LEVEL_BATCH_UPDATE = API_PREFIX + "/project/level/batchUpdate";
    public static final String PROJECT_LEVEL_CREATE = API_PREFIX + "/project/level/create";
    public static final String PROJECT_LEVEL_DELETE = API_PREFIX + "/project/level/delete";
    public static final String PROJECT_LEVEL_RECOVER = API_PREFIX + "/project/level/recover";
    public static final String PROJECT_LEVEL_UPDATE = API_PREFIX + "/project/level/update";
    public static final String PROJECT_NEED_DESIGN_REVISION = API_PREFIX + "/project/needDesignRevision";

    public static final String BIM_TOOL_PAGE_KUJIALE = "/tps/api/page/tool/bim/kujiale";

    public static final String BIM_TOOL_RPC_PAGE_KUJIALE = "/tps/api/rpc/page/tool/bim/kujiale";

    public static final String BIM_TOOL_PAGE_COOHOM = "/tps/api/page/tool/bim/coohom";

    public static final String BIM_TOOL_RPC_PAGE_COOHOM = "/tps/api/rpc/page/tool/bim/coohom";

    public static final String DIY_TOOL_PAGE_KUJIALE = "/tps/api/page/tool/diy/kujiale";

    public static final String DIY_TOOL_RPC_PAGE_KUJIALE = "/tps/api/rpc/page/tool/diy/kujiale";

    public static final String DIY_TOOL_PAGE_COOHOM = "/tps/api/page/tool/diy/coohom";

    public static final String DIY_TOOL_RPC_PAGE_COOHOM = "/tps/api/rpc/page/tool/diy/coohom";

}
