<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ DesignMetaDataMapper.xml
  ~ Copyright 2019 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.1//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.DesignMetaDataMapper">

    <select id="getLastOpenedLevelId" resultType="String">
        SELECT reverse03 as lastOpenedLevelId
        FROM `floorplan_metadata`
        WHERE planid = #{planId}
    </select>

    <update id="updateLastOpenedLevelId">
        UPDATE `floorplan_metadata`
        <set>
            <trim suffixOverrides=",">
                <if test="lastOpenedLevelId != null">reverse03 = #{lastOpenedLevelId},</if>
            </trim>
        </set>
        WHERE planid = #{planId}
    </update>

    <insert id="insertLastOpenedLevelId" >
        INSERT INTO floorplan_metadata (
        <trim suffixOverrides=",">
            <if test="planId != null">planid,</if>
            <if test="lastOpenedLevelId != null">reverse03,</if>
        </trim>
        )
        Values (
        <trim suffixOverrides=",">
            <if test="planId != null">#{ planId },</if>
            <if test="lastOpenedLevelId != null">#{lastOpenedLevelId},</if>
        </trim>
        )
    </insert>

</mapper>