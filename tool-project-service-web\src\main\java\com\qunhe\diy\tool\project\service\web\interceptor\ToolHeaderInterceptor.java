/*
 * ToolHeaderInterceptor.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.interceptor;

import com.qunhe.diy.tool.project.service.web.context.ToolType;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.utils.HunterLogger;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public class ToolHeaderInterceptor implements HandlerInterceptor {
    public static final String TOOL_NAME_HEADER = "x-tool-name";
    public static final String VRC = "x-qh-vrc";
    private static final HunterLogger LOG = HunterLogger.getLogger(ToolHeaderInterceptor.class);
    private static final String TOOL_TYPE_HEADER = "x-tool-type";

    private static final String FOP_ORDER_PARAM = "FOPOrderId";

    @Override
    public boolean preHandle(final HttpServletRequest httpServletRequest,
            final HttpServletResponse httpServletResponse, final Object o) {
        try {
            final String value = httpServletRequest.getHeader(TOOL_NAME_HEADER);
            if (!StringUtils.isEmpty(value)) {
                ToolTypeContextHolder.setToolType(ToolType.typeOf(value));
            }
            final String vrc = httpServletRequest.getHeader(VRC);
            if (!StringUtils.isEmpty(vrc)) {
                ToolTypeContextHolder.getVrcHolder().set(vrc);
            }
            final String toolType = httpServletRequest.getHeader(TOOL_TYPE_HEADER);
            if (!StringUtils.isEmpty(toolType)) {
                ToolTypeContextHolder.setNewToolType(ToolType.typeOf(toolType));
            }
            if (httpServletRequest.getParameter(FOP_ORDER_PARAM) != null) {
                ToolTypeContextHolder.setToolType(ToolType.FOP_ORDER);
            }
        } catch (final Exception e) {
            LOG.message("prehandle-tool-header-error").with(e).warn();
        }
        return true;
    }

    @Override
    public void postHandle(final HttpServletRequest httpServletRequest,
            final HttpServletResponse httpServletResponse, final Object o,
            final ModelAndView modelAndView) {
        // nothing
    }

    @Override
    public void afterCompletion(final HttpServletRequest httpServletRequest,
            final HttpServletResponse httpServletResponse, final Object o, final Exception e) {
        ToolTypeContextHolder.clear();
    }
}
