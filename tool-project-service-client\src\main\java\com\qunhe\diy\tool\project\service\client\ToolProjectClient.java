//4823dd24df4d2e32807980cd19da2e3a
/*
 * ProjectClient.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.client;

import com.qunhe.diy.tool.project.service.api.ToolProjectAPi;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.common.util.ParamCheckUtil;
import com.qunhe.diy.tool.project.service.factory.ApiFactory;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelDesignCreateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.rpc.common.builder.ParamPairsBuilder;
import com.qunhe.rpc.common.utils.Pair;
import com.qunhe.web.standard.data.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@DependsOn({ "tpsApiFactory" })
public class ToolProjectClient {

    public final ToolProjectAPi toolProjectAPi;

    @Autowired
    public ToolProjectClient(final ApiFactory apiFactory) {
        this.toolProjectAPi = apiFactory.getApiInstance(ToolProjectAPi.class);
    }

    public Result<ToolProjectHomeDesign> create(final ToolProjectSaveParam toolProjectSaveParam) {
        ParamCheckUtil.checkSaveParam(toolProjectSaveParam, true);
        return toolProjectAPi.create(toolProjectSaveParam.getUserId(), toolProjectSaveParam);
    }

    public Result<ToolProjectHomeDesign> copy(final ToolProjectCopyParam toolProjectCopyParam) {
        ParamCheckUtil.checkCopyParam(toolProjectCopyParam);
        return toolProjectAPi.copy(toolProjectCopyParam.getDstUserId(), toolProjectCopyParam);
    }

    public Result<Boolean> recycleProjectDesign(final Long userId, final Long designId,
            final Long planId) {
        ParamCheckUtil.checkRecycle(designId, planId);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("planId", planId)
                .append("designId", designId)
                .build();
        return toolProjectAPi.recycleProjectDesign(userId, params);
    }

    public Result<BatchDeleteDTO> batchRecycleProjectDesign(Long userId, List<Long> designIds) {
        ParamCheckUtil.checkBatchOperate(designIds);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("designIds", designIds)
                .build();
        return toolProjectAPi.batchRecycleProjectDesign(userId, params);
    }

    public Result<Boolean> recoverProjectDesign(final Long userId, final Long designId,
            final Long planId) {
        ParamCheckUtil.checkRecycle(designId, planId);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("planId", planId)
                .append("designId", designId)
                .build();
        return toolProjectAPi.recoverProjectDesign(userId, params);
    }

    public Result<Boolean> batchRecoverProjectDesign(Long userId, List<Long> designIds) {
        ParamCheckUtil.checkBatchOperate(designIds);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("designIds", designIds)
                .build();
        return toolProjectAPi.batchRecoverProjectDesign(userId, params);
    }

    public Result<String> batchRemoveProject(BatchOperatorVO batchOperatorVO) {
        ParamCheckUtil.checkBatchOperateDelete(batchOperatorVO);
        return toolProjectAPi.batchRemoveProject(batchOperatorVO);
    }

    public Result<String> batchRollbackRemoveProject(BatchOperatorVO batchOperatorVO) {
        ParamCheckUtil.checkBatchOperateDelete(batchOperatorVO);
        return toolProjectAPi.batchRollbackRemoveProject(batchOperatorVO);
    }

    public Result<BatchOperationStatusEnum> getBatchOperateStatus(final Long userId,
            final String recordId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("userId", userId)
                .append("recordId", recordId)
                .build();
        return toolProjectAPi.getBatchOperateStatus(params);
    }


    public Result<Boolean> restoreProjectDesign(final Long planId,
            final Long designId, final Long userId, final boolean needCheckUserId) {
        ParamCheckUtil.checkRecycle(designId, planId);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("planId", planId)
                .append("designId", designId)
                .append("needCheckUserId", needCheckUserId)
                .build();
        return toolProjectAPi.recoverProjectDesign(userId, params);
    }

    public Result<Boolean> deleteProjectDesign(final Long userId, final Long designId,
            final Long planId) {
        ParamCheckUtil.checkRecycle(designId, planId);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("planId", planId)
                .append("designId", designId)
                .build();
        return toolProjectAPi.deleteProjectDesign(userId, params);
    }

    public Result<Boolean> recoverDeleteProjectDesign(final Long userId, final Long designId,
            final Long planId) {
        ParamCheckUtil.checkRecycle(designId, planId);
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("planId", planId)
                .append("designId", designId)
                .build();
        return toolProjectAPi.recoverDeleteProjectDesign(userId, params);
    }

    public Result<Boolean> handoverProject(final Long toUserId, final List<Long> fromUserIds,
            final Long taskId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("toUserId", toUserId)
                .append("taskId", taskId)
                .build();
        return toolProjectAPi.handoverProject(fromUserIds, params);
    }

    public LevelBatchUpdateResponse batchCreateOrUpdateLevels(final Long designId, final List<LevelInfo> levelInfos) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("designId", designId)
                .build();
        return toolProjectAPi.batchCreateOrUpdateLevels(params, levelInfos);
    }

    public LevelDesignCreateResponse createLevel(final Long planId, final Long designId,
                                                 final Long userId, final boolean upward,
                                                 final boolean insert, final String currentLevelId,
                                                 final String levelName)  {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(7)
                .append("planid", planId)
                .append("designid", designId)
                .append("userid", userId)
                .append("upward", upward)
                .append("insert", insert)
                .append("levelid", currentLevelId)
                .append("levelname", levelName)
                .build();
        return toolProjectAPi.createLevel(params);
    }

    public boolean updateLevel(final Long planId, final Long designId, final Long userId,
                               final List<LevelInfo> levelInfos)  {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(3)
                .append("planid", planId)
                .append("designid", designId)
                .append("userid", userId)
                .build();
        return toolProjectAPi.updateLevel(params, levelInfos);
    }

    public boolean deleteLevel(final String levelId, final Long userId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(2)
                .append("levelid", levelId)
                .append("userid", userId)
                .build();
        return toolProjectAPi.deleteLevel(params);
    }

    public boolean recoverDeletedLevel(final Long designId, final String levelId,
                                       final Long userId, final Integer levelIndex,
                                       final String levelName) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(5)
                .append("designid", designId)
                .append("levelid", levelId)
                .append("userid", userId)
                .append("levelindex", levelIndex)
                .append("levelname", levelName)
                .build();

        return toolProjectAPi.recoverDeletedLevel(params);
    }

    public boolean needDesignRevision(final Long designId) {
        final List<Pair<String, Object>> params = new ParamPairsBuilder(1)
                .append("designId", designId)
                .build();
        return toolProjectAPi.needDesignRevision(params);
    }

}
