/*
 * HasTestUtil.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
public class HasTestUtil {

    public static void setHasExceptionCountDegradeResource(final String resource) {
        DegradeRule degradeRule = new DegradeRule();
        degradeRule.setResource(resource);
        degradeRule.setCount(0);
        degradeRule.setMinRequestAmount(1);
        degradeRule.setTimeWindow(10);
        degradeRule.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_COUNT);
        DegradeRuleManager.loadRules(Lists.newArrayList(degradeRule));
    }

    public static void setHasRtDegradeResource(final String resource) {
        DegradeRule degradeRule = new DegradeRule();
        degradeRule.setResource(resource);
        degradeRule.setCount(0);
        degradeRule.setSlowRatioThreshold(1);
        degradeRule.setMinRequestAmount(1);
        degradeRule.setTimeWindow(10);
        degradeRule.setGrade(RuleConstant.DEGRADE_GRADE_RT);
        DegradeRuleManager.loadRules(Lists.newArrayList(degradeRule));
    }

}
