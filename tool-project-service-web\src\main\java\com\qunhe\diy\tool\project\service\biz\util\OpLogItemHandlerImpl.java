/*
 * OpLogItemHandlerImpl.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.util;

import com.qunhe.assembly.oplog.LogItemDto;
import com.qunhe.assembly.oplog.manager.OpLogItemHandler;
import lombok.SneakyThrows;
import org.slf4j.MDC;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Primary
@Service
public class OpLogItemHandlerImpl implements OpLogItemHandler {
    private static final String AUDIT_LOG = "audit_log";


    @Override
    public LogItemDto doBeforeHandler(final Object[] objects) {
        LogItemDto logItemDto = new LogItemDto();
        final String auditLog = MDC.get(AUDIT_LOG);
        logItemDto.setData(auditLog);
        return logItemDto;
    }

    @Override
    public LogItemDto doAfterHandler(final Object o) {
        return null;
    }
}
