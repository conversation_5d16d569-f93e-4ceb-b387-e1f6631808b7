/*
 * DowngradingServiceNewTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.project.service.client.client.DowngradingClient;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectLevelDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * Additional unit tests for DowngradingService
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class DowngradingServiceNewTest {

    @Mock
    private DowngradingClient downgradingClient;

    @InjectMocks
    private DowngradingService downgradingService;

    @Before
    public void setUp() {
        // Setup common test data
    }

    @Test
    public void testRefreshSearchData_Success() {
        // Setup
        List<Object> testData = Arrays.asList("data1", "data2", "data3");

        // Execute
        downgradingService.refreshSearchData(testData);

        // Verify
        verify(downgradingClient, times(1)).refresh(testData);
    }

    @Test
    public void testRefreshSearchData_EmptyList() {
        // Setup
        List<Object> emptyData = Collections.emptyList();

        // Execute
        downgradingService.refreshSearchData(emptyData);

        // Verify
        verify(downgradingClient, times(1)).refresh(emptyData);
    }

    @Test
    public void testRefreshSearchData_NullList() {
        // Setup
        List<Object> nullData = null;

        // Execute
        downgradingService.refreshSearchData(nullData);

        // Verify
        verify(downgradingClient, times(1)).refresh(nullData);
    }

    @Test
    public void testRefreshSearchData_Exception() {
        // Setup
        List<Object> testData = Arrays.asList("data1", "data2");
        doThrow(new RuntimeException("Test exception")).when(downgradingClient).refresh(testData);

        // Execute - should not throw exception
        downgradingService.refreshSearchData(testData);

        // Verify
        verify(downgradingClient, times(1)).refresh(testData);
    }

    @Test
    public void testAddHotData_Success() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("测试方案")
                .build();

        ToolProjectLevelDesign levelDesign = ToolProjectLevelDesign.builder()
                .levelId("level-123")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Collections.singletonList(levelDesign))
                .build();

        Long userId = 11111L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_WithMultipleLevels() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("多层方案")
                .build();

        ToolProjectLevelDesign level1 = ToolProjectLevelDesign.builder()
                .levelId("level-1")
                .build();

        ToolProjectLevelDesign level2 = ToolProjectLevelDesign.builder()
                .levelId("level-2")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Arrays.asList(level1, level2))
                .build();

        Long userId = 11111L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify - should use first level's ID
        ArgumentCaptor<List> listCaptor = ArgumentCaptor.forClass(List.class);
        verify(downgradingClient, times(1)).refresh(listCaptor.capture());

        List capturedList = listCaptor.getValue();
        assertNotNull("Captured list should not be null", capturedList);
        assertEquals("Should have one element", 1, capturedList.size());
    }

    @Test
    public void testAddHotData_EmptyLevels() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("无层级方案")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Collections.emptyList())
                .build();

        Long userId = 11111L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_NullLevels() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("空层级方案")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(null)
                .build();

        Long userId = 11111L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_NullSaveParam() {
        // Setup
        ToolProjectSaveParam saveParam = null;

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Collections.emptyList())
                .build();

        Long userId = 11111L;

        // Execute - should not throw exception
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_NullHomeDesign() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("测试方案")
                .build();

        ToolProjectHomeDesign homeDesign = null;
        Long userId = 11111L;

        // Execute - should not throw exception due to try-catch
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify - should not call refresh due to exception handling
        verify(downgradingClient, never()).refresh(anyList());
    }

    @Test
    public void testAddHotData_NullUserId() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("测试方案")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Collections.emptyList())
                .build();

        Long userId = null;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_ClientException() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("测试方案")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Collections.emptyList())
                .build();

        Long userId = 11111L;

        doThrow(new RuntimeException("Client exception")).when(downgradingClient).refresh(anyList());

        // Execute - should not throw exception due to try-catch
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }

    @Test
    public void testAddHotData_VerifyProjectDesignData() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("验证数据方案")
                .build();

        ToolProjectLevelDesign levelDesign = ToolProjectLevelDesign.builder()
                .levelId("test-level-id")
                .build();

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(99999L)
                .planId(88888L)
                .levels(Collections.singletonList(levelDesign))
                .build();

        Long userId = 77777L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify the data passed to refresh method
        ArgumentCaptor<List> listCaptor = ArgumentCaptor.forClass(List.class);
        verify(downgradingClient, times(1)).refresh(listCaptor.capture());

        List capturedList = listCaptor.getValue();
        assertNotNull("Captured list should not be null", capturedList);
        assertEquals("Should have one element", 1, capturedList.size());
        
        // The captured object should be a ProjectDesign with correct data
        Object capturedObject = capturedList.get(0);
        assertNotNull("Captured object should not be null", capturedObject);
    }

    @Test
    public void testAddHotData_LargeDataSet() {
        // Setup
        ToolProjectSaveParam saveParam = ToolProjectSaveParam.builder()
                .name("大数据集方案")
                .build();

        // Create many levels
        ToolProjectLevelDesign[] levels = new ToolProjectLevelDesign[100];
        for (int i = 0; i < 100; i++) {
            levels[i] = ToolProjectLevelDesign.builder()
                    .levelId("level-" + i)
                    .build();
        }

        ToolProjectHomeDesign homeDesign = ToolProjectHomeDesign.builder()
                .designId(12345L)
                .planId(67890L)
                .levels(Arrays.asList(levels))
                .build();

        Long userId = 11111L;

        // Execute
        downgradingService.addHotData(saveParam, homeDesign, userId);

        // Verify
        verify(downgradingClient, times(1)).refresh(anyList());
    }
}
