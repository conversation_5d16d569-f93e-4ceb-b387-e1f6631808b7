/*
 * UserIdentityRegionFinderTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.regionfinder;

import static org.junit.Assert.*;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFindResult;
import com.qunhe.diy.tool.project.service.biz.regionfinder.data.RegionFinderParam;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diybe.dms.data.Region;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */

@RunWith(MockitoJUnitRunner.class)
public class UserIdentityRegionFinderTest {

    @Mock
    private UserInfoFacade userInfoFacade;

    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;

    @InjectMocks
    private UserIdentityRegionFinder userIdentityRegionFinder;

    @Test
    public void findRegion_coohom_user() {
        Long userId = 22L;
        Mockito.when(userInfoFacade.isCoohomUser(userId)).thenReturn(true);
        RegionFindResult region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, null, false));
        Assert.assertEquals("coohom user return oversea", region.getRegion(), Region.OVERSEAS);
    }

    @Test
    public void findRegion_normal_user() {
        Long userId = 22L;
        Mockito.when(userInfoFacade.isCoohomUser(userId)).thenReturn(false);
        RegionFindResult region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, null, false));
        Assert.assertEquals("coohom user return DOMESTIC", region.getRegion(), Region.DOMESTIC);
    }

    @Test
    public void findRegion_exception_then_check_vrc() {
        Long userId = 22L;
        String vrc = "vrc";
        Mockito.when(userInfoFacade.isCoohomUser(userId)).thenThrow(new IllegalArgumentException());
        Mockito.when(projectDesignFacadeDb.getCoohomVrcList()).thenReturn(Lists.newArrayList(vrc));
        RegionFindResult region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, vrc, true));
        Assert.assertEquals("coohom vrc return oversea", region.getRegion(), Region.OVERSEAS);
        assertTrue("coohom vrc is fallback", region.isFallback());
        region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, "test", false));
        Assert.assertEquals("normal vrc return DOMESTIC", region.getRegion(), Region.DOMESTIC);
    }

    @Test
    public void findRegion_exception_then_check_host() {
        Long userId = 22L;
        String vrc = "vrc";
        Mockito.when(userInfoFacade.isCoohomUser(userId)).thenThrow(new IllegalArgumentException());
        Mockito.when(projectDesignFacadeDb.getCoohomVrcList()).thenThrow(new IllegalArgumentException());
        RegionFindResult region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, vrc, true));
        Assert.assertEquals("coohom host return oversea", region.getRegion(), Region.OVERSEAS);
        assertTrue("coohom vrc is fallback", region.isFallback());
        region = userIdentityRegionFinder.findRegion(new RegionFinderParam(null, userId, "test", false));
        Assert.assertEquals("normal host return DOMESTIC", region.getRegion(), Region.DOMESTIC);
    }

}