/*
 * ToolProjectController.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.qunhe.assembly.oplog.annotation.DiyPrint;
import com.qunhe.assembly.oplog.annotation.OpLogAnalyze;
import com.qunhe.assembly.oplog.enums.PrintTypeEnum;
import com.qunhe.diy.tool.project.service.biz.annotation.MultiLevelRevision;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesign;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiDesignBatchGetRequest;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiErrorCode;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiIdType;
import com.qunhe.diy.tool.project.service.common.data.restapi.RestApiLevelBatchUpdateRequest;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.diybe.module.restapi.common.anno.DesignId;
import com.qunhe.diybe.module.restapi.common.anno.Domain;
import com.qunhe.diybe.module.restapi.common.anno.RestApi;
import com.qunhe.diybe.module.restapi.common.data.response.RestApiBatchResponse;
import com.qunhe.diybe.module.restapi.common.enums.AuthCheckLevel;
import com.qunhe.diybe.module.restapi.common.enums.RestApiType;
import com.qunhe.diybe.module.restapi.common.error.Code;
import com.qunhe.diybe.module.restapi.common.error.ErrorDetails;
import com.qunhe.diybe.module.restapi.common.exception.RestApiProcessException;
import com.qunhe.diybe.module.restapi.common.module.RestApiDomain;
import com.qunhe.diybe.module.restapi.common.operation.Operation;
import com.qunhe.diybe.module.restapi.core.serializer.ObsIdParam;
import com.qunhe.diybe.module.restapi.core.util.ObsValueCheckUtil;
import com.qunhe.interceptors.SessionUserContext;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.auth.security.PreAuthorize;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.BATCH_GET_DESIGN_RESOURCE_PATH;
import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.DESIGN_RESOURCE_PATH;
import static com.qunhe.diy.tool.project.service.common.constant.RestApiConstant.LEVEL_BATCH_UPDATE_RESOURCE_PATH;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Domain(RestApiDomain.PROJECT_DESIGN)
@SuppressWarnings({ "PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException" })
public class ToolProjectRestApiController {

    private final static QHLogger LOG = QHLogger.getLogger(ToolProjectRestApiController.class);

    private static final int BATCH_SIZE_LIMIT = 20;

    private final ToolProjectRestApiService toolProjectRestApiService;

    private final ProjectClient projectClient;


    @GetMapping(value = DESIGN_RESOURCE_PATH)
    @RestApi(type = RestApiType.GET, authCheckLevel = AuthCheckLevel.READ)
    public RestApiDesign getProjectDesign(
            @PathVariable(value = "id") final String id,
            @RequestParam(value = "idType") final String idType)
            throws RestApiProcessException,
            DiyManageServiceException {

        RestApiIdType restApiIdType;
        try {
            restApiIdType = RestApiIdType.valueOf(idType);
        } catch (Exception e) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                    .withMessage("idType should be DESIGN or PLAN")
                    .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
            );
        }

        if (!ObsValueCheckUtil.isValid(id)) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                    .withMessage("invalid planId or designId value")
                    .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
            );
        }

        Long finalDesignId;
        Long decryptedId = LongCipher.DEFAULT.decrypt(id);
        if (restApiIdType == RestApiIdType.PLAN) {
            finalDesignId = projectClient.getDesignIdByPlanId(decryptedId);
            if (finalDesignId == null) {
                throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                        .withMessage("design not exist")
                        .withMetaData("planId", id)
                        .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
                );
            }
        } else {
            finalDesignId = decryptedId;
        }
        List<RestApiDesign> projectDesigns = toolProjectRestApiService.getProjectDesigns(
                Collections.singletonList(finalDesignId),
                SessionUserContext.getSessionUserId());
        if (CollectionUtils.isEmpty(projectDesigns)) {
            return null;
        }
        return projectDesigns.get(0);


    }

    @PostMapping(value = BATCH_GET_DESIGN_RESOURCE_PATH)
    @RestApi(type = RestApiType.CUSTOM, authCheckLevel = AuthCheckLevel.READ)
    public RestApiBatchResponse<RestApiDesign> batchGetProjectDesign(
            @RequestBody RestApiDesignBatchGetRequest request)
            throws RestApiProcessException, DiyManageServiceException {

        RestApiIdType restApiIdType;
        try {
            restApiIdType = RestApiIdType.valueOf(request.getIdType());
        } catch (Exception e) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                    .withMessage("idType should be DESIGN or PLAN")
                    .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
            );
        }

        if (request.getIds().size() > BATCH_SIZE_LIMIT) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                    .withMessage("too many ids, should not over " + BATCH_SIZE_LIMIT)
                    .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
                    .withMetaData("currentIdSize", String.valueOf(request.getIds().size()))
                    .withMetaData("maxIdSize", String.valueOf(BATCH_SIZE_LIMIT))
            );
        }

        if ((request.getIds().stream().anyMatch(d -> !ObsValueCheckUtil.isValid(d)))) {
            throw new RestApiProcessException(Code.INVALID_ARGUMENT, ErrorDetails.build()
                    .withMessage("invalid id value")
                    .withReason(RestApiErrorCode.INVALID_ARGUMENT.name())
            );
        }

        List<Long> finalDesignIds;
        if (restApiIdType == RestApiIdType.PLAN) {
            finalDesignIds = projectClient.getDesignIdsByFloorPlanIds(
                    request.getIds()
                            .stream().map(LongCipher.DEFAULT::decrypt)
                            .collect(Collectors.toList()));
        } else {
            finalDesignIds = request.getIds()
                    .stream().map(LongCipher.DEFAULT::decrypt)
                    .collect(Collectors.toList());
        }

        List<RestApiDesign> projectDesigns = toolProjectRestApiService.getProjectDesigns(
                finalDesignIds, SessionUserContext.getSessionUserId());
        return new RestApiBatchResponse<>(projectDesigns);
    }

    @MultiLevelRevision(designId = "#designId")
    @PreAuthorize("hasWrite(#designId)")
    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "batchUpdateLevels",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designId"))
    @PostMapping(LEVEL_BATCH_UPDATE_RESOURCE_PATH)
    @RestApi(type = RestApiType.CUSTOM, authCheckLevel = AuthCheckLevel.WRITE)
    public Operation<RestApiBatchResponse<LevelInfo>> batchUpdateLevels(
            @DesignId @ObsIdParam final Long designId,
            @RequestBody RestApiLevelBatchUpdateRequest request) throws RestApiProcessException,
            DiyManageServiceException {
        return Operation.finish(UUID.randomUUID().toString(),
                new RestApiBatchResponse<>(toolProjectRestApiService.batchUpdateLevels(designId,
                        request)));
    }


}
