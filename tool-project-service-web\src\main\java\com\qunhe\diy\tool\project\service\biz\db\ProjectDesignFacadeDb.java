/*
 * ProjectDesignFacadeDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.Lists;
import com.qunhe.assembly.oplog.annotation.DiyPrint;
import com.qunhe.assembly.oplog.annotation.OpLogAnalyze;
import com.qunhe.assembly.oplog.enums.PrintTypeEnum;
import com.qunhe.diy.tool.project.service.biz.constants.CacheNames;
import com.qunhe.diy.tool.project.service.biz.util.ProxyExtractor;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.client.project.DynamicProjectClient;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.VrcEnum;
import com.qunhe.diybe.dms.project.data.ProjectCols;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.diybe.dms.project.search.ProjectSearchRequest;
import com.qunhe.diybe.dms.project.search.ProjectSearchResponse;
import com.qunhe.diybe.dms.project.search.SearchQueryBuilder;
import com.qunhe.diybe.dms.project.search.query.BoolQuery;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.log.QHLogger;
import com.qunhe.project.platform.project.search.common.enums.ColDict;
import com.qunhe.projectmanagement.client.data.DefaultCols;
import com.qunhe.projectmanagement.client.data.QueryByDesignIdParam;
import com.qunhe.web.standard.exception.BizzException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class ProjectDesignFacadeDb implements ProxyExtractor<ProjectDesignFacadeDb> {

    private static final QHLogger LOG = QHLogger.getLogger(ProjectDesignFacadeDb.class);

    private static final List<String> COLS;

    private static final int SEARCH_DEFAULT_LIMIT = 20;

    private final DynamicProjectClient dynamicProjectClient;

    private final ProjectClient projectClient;

    static {
        COLS = new ArrayList<>(DefaultCols.FLOOR_PLAN_COLS);
        COLS.add(ProjectCols.NAME);
        COLS.add(ProjectCols.VRC);
        COLS.add(ProjectCols.SAVED);
        COLS.add(ProjectCols.DESIGN_ATTR_PRIVATE_STATUS);
        COLS.add(ProjectCols.PLAN_PIC_ID);
        COLS.add(ProjectCols.DELETED);
        COLS.add(ProjectCols.USER_ID);
        COLS.add(ProjectCols.DESC);
        COLS.add(ProjectCols.COMMUNITY_NAME);
        COLS.add(ProjectCols.COMM_LOGIC_AREA_ID);
        COLS.add(ProjectCols.COMM_LOGIC_PROVINCE_ID);
        COLS.add(ProjectCols.PLAN_ID);
        COLS.add(ProjectCols.COMM_ID);
    }

    @Autowired
    public ProjectDesignFacadeDb(final DynamicProjectClient dynamicProjectClient,
            final ProjectClient diyManageClientProjectClient) {
        this.dynamicProjectClient = dynamicProjectClient;
        this.projectClient = diyManageClientProjectClient;
    }

    @SentinelResource(value = "createProjectSequence")
    public ProjectSequence createProject(final ToolProjectSaveParam toolProjectSaveParam)
            throws BizzException {
        ProjectDesign projectDesign = convert2ProjectDesign(toolProjectSaveParam);
        ProjectSequence projectSequence =
                dynamicProjectClient.createProjectDesignWithVrcAndExtraAttr(projectDesign,
                        toolProjectSaveParam.getCreatedAppId());
        if (projectSequence == null) {
            throw new ToolProjectCreateException(ErrorCode.CREATE_ERROR);
        }
        return projectSequence;
    }

    @SentinelResource(value = "getProjectDesignByDesignId")
    public ProjectDesign getProject(final QueryByDesignIdParam queryByDesignIdParam) {
        queryByDesignIdParam.setCols(COLS);
        return projectClient.getProjectDesignByDesignId(queryByDesignIdParam.getDesignId(), COLS);
    }

    public ProjectDesign getProject(final Long planId) {
        return projectClient.getProjectDesignByPlanId(planId, COLS);
    }

    public ProjectDesign getProjectByDesignId(final Long designId) {
        return projectClient.getProjectDesignByDesignId(designId, COLS);
    }

    public Map<Long, ProjectDesign> getProjectByDesignIds(final List<Long> designIds) {
        List<ProjectDesign> projectDesigns = projectClient.getProjectDesignsByDesignIds(designIds,
                COLS);
        if (CollectionUtils.isEmpty(projectDesigns)) {
            return Collections.emptyMap();
        }
        return projectDesigns.stream().collect(
                Collectors.toMap(ProjectDesign::getDesignId, Function.identity()));
    }

    public int updateProjectWithModifiedTime(final boolean silent,
            final ProjectDesign design) {
        return projectClient.updateProjectDesign(silent, design);
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "recoverProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#planId"))
    public Integer recoverProject(final Long planId) {
        return projectClient.recoverPlanFromRecycleBin(planId);
    }

    public Integer batchRecoverProject(final List<Long> designIds) {
        return projectClient.batchRecoverPlanFromRecycleBin(designIds);
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "restoreProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designId"))
    public int restoreProject(final Long userId, final Long designId, boolean needCheckUserId) {
        if (userId != null && needCheckUserId) {
            return projectClient.restoreDesign(designId, userId);
        }
        return projectClient.restoreDesignWithoutCheck(designId);
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "removeProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designId"))
    public int deleteProject(final Long designId) {
        return projectClient.deleteDesignsByDesignIds(
                Collections.singletonList(designId));
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "removeProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designIds"))
    @Retryable
    public int batchDeleteProject(final List<Long> designIds) {
        if (CollectionUtils.isEmpty(designIds)) {
            return 0;
        }
        return projectClient.deleteDesignsByDesignIds(designIds);
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "rollbackRemoveProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designIds"))
    @Retryable
    public int batchRollbackRemoveProject(final List<Long> designIds) {
        if (CollectionUtils.isEmpty(designIds)) {
            return 0;
        }
        return projectClient.rollbackDeleteDesignsByDesignIds(designIds);
    }

    public Long getPlanId(final Long designId) {
        return projectClient.getPlanIdByDesignIdWithoutDeleted(
                designId);
    }

    public Long getDesignId(final Long planId) {
        return projectClient.getDesignIdByPlanIdWithDeletedCheck(planId);
    }

    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "rollbackRemoveProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#planId"))
    public int recoverDeleteProject(final Long planId) {
        return projectClient.recoverDeletedPlan(planId);
    }


    @OpLogAnalyze(domain = "TOOL_PROJECT_SERVICE", intent = "recycleProject",
            diyPrint = @DiyPrint(printType = { PrintTypeEnum.ALL_ARGS }, opTargetKey =
                    "#designId"))
    @SentinelResource(value = "recycleDesign")
    public int recycleDesign(final Long userId, final Long designId) {
        return projectClient.recycleDesign(designId, userId);
    }

    public int batchRecycleDesign(final Long userId, final List<Long> designIds) {
        return projectClient.batchRecycleDesign(designIds, userId);
    }

    @Retryable(maxAttempts = 3)
    public boolean switchDesignUser(final Long fromUserId, final Long userId, final Long designId) {
        return dynamicProjectClient.switchDesignUser(designId, fromUserId, userId);
    }

    public List<Long> getDesignsByUserId(final Long userId) {
        return projectClient.getDesignIdsByUserId(userId);
    }

    @SentinelResource(value = "countDesignsByUserIdIgnoreRecycle")
    public int countDesignsByUserIdIgnoreRecycle(final Long userId) {
        final ProjectSearchResponse<ProjectDesign> response = searchUndeletedProjectsByUserId(0,
                SEARCH_DEFAULT_LIMIT, userId, COLS);
        return Math.toIntExact(response.getCount());
    }

    public List<ProjectDesign> searchProjectsByUserIdIgnoreDelete(int start, int num,
            final Long userId,
            final Collection<String> cols) {
        final ProjectSearchResponse<ProjectDesign> response = searchUndeletedProjectsByUserId(start,
                num, userId, cols);
        if (response == null || CollectionUtils.isEmpty(response.getContent())) {
            return Collections.emptyList();
        }
        return response.getContent();
    }

    private ProjectSearchResponse<ProjectDesign> searchUndeletedProjectsByUserId(int start, int num,
            final Long userId,
            final Collection<String> cols) {
        ProjectSearchResponse<ProjectDesign> response = new ProjectSearchResponse<>();

        //回收站方案 (注意复制的方案authorId = 原方案userid)
        final BoolQuery binQuery = SearchQueryBuilder.bool().must(
                SearchQueryBuilder.term(ColDict.AUTHOR_ID.getEsField(),
                        userId)).must(SearchQueryBuilder.term(ColDict.USER_ID.getEsField(), 1));

        final BoolQuery boolQuery = SearchQueryBuilder.bool()
                .filter(SearchQueryBuilder.term(ColDict.DELETED.getEsField(), false))
                .should(SearchQueryBuilder.term(ColDict.USER_ID.getEsField(), userId))
                .should(binQuery)
                .minimumShouldMatch("1");

        final ProjectSearchRequest searchRequest = ProjectSearchRequest.builder()
                .from(start)
                .size(num)
                .query(boolQuery)
                .include(cols)
                .sortByDesc(ColDict.MODIFIED_TIME.getEsField())
                .build();
        LOG.message("searchProjectsByUserIdIgnoreDelete")
                .with("dsl", searchRequest.getQueryDsl())
                .with("traceId", SpanContextHolder.getTraceId())
                .info();
        try {
            response = dynamicProjectClient.searchProject("searchProjectsByUserIdIgnoreDelete",
                    searchRequest);
        } catch (Exception e) {
            //搜索超时或异常
            LOG.message("searchUndeletedProjectsByUserId", e)
                    .with("start", start)
                    .with("num", num)
                    .with("userId", userId)
                    .error();
        }
        return response;
    }

    @SentinelResource("getCoohomVrcList")
    public List<String> getCoohomVrcList() {
        return projectClient.getCoohomVrcList();
    }

    private ProjectDesign convert2ProjectDesign(final ToolProjectSaveParam toolProjectSaveParam) {
        ProjectDesign projectDesign = new ProjectDesign();
        projectDesign.setCommId(toolProjectSaveParam.getCommId());
        projectDesign.setArea(toolProjectSaveParam.getArea());
        projectDesign.setRealArea(toolProjectSaveParam.getRealArea());
        projectDesign.setSrcArea(toolProjectSaveParam.getSrcArea());
        projectDesign.setUserId(toolProjectSaveParam.getUserId());
        projectDesign.setRecommend(toolProjectSaveParam.getRecommend() ? 1 : 0);
        projectDesign.setReworked(toolProjectSaveParam.getReworked() ? 1 : 0);
        projectDesign.setPlanType(toolProjectSaveParam.getPlanType());
        projectDesign.setSourceId(toolProjectSaveParam.getSourceId());
        projectDesign.setDesignName(toolProjectSaveParam.getName());
        projectDesign.setDesignAlbumId(toolProjectSaveParam.getAlbumId());
        projectDesign.setModelDataId(toolProjectSaveParam.getModelDataId());
        projectDesign.setDesignDesignDataId(toolProjectSaveParam.getDesignDataId());
        projectDesign.setVrc(toolProjectSaveParam.getVrc());
        projectDesign.setDesignSaved(toolProjectSaveParam.getDesignSaved() ? 1 : 0);
        projectDesign.setUnitId(toolProjectSaveParam.getUnitId());
        projectDesign.setSpecId(toolProjectSaveParam.getSpecId());
        projectDesign.setModelStatus(toolProjectSaveParam.getModelStatus());
        projectDesign.setDesignattrPrivatestatus(toolProjectSaveParam.getDesignAttribute());
        projectDesign.setUploadPics(toolProjectSaveParam.getUploadPics());
        projectDesign.setAuthorId(toolProjectSaveParam.getAuthorId());
        projectDesign.setDesignDesc(toolProjectSaveParam.getDesignDesc());
        projectDesign.setRegion(toolProjectSaveParam.getRegion());
        return projectDesign;
    }

    @Cacheable(value = CacheNames.DIY_DESIGN_INFO_DESIGN_ID, key = "#designId")
    public DiyDesignInfo getDiyDesignInfo(final Long designId) {
        return dynamicProjectClient.getProjectDesignByDesignId(designId);
    }

    public ProjectDesign getVrcAndParentId(final Long designId) {
        try {
            final ProjectDesign projectDesign = projectClient.getProjectDesignByDesignId(
                    designId, Lists.newArrayList(ProjectCols.VRC, ProjectCols.COPY_LOG_PARENT_ID));
            if (projectDesign == null) {
                LOG.message("getVrcAndParentId - null")
                        .with("designId", designId)
                        .warn();
                return null;
            }
            return projectDesign;
        } catch (final Exception e) {
            // 异常返回 null
            LOG.message("getVrcAndParentId - error")
                    .with("designId", designId)
                    .warn();
            return null;
        }
    }

    @SentinelResource(value = "PlanDb#updateVrc", blockHandler = "updateVrcFallback")
    public void updateVrc(final Long planId, String vrc) {
        LOG.message("insertFloorPlan - updateVrc - vrc")
                .with("vrc", vrc)
                .info();
        if (StringUtils.isBlank(vrc)) {
            vrc = VrcEnum.BIM.getCode();
        }
        final int res = projectClient.updateVrc(planId, vrc);
        if (res == 0) {
            LOG.message("insertFloorPlan - updateVrc - fail")
                    .with("planId", planId)
                    .warn();
        }
    }

    public boolean updateVrcFallback(final Long planId, String vrc, final BlockException e)
            throws BlockException {
        LOG.message("insertFloorPlan - updateVrc - error", e)
                .with("planId", planId)
                .with("vrc", vrc)
                .warn();
        throw e;
    }

}
