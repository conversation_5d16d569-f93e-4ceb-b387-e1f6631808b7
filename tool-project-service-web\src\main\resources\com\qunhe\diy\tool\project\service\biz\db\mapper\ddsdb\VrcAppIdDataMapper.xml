<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ ElementLabelMapper.xml
  ~ Copyright 2023 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.VrcAppIdDataMapper">

    <resultMap id="resultMap"
               type="com.qunhe.diy.tool.project.service.common.data.VrcAppIdData">
        <result column="appkey" property="appKey" />
        <result column="vrc" property="vrc" />
        <result column="v_type" property="vType" />
        <result column="r_type" property="rType" />
        <result column="appid" property="appId" />
        <result column="access_point" property="accessPoint" />
        <result column="appname" property="appName" />
        <result column="creator" property="creator" />
        <result column="stage" property="stage" />
        <result column="description" property="description" />
        <result column="region" property="region" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        appkey,
        vrc,
        v_type,
        r_type,
        appid,
        access_point,
        appname,
        creator,
        stage,
        description,
        created,
        lastmodified,
        region
    </sql>

    <delete id="deleteVrcAppIdData">
        DELETE FROM vrc_appid
        WHERE v_type = #{vType}
          AND appid = #{appId}
        <if test="stage != null">AND stage = #{stage}</if>
    </delete>

    <select id="getAllVrcAppIdList"
            resultMap="resultMap">
        SELECT <include refid="Base_Column_List"/>  FROM vrc_appid where stage = #{stage}
    </select>


    <!-- 注意！尽量不要直接使用 select *,请根据实际情况选用需要的字段 -->
    <select id="getVrcByVtypeAndAppId" resultType="String">
        SELECT vrc
        FROM vrc_appid
        WHERE v_type = #{vType}
        AND appid = #{appId}
        AND stage = #{stage}
        LIMIT 1
    </select>

    <select id="getByVTypeAndAppId" resultMap="resultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM vrc_appid
        WHERE v_type = #{vType}
          AND appid = #{appId}
        AND stage = #{stage}
            LIMIT 1
    </select>

    <insert id="saveVrcAppIdData" parameterType="com.qunhe.diy.tool.project.service.common.data.VrcAppIdData" >
        INSERT INTO vrc_appid (
        <trim suffixOverrides=",">
            <if test="appKey != null">appKey,</if>
            <if test="vrc != null">vrc,</if>
            <if test="vType != null">v_type,</if>
            <if test="rType != null">r_type,</if>
            <if test="appId != null">appid,</if>
            <if test="accessPoint != null">access_point,</if>
            <if test="appName != null">appname,</if>
            <if test="creator != null">creator,</if>
            <if test="stage != null">stage,</if>
            <if test="description != null">description,</if>
            <if test="region != null">region,</if>
        </trim>
        )
        Values (
        <trim suffixOverrides=",">
            <if test="appKey != null">#{appKey},</if>
            <if test="vrc != null">#{vrc},</if>
            <if test="vType != null">#{vType},</if>
            <if test="rType != null">#{rType},</if>
            <if test="appId != null">#{appId},</if>
            <if test="accessPoint != null">#{accessPoint},</if>
            <if test="appName != null">#{appName},</if>
            <if test="creator != null">#{creator},</if>
            <if test="stage != null">#{stage},</if>
            <if test="description != null">#{description},</if>
            <if test="region != null">#{region},</if>
        </trim>
        )
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            <if test="appKey != null">appkey = #{appKey},</if>
            <if test="vrc != null">vrc = #{vrc},</if>
            <if test="vType != null">v_type = #{vType},</if>
            <if test="rType != null">r_type = #{rType},</if>
            <if test="appId != null">appid = #{appId},</if>
            <if test="accessPoint != null">access_point =#{accessPoint},</if>
            <if test="appName != null">appname =#{appName},</if>
            <if test="creator != null">creator =#{creator},</if>
            <if test="stage != null">stage =#{stage},</if>
            <if test="description != null">description =#{description},</if>
            <if test="region != null">region=#{region},</if>
        </trim>
    </insert>

    <update id="updateVrcAppIdData" parameterType="com.qunhe.diy.tool.project.service.common.data.VrcAppIdData">
        UPDATE vrc_appid
        <set>
            <trim suffixOverrides=",">
                <if test="appkey != null">appkey = #{appKey},</if>
                <if test="vrc != null">vrc = #{vrc},</if>
                <if test="vType != null">v_type = #{vType},</if>
                <if test="rType != null">r_type = #{rType},</if>
                <if test="appId != null">appid = #{appId},</if>
                <if test="accessPoint != null">access_point =#{accessPoint},</if>
                <if test="appName != null">appname =#{appName},</if>
                <if test="creator != null">creator =#{creator},</if>
                <if test="stage != null">stage =#{stage},</if>
                <if test="description != null">description =#{description},</if>
                <if test="region != null">region=#{region},</if>
            </trim>
        </set>
        WHERE v_type = #{vType}
        AND appid = #{appId}
        AND stage = #{stage}

    </update>
</mapper>
