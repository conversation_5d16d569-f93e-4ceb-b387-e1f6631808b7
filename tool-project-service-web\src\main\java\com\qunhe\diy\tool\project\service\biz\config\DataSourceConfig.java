/*
 * DataSourceConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.middleware.mybatis.tddl.starter.DataSourceMapperConfigure;
import com.qunhe.middleware.mybatis.tddl.starter.DataSourceMapperConfigures;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@DataSourceMapperConfigures({

        @DataSourceMapperConfigure(
                tddlAppName = "${qunhe.fenshua123-datasource.app-name}",
                mapperXmlLocations = "classpath:com/qunhe/diy/tool/project/service/biz/db/mapper" +
                        "/fenshua/*.xml",
                mapperBasePackages = "com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua",
                sqlSessionFactoryBeanName = "sqlSessionFactory"
        ),
        @DataSourceMapperConfigure(
                tddlAppName = "${qunhe.tddl.booster-dds-db}",
                mapperXmlLocations = "classpath:com/qunhe/diy/tool/project/service/biz/db/mapper" +
                        "/ddsdb/*.xml",
                mapperBasePackages = "com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb"
        )

})
@Configuration
public class DataSourceConfig {
}
