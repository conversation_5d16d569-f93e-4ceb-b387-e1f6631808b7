/*
 * ProjectNotifyService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.project.data.ProjectSequence;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.instdeco.plan.yun.core.client.apis.DecoProjectApi;
import com.qunhe.instdeco.plan.yuncoreapi.clients.DesignClient;
import com.qunhe.log.QHLogger;
import com.qunhe.user.growth.floor.plan.cool.client.client.IncomingClient;
import com.qunhe.user.growth.floor.plan.cool.common.data.incoming.BindDrawingDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ProjectNotifyService {

    private static final QHLogger LOG = QHLogger.getLogger(ProjectNotifyService.class);

    private final IncomingClient incomingClient;
    private final DecoProjectApi decoProjectApi;
    private final DesignClient designClient;
    private final ToadProperties toadProperties;

    @Autowired
    public ProjectNotifyService(final IncomingClient incomingClient,
            DecoProjectApi decoProjectApi, DesignClient designClient,
            ToadProperties toadProperties) {
        this.incomingClient = incomingClient;
        this.decoProjectApi = decoProjectApi;
        this.designClient = designClient;
        this.toadProperties = toadProperties;
    }

    @Async("createExecutor")
    public void notifyProjectCreate(final ToolProjectSaveParam toolProjectSaveParam,
            final ProjectSequence projectSequence, final SessionUser sessionUser) {
        if (StringUtils.isNotBlank(toolProjectSaveParam.getDarenIncomingId())) {
            notifySiteForDarenCreate(projectSequence.getDesignId(),
                    toolProjectSaveParam.getDarenIncomingId());
        }
        notifySiteForDesignCreated(projectSequence.getDesignId(), toolProjectSaveParam.getUserId(),
                sessionUser);
    }

    public void notifySiteForDarenCreate(final long designId, final String incomingId) {
        try {
            final BindDrawingDto bindDrawingDto = new BindDrawingDto();
            bindDrawingDto.setDesignId(designId);
            bindDrawingDto.setIncomingId(LongCipher.DEFAULT.decrypt(incomingId));
            incomingClient.bindDrawingDesign(bindDrawingDto);
        } catch (final Exception e) {
            // 忽略异常，不影响后续流程
            LOG.message("notifySiteForDarenCreate - error")
                    .with("designId", designId)
                    .with(e)
                    .warn();
        }
    }

    public void notifySiteForDesignCreated(final long designId, final long userId,
            final SessionUser sessionUser) {
        try {
            decoProjectApi.addDesignAttributeDefaultPrivate(userId, sessionUser == null ? 0 : sessionUser
                    .getUserType(), designId);
        } catch (final Exception e) {
            // 忽略异常，不影响后续流程
            LOG.message("addDesignAttribute - error")
                    .with("designId", designId)
                    .with("userId", userId)
                    .with(e)
                    .warn();
        }
    }

}
