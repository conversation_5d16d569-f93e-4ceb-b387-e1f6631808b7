/*
 * DesignSearchException.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.exception;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
public class DesignSearchException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public DesignSearchException(String message) {
        super(message);
    }

    public DesignSearchException(String message, Throwable cause) {
        super(message, cause);
    }
}
