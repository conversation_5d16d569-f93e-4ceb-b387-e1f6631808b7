/*
 * FloorPlanMetaDataDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.FloorPlanMetaDataMapper;
import com.qunhe.diy.tool.project.service.common.data.FloorPlanMetaData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FloorPlanMetaDataDbTest {

    @Mock
    private FloorPlanMetaDataMapper mockFloorPlanMetaDataMapper;

    private FloorPlanMetaDataDb floorPlanMetaDataDbUnderTest;

    @Before
    public void setUp() throws Exception {
        floorPlanMetaDataDbUnderTest = new FloorPlanMetaDataDb(mockFloorPlanMetaDataMapper);
    }

    @Test
    public void testAddOrUpdateDwDrawingVersion() {
        // Setup
        // Run the test
        floorPlanMetaDataDbUnderTest.addOrUpdateDwDrawingVersion(0L, "dwDrawingVersion");

        // Verify the results
        verify(mockFloorPlanMetaDataMapper).addOrUpdateDwDrawingVersion(0L, "dwDrawingVersion");
    }

    @Test
    public void testCopyDwDrawingVersion() {
        // Setup
        // Configure FloorPlanMetaDataMapper.selectByPlanId(...).
        final FloorPlanMetaData floorPlanMetaData = FloorPlanMetaData.builder()
                .dwDrawingVersion("dwDrawingVersion")
                .build();
        when(mockFloorPlanMetaDataMapper.selectByPlanId(0L)).thenReturn(floorPlanMetaData);

        // Run the test
        floorPlanMetaDataDbUnderTest.copyDwDrawingVersion(0L, 0L);

        // Verify the results
        verify(mockFloorPlanMetaDataMapper).addOrUpdateDwDrawingVersion(0L, "dwDrawingVersion");
    }

    @Test
    public void testCopyDwDrawingVersion_FloorPlanMetaDataMapperSelectByPlanIdReturnsNull() {
        // Setup
        when(mockFloorPlanMetaDataMapper.selectByPlanId(0L)).thenReturn(null);

        // Run the test
        floorPlanMetaDataDbUnderTest.copyDwDrawingVersion(0L, 0L);

        // Verify the results
        verify(mockFloorPlanMetaDataMapper, times(0)).
                addOrUpdateDwDrawingVersion(anyLong(), anyString());
    }
}
