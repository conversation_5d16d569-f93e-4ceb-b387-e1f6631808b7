/*
 * CoverPicFacadeTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.user.growth.design.excellent.client.client.UserDesignClient;
import com.qunhe.user.growth.design.excellent.common.data.dto.SimpleDesignDto;
import com.qunhe.web.standard.exception.BizzException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CoverPicFacadeTest {

    @Mock
    private UserDesignClient mockUserDesignClient;

    private CoverPicFacade coverPicFacadeUnderTest;

    @Before
    public void setUp() throws Exception {
        coverPicFacadeUnderTest = new CoverPicFacade(mockUserDesignClient);
    }
    
    @Test
    public void testGetCoverPicsByPlanIds_Success() throws BizzException {
        // 准备测试数据
        List<Long> planIds = Arrays.asList(1L, 2L, 3L);
        
        SimpleDesignDto design1 = new SimpleDesignDto();
        design1.setPlanId(1L);
        design1.setCoverPic("cover1.jpg");
        
        SimpleDesignDto design2 = new SimpleDesignDto();
        design2.setPlanId(2L);
        design2.setCoverPic("cover2.jpg");
        
        SimpleDesignDto design3 = new SimpleDesignDto();
        design3.setPlanId(3L);
        design3.setCoverPic(null); // 测试 null 封面图片的情况
        
        // 设置 mock 行为
        when(mockUserDesignClient.getCoverPicByPlanIds(planIds))
                .thenReturn(Arrays.asList(design1, design2, design3));
        
        // 执行测试
        Map<Long, String> result = coverPicFacadeUnderTest.getCoverPicsByPlanIds(planIds);
        
        // 验证结果
        assertThat(result).hasSize(2);
        assertThat(result.get(1L)).isEqualTo("cover1.jpg");
        assertThat(result.get(2L)).isEqualTo("cover2.jpg");
        assertThat(result.containsKey(3L)).isFalse(); // 应该过滤掉 null 封面
    }
    
    @Test
    public void testGetCoverPicsByPlanIds_EmptyInput() {
        // 执行测试 - 空列表
        Map<Long, String> result1 = coverPicFacadeUnderTest.getCoverPicsByPlanIds(Collections.emptyList());
        assertThat(result1).isEmpty();
        
        // 执行测试 - null 输入
        Map<Long, String> result2 = coverPicFacadeUnderTest.getCoverPicsByPlanIds(null);
        assertThat(result2).isEmpty();
    }
    
    @Test
    public void testGetCoverPicsByPlanIds_EmptyResponse() throws BizzException {
        // 准备测试数据
        List<Long> planIds = Arrays.asList(1L, 2L);
        
        // 设置 mock 行为 - 返回空结果
        when(mockUserDesignClient.getCoverPicByPlanIds(planIds)).thenReturn(Collections.emptyList());
        
        // 执行测试
        Map<Long, String> result = coverPicFacadeUnderTest.getCoverPicsByPlanIds(planIds);
        
        // 验证结果
        assertThat(result).isEmpty();
    }
    
    @Test
    public void testGetCoverPicsByPlanIds_ThrowsException() throws BizzException {
        // 准备测试数据
        List<Long> planIds = Arrays.asList(1L, 2L);
        
        // 设置 mock 行为 - 抛出异常
        when(mockUserDesignClient.getCoverPicByPlanIds(planIds))
                .thenThrow(new BizzException("业务异常"));
        
        // 执行测试
        Map<Long, String> result = coverPicFacadeUnderTest.getCoverPicsByPlanIds(planIds);
        
        // 验证结果 - 应该返回空 Map
        assertThat(result).isEmpty();
    }
    
    @Test
    public void testGetCoverPicsByPlanIdsFallback() {
        // 准备测试数据
        List<Long> planIds = Arrays.asList(1L, 2L);
        Throwable exception = new RuntimeException("测试异常");
        
        // 执行测试
        Map<Long, String> result = coverPicFacadeUnderTest.getCoverPicsByPlanIdsFallback(planIds, exception);
        
        // 验证结果
        assertThat(result).isEmpty();
    }
    
    @Test
    public void testGetCoverPicsByPlanIds_DuplicateKeys() throws BizzException {
        // 准备测试数据
        List<Long> planIds = Arrays.asList(1L, 1L); // 重复的 planId
        
        SimpleDesignDto design1 = new SimpleDesignDto();
        design1.setPlanId(1L);
        design1.setCoverPic("cover1.jpg");
        
        SimpleDesignDto design2 = new SimpleDesignDto();
        design2.setPlanId(1L);
        design2.setCoverPic("cover2.jpg");
        
        // 设置 mock 行为
        when(mockUserDesignClient.getCoverPicByPlanIds(planIds))
                .thenReturn(Arrays.asList(design1, design2));
        
        // 执行测试
        Map<Long, String> result = coverPicFacadeUnderTest.getCoverPicsByPlanIds(planIds);
        
        // 验证结果 - 应该保留第一个值
        assertThat(result).hasSize(1);
        assertThat(result.get(1L)).isEqualTo("cover1.jpg");
    }
}
