package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SaasConfigFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayLunchService;
import com.qunhe.diy.tool.project.service.web.page.diy.GrayVersionConstants;
import com.qunhe.instdeco.plan.data.SessionUser;
import com.qunhe.instdeco.plan.graylaunchmw.data.HttpRequestMetaData;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * 测试 AbstractDiyPageHandler 的 protected 方法。
 * <AUTHOR>
 * @date 2025/5/12
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractDiyPageHandlerTest {

    @InjectMocks
    private TestDiyPageHandler testDiyPageHandler;

    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private BusinessAccountDb businessAccountDb;
    @Mock
    private BusinessAccessService businessAccessService;
    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    private VrcAppIdDataDb vrcAppIdDataDb;
    @Mock
    private SessionConflictHandler sessionConflictHandler;
    @Mock
    private GrayLunchService grayLunchService;
    @Mock
    private ToadProperties toadProperties;
    @Mock
    private UserDb userDb;
    @Mock
    private SaaSConfigService saaSConfigService;
    @Mock
    private ToolLinkConfigCache toolLinkConfigCache;
    @Mock
    private SaasConfigFacade saasConfigFacade;
    @Mock
    private AuthCheckService authCheckService;

    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private DiyPageParam param;
    
    private static final Long USER_ID = 1L;
    private static final Long ROOT_ACCOUNT_ID = 10L;
    private static final Long DESIGN_ID_DECRYPTED = 123L;
    private static final String OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(DESIGN_ID_DECRYPTED);
    private static final String ENCRYPTED_USER_ID = LongCipher.DEFAULT.encrypt(USER_ID);
    private static final String ENCRYPTED_PLAN_ID_RAW = LongCipher.DEFAULT.encrypt(DESIGN_ID_DECRYPTED + 1);
    private static final String ACTUAL_APP_ID = "actualAppId";
    private static final Long APP_ID_NUM = 100L;
    
    private static final Byte MOCK_BIM_PLAN_TYPE = 10; 
    private static final Byte MOCK_PHOTO_STUDIO_PLAN_TYPE = 20; 


    @Before
    public void setUp() {
        when(param.getUserId()).thenReturn(USER_ID);
        lenient().when(param.getObsDesignId()).thenReturn(OBS_DESIGN_ID);
        lenient().when(param.isFromCoohom()).thenReturn(false);

        lenient().when(businessAccountDb.getRootAccountForBAndC(USER_ID)).thenReturn(ROOT_ACCOUNT_ID);
        lenient().when(businessAccessService.checkBimAccess(USER_ID)).thenReturn(false);

        lenient().when(toadProperties.getDomainHost()).thenReturn("https://test.coohom.com");
        lenient().when(toadProperties.getYunHost()).thenReturn("https://yun.coohom.com");
        lenient().when(toadProperties.getLoginUrl()).thenReturn("https://www.coohom.com/login");
        lenient().when(toadProperties.getProjectStage()).thenReturn("test");
        lenient().when(toadProperties.getVrcConfig()).thenReturn("test_vrc_config");

        // Mock 用户信息
        H5Model mockH5Model = new H5Model();
        mockH5Model.setUserContactName("Test User");
        
        SessionUser sessionUser = new SessionUser();
        sessionUser.setUserId(USER_ID);
        sessionUser.setUserVersion((byte)1);
        
        UserDto userDto = new UserDto();
        userDto.setUserName("testUser");
        lenient().when(userDb.getUserBySession()).thenReturn(userDto);
        lenient().when(userInfoFacade.checkKubiInfo(USER_ID)).thenReturn(100);
        lenient().when(userInfoFacade.getUserNameByUserId(USER_ID)).thenReturn("Test User");

        // Mock 工具链接配置
        com.qunhe.instdeco.plan.businessconfig.BusinessConfig toolLinkConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfig.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap allToolConfigs = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap diyConfigs = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement recentChangesConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement renderFeedbackConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);
        com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement diyCreate3dPanoConfig = Mockito.mock(com.qunhe.instdeco.plan.businessconfig.BusinessConfigElement.class);

        lenient().when(toolLinkConfig.getAllToolConfig()).thenReturn(allToolConfigs);
        lenient().when(toolLinkConfig.getDiyConfig()).thenReturn(diyConfigs);
        lenient().when(allToolConfigs.get("recentChanges")).thenReturn(recentChangesConfig);
        lenient().when(recentChangesConfig.getShow()).thenReturn(true);
        lenient().when(diyConfigs.get("renderFeedback")).thenReturn(renderFeedbackConfig);
        lenient().when(renderFeedbackConfig.getShow()).thenReturn(true);
        lenient().when(diyConfigs.get("diyCreate3dPano")).thenReturn(diyCreate3dPanoConfig);
        lenient().when(diyCreate3dPanoConfig.getShow()).thenReturn(true);
        lenient().when(toolLinkConfigCache.getDefaultConfig()).thenReturn(toolLinkConfig);

        // Mock 收藏图标配置
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap favorIconConfigMap = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement favorIconElement = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement.class);
        lenient().when(saaSConfigService.getBusinessConfigMap(USER_ID, SaaSConfigService.COMMON_CONFIG_FAVOR_ICON))
                .thenReturn(favorIconConfigMap);
        lenient().when(favorIconConfigMap.isNotEmpty()).thenReturn(true);
        lenient().when(favorIconConfigMap.get(SaaSConfigService.COMMON_CONFIG_FAVOR_ICON)).thenReturn(favorIconElement);
        lenient().when(favorIconElement.getShow()).thenReturn(true);
        lenient().when(favorIconElement.getPicUrl()).thenReturn("http://example.com/icon.png");

        // Mock 语言配置
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap langConfigMap = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap.class);
        com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement langElement = Mockito.mock(com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement.class);
        lenient().when(saaSConfigService.getBusinessConfigMap(USER_ID, SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE))
                .thenReturn(langConfigMap);
        lenient().when(langConfigMap.isEmpty()).thenReturn(false);
        lenient().when(langConfigMap.get(SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE)).thenReturn(langElement);
        lenient().when(langElement.getStatus()).thenReturn(0);

        // Mock AB测试结果
        lenient().when(saaSConfigService.getAbTestResult(anyString(), any(), anyLong(), anyString()))
                .thenReturn(new HashMap<>());

        // Mock 灰度发布检查
        lenient().when(grayLunchService.checkVersion(eq(GrayVersionConstants.YUN_TU_PERSSION), eq(GrayVersionConstants.ALLOW), any(HttpRequestMetaData.class)))
                .thenReturn(true);
    }

    /**
     * 测试 fillFavorIcon 方法：当配置存在且显示时。
     */
    @Test
    public void testFillFavorIcon_WhenConfigExistsAndShow() {
        // Arrange
        H5Model h5Model = new H5Model();
        
        // Act
        testDiyPageHandler.fillFavorIcon(USER_ID, h5Model);
        
        // Assert
        assertNotNull("favorIcon should not be null", h5Model.getFavorIcon());
        assertEquals("icon url should match",
            "http://example.com/icon.png",
            h5Model.getFavorIcon().getPicUrl());
    }

    /**
     * 测试 fillToolConfig 方法：默认配置存在。
     */
    @Test
    public void testFillToolConfig_WithDefaultConfig() {
        // Arrange
        H5Model h5Model = new H5Model();
        
        // Act
        testDiyPageHandler.fillToolConfig(h5Model);
        
        // Assert
        assertNotNull("toolJsConfigJson should not be null", h5Model.getToolJsConfigJson());
        assertTrue("JSON should contain diyCreate3dPano show value",
            h5Model.getToolJsConfigJson().contains("\"show\":true"));
    }

    /**
     * 测试 fillUserInfo 方法：用户信息正常。
     */
    @Test
    public void testFillUserInfo_WithValidUser() {
        // Arrange
        H5Model h5Model = new H5Model();
        
        // Act
        testDiyPageHandler.fillUserInfo(USER_ID, h5Model);
        
        // Assert
        assertNotNull("user should not be null", h5Model.getUser());
        assertEquals("encrypted user id should match",
            ENCRYPTED_USER_ID,
            h5Model.getUser().getObsUserId());
    }

    /**
     * 测试 useNewDecorationVersion 方法：sessionUser 为 null。
     */
    @Test
    public void testUseNewDecorationVersion_SessionUserNull() {
        // Arrange
        when(userDb.getSessionUserBySession()).thenReturn(null);
        
        // Act
        boolean result = testDiyPageHandler.useNewDecorationVersion(request);
        
        // Assert
        assertFalse("should return false when sessionUser is null", result);
    }

    /**
     * 测试 fillPlanInfo 方法：填充计划信息。
     */
    @Test
    public void testFillPlanInfo() {
        // Arrange
        H5Model h5Model = new H5Model();
        String obsSrcPlanId = "obsSrcPlanId123";
        String planName = "Test Plan";
        String askId = "ask456";
        
        // Act
        testDiyPageHandler.fillPlanInfo(obsSrcPlanId, planName, askId, MOCK_BIM_PLAN_TYPE, h5Model);
        
        // Assert
        assertNotNull("planInfo should not be null", h5Model.getPlanInfo());
        assertEquals("plan name should match",
            planName,
            h5Model.getPlanInfo().getPlanConfig().getPlanName());
    }

    /**
     * 测试 fillAccountInfo 方法：语言配置为中文。
     */
    @Test
    public void testFillAccountInfo_ChineseLang() {
        // Arrange
        H5Model h5Model = new H5Model();
        
        // Act
        testDiyPageHandler.fillAccountInfo(USER_ID, ROOT_ACCOUNT_ID, h5Model);
        
        // Assert
        assertNotNull("rootAccount should not be null", h5Model.getRootAccount());
        assertEquals("language should be Chinese",
            "zh_CN",
            h5Model.getRootAccount().getLang());
    }

    /**
     * 测试 checkUpgradeAccess 方法：没有升级权限。
     */
    @Test
    public void testCheckUpgradeAccess_NoUpgradePermission() {
        // Act
        boolean result = testDiyPageHandler.checkUpgradeAccess(null, null, null);
        
        // Assert
        assertFalse("should return false when no upgrade permission",
            result);
    }

    /**
     * 测试 redirectByUrl 方法：基本重定向。
     */
    @Test
    public void testRedirectByUrl() throws Exception {
        // Act
        ToolPageResult result = testDiyPageHandler.redirectByUrl("https://test.com");
        
        // Assert
        assertNotNull("PageServiceResult should not be null", result);
        assertEquals("status should be FOUND (302)",
            org.springframework.http.HttpStatus.FOUND.value(),
            result.getStatusCode());
        assertEquals("redirect URL should match", "https://test.com", result.getRedirectUrl());
    }

    /**
     * 测试 redirectDefaultPage 方法：查询字符串为空。
     */
    @Test
    public void testRedirectDefaultPage_EmptyQueryString() throws Exception {
        // Arrange
        when(request.getQueryString()).thenReturn(null);
        String expectedRedirectUrl = "http://default.com/mydesigns"; // 假设的默认URL
        lenient().when(toadProperties.getMyDesignUrl()).thenReturn(expectedRedirectUrl);
        
        // Act
        ToolPageResult result = testDiyPageHandler.redirectDefaultPage(request);
        
        // Assert
        assertNotNull("PageServiceResult should not be null", result);
        assertEquals("status should be FOUND (302)",
            org.springframework.http.HttpStatus.FOUND.value(),
            result.getStatusCode());
        assertEquals("Redirect URL should be the default my design URL", 
            expectedRedirectUrl, 
            result.getRedirectUrl());
    }
}

class TestDiyPageHandler extends AbstractDiyPageHandler {
    public TestDiyPageHandler(UserInfoFacade userInfoFacade, BusinessAccountDb businessAccountDb, BusinessAccessService businessAccessService, ProjectDesignFacadeDb projectDesignFacadeDb, VrcAppIdDataDb vrcAppIdDataDb, SessionConflictHandler sessionConflictHandler, GrayLunchService grayLunchService, ToadProperties toadProperties, UserDb userDb, SaaSConfigService saaSConfigService, ToolLinkConfigCache toolLinkConfigCache, SaasConfigFacade saasConfigFacade, AuthCheckService authCheckService) {
        super(userInfoFacade, businessAccountDb, businessAccessService, projectDesignFacadeDb, vrcAppIdDataDb, sessionConflictHandler, grayLunchService, toadProperties, userDb, saaSConfigService, toolLinkConfigCache, saasConfigFacade, authCheckService);
    }

    @Override
    protected ToolPageResult handleExistingDesign(HttpServletRequest request, HttpServletResponse response,
            DiyPageParam param, String actualAppId, Long appIdNum, Long rootAccountId, boolean bimAccess) {
        return null;
    }

    @Override
    protected ToolPageResult handleNewDesign(HttpServletRequest request, HttpServletResponse response, DiyPageParam param, String actualAppId, Long appIdNum, Long rootAccountId, boolean bimAccess) {
        return null;
    }
}