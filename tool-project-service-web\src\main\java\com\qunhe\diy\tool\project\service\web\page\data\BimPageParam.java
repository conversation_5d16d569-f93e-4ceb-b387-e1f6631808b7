/*
 * KamPageParam.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Getter
@Setter
@Builder
public class BimPageParam {

    private String obsDesignId;

    private String redirectUrl;

    private String levelId;

    private String obsSrcPlanId;

    private String planName;

    private String askId;

    private Byte planType;

    private Integer stage;

    private Long userId;

    private boolean trialUser;

    private String obsAppId;

    private boolean fromCoohom;

    /**
     * coohom 协作
     */
    private boolean cooperate;

    private Long orderDesignId;
}
