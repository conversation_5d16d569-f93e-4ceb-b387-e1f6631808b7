/*
 * CommunityDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.CommunityMapper;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.common.param.CommunityInfo;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import com.qunhe.log.QHLogger;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.CognitiveComplexity"})
public class CommunityDb {
    private static final long DEFAULT_AERA_ID = 175L;
    private static final Integer SYS_DICT_LEVEL_PROVINCE = 0;
    private static final Integer SYS_DICT_LEVEL_CITY = 1;

    private static QHLogger LOG = QHLogger.getLogger(CommunityDb.class);

    private final CommunityMapper communityMapper;
    private final SysDictAreaMapper sysDictAreaMapper;

    public CommunityDb(final CommunityMapper communityMapper,
            final SysDictAreaMapper sysDictAreaMapper) {
        this.communityMapper = communityMapper;
        this.sysDictAreaMapper = sysDictAreaMapper;
    }

    public CommunityInfo getCommFullInfos(final String name, final int areaId) {
        return communityMapper.getCommFullInfos(name, areaId);
    }

    public Long getAreaId(final String province, final String city) {
        if (city == null && province == null) {
            return DEFAULT_AERA_ID;
        } else if (city == null && province != null) {
            final List<SysDictArea> provinces = sysDictAreaMapper.getByName(province, SYS_DICT_LEVEL_PROVINCE);
            if (!provinces.isEmpty()) {
                final Long provinceId = provinces.get(0).getAreaId();
                final List<SysDictArea> cities = sysDictAreaMapper.getChildren(provinceId);
                return !cities.isEmpty() ? cities.get(0).getAreaId() : provinceId;
            }
        } else if (city != null) {
            final List<SysDictArea> cities = sysDictAreaMapper.getByName(city, SYS_DICT_LEVEL_CITY);
            if (cities.isEmpty()) {
                return getAreaId(province, null);
            }

            if (province == null) {
                return cities.get(0).getAreaId();
            }
            for (final SysDictArea sysDictAreaCity : cities) {
                final SysDictArea sysDictAreaProvince =
                        sysDictAreaMapper.getById(sysDictAreaCity.getParentAreaId());
                if (sysDictAreaProvince.getName().contains(province)) {
                    return sysDictAreaCity.getAreaId();
                }
            }
        }
        return DEFAULT_AERA_ID;
    }

    @SentinelResource(value = "getOrCreateCommunity", fallback = "getOrCreateCommunityFallBack")
    public Community getOrCreateCommunity(Long areaId, String communityName) {
        if (areaId == null) {
            return null;
        }

        final Community community;
        if (communityName == null) {
            community = getDefaultCommunity(areaId);
        } else {
            final Community[] communities = getCommunities(communityName, areaId);
            if (communities.length > 0) {
                community = communities[0];
            } else {
                community = new Community();
                community.setName(communityName);
                community.setAreaId(areaId);
                community.setChecked(0);
                communityMapper.insertCommunity(community);
            }
        }
        return community;
    }

    public Community getOrCreateCommunityFallBack(Long areaId, String communityName, Throwable e) {
        LOG.message("getOrCreateCommunity - FallBack", e)
                .with("areaId", areaId)
                .with("communityName", communityName)
                .error();
        return getDefaultCommunity(DEFAULT_AERA_ID);
    }

    public Community getDefaultCommunity(final Long areaId) {
        return communityMapper.getDefaultCommunityByAreaId(areaId);
    }

    public Community[] getCommunities(final String name, final Long areaId) {
        return communityMapper.getCommunitiesByNameAndAreaId(name, areaId);
    }

    public String getCommNameByCommId(final Long commId) {
        return communityMapper.getNameById(commId);
    }

    public SysDictArea getSysDictArea(final Long commId) {
        return communityMapper.getAreaByCommId(commId);
    }
}
