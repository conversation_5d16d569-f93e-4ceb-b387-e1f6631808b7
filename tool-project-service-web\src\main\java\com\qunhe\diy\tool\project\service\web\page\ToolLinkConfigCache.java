/*
 * ToolLinkConfigCache.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfig;
import com.qunhe.instdeco.plan.generalapi.client.BusinessConfigClient;
import com.qunhe.utils.HunterLogger;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 *
 */
@Service
public class ToolLinkConfigCache {
    private static final HunterLogger LOG = HunterLogger.getLogger(ToolLinkConfigCache.class);

    private static final int CACHE_DURATION = 30;

    private final Supplier<BusinessConfig> businessConfigSupplier;

    public ToolLinkConfigCache(final BusinessConfigClient businessConfigClient) {
        businessConfigSupplier = Suppliers.memoizeWithExpiration(
                () -> {
                    try {
                        final BusinessConfig businessConfig = businessConfigClient
                                .getDefaultToolLinkConfig();
                        if (businessConfig == null) {
                            LOG.message(
                                            "ToolLinkConfigCache - could not get default tool " +
                                                    "link config")
                                    .warn();
                            return null;
                        }
                        LOG.message("ToolLinkConfigCache - get default tool link config")
                                .info();
                        return businessConfig;
                    } catch (final Exception e) {
                        LOG.message("ToolLinkConfigCache", e)
                                .warn();
                        return null;
                    }
                },
                CACHE_DURATION,
                TimeUnit.MINUTES
        );
    }

    public BusinessConfig getDefaultConfig() {
        return businessConfigSupplier.get();
    }
}
