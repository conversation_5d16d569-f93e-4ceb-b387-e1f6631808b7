
qunhe:
  container:
    type: jetty # jetty or undertow
  service:
    autoRegister: false
    vip: com.qunhe.instdeco.service.tool-be.tool-project-service
    vip-list:
      - com.qunhe.instdeco.picservice
      - com.qunhe.instdeco.service.diy.diymanage
      - com.qunhe.instdeco.service.web-be.floor-plan-cool
      - com.qunhe.instdeco.service.yuncore
      - com.qunhe.instdeco.service.uic
      - com.qunhe.instdeco.service.web-be.saas.arc-authenticator
      - com.qunhe.instdeco.service.web-be.project-auth
      - com.qunhe.instdeco.service.tool-be.synergy-service
      - com.qunhe.instdeco.service.tool-be.project-public-service
      - com.qunhe.instdeco.service.web-be.project-search
      - com.qunhe.instdeco.service.rcsservice
      - com.qunhe.instdeco.service.web-be.design-excellent
      - com.qunhe.instdeco.service.openapi
      - com.qunhe.instdeco.service.libra
      - com.qunhe.instdeco.service.web-be.security-detect-service
      - com.qunhe.instdeco.service.tool-be.layoutdesignservice
      - com.qunhe.instdeco.service.tool-be.designinfoservice
      - com.qunhe.instdeco.service.usercenter
      - com.qunhe.instdeco.service.growth
      - com.qunhe.exabrain.bigdata.userinfo
      - com.qunhe.instdeco.service.diy.diyservice
      - com.qunhe.instdeco.service.web-be.saas-design
      - com.qunhe.instdeco.service.tool-be.dcs-order
      - com.qunhe.instdeco.service.general
      - com.qunhe.instdeco.service.web-be.saas-business-account
      - com.qunhe.instdeco.service.diy.designservice
    basePackage: com.qunhe.mdw.security.detect.service.client.api,com.qunhe.custom.dcs.order.client.apis
  has:
    hasId: kujiale_tool-be_diy_tool-project-service
  switch:
    id: kujiale_tool-be_diy_tool-project-service
  content-length:
    filter:
      enable: true


mybatis:
  config-location: classpath:mybatis-config.xml

i18n:
  resource:
    vipasname: true

meter-thread:
  # 采集时间间隔，默认 30s
  delay: 30
  # 是否进行指标采集，默认 true
  metrics: true
  # 使用 TransmittableThreadLocal 修饰，配置为 true 时，与下面那条配置效果一样
  ttl: false
  # 是否开启日志，默认关闭
  log: true
  # 线程池配置
  executors:
    # 线程名称
    #业务场景：移交方案
    handoverExecutor:
      # 线程池类型，thread、task、scheduled
      thread:
        # 线程池类型，默认 ThreadPoolExecutor
        type: ThreadPoolExecutor
        # 参数 spec，必填
        spec: corePoolSize=5,maximumPoolSize=20,keepAliveTime=10m,queueSize=500
        # 拒绝策略，不设置默认为线程池 CallerRunsPolicy， 1.1.7 后为必填项
        handler: java.util.concurrent.ThreadPoolExecutor.AbortPolicy
        # 自定义装饰器, 继承 MeterTaskDecorate 实现自定义功能
        decorate:
    #业务场景：方案创建时消息通知
    createExecutor:
      thread:
        type: ThreadPoolExecutor
        spec: corePoolSize=5,maximumPoolSize=20,keepAliveTime=10m,queueSize=500
        handler: java.util.concurrent.ThreadPoolExecutor.DiscardPolicy
        decorate:
    #业务场景：热点数据刷写
    downgradingExecutor:
      thread:
        type: ThreadPoolExecutor
        spec: corePoolSize=5,maximumPoolSize=20,keepAliveTime=10m,queueSize=500
        handler: java.util.concurrent.ThreadPoolExecutor.DiscardPolicy
        decorate:
    #业务场景：方案批量彻底删除和回滚
    batchOperationExecutor:
      thread:
        type: ThreadPoolExecutor
        spec: corePoolSize=5,maximumPoolSize=20,keepAliveTime=10m,queueSize=500
        handler: java.util.concurrent.ThreadPoolExecutor.DiscardPolicy
        decorate:
    #业务场景：保存多层历史版本
    multiLevelRevisionExecutor:
      thread:
        type: ThreadPoolExecutor
        spec: corePoolSize=5,maximumPoolSize=20,keepAliveTime=10m,queueSize=500
        handler: java.util.concurrent.ThreadPoolExecutor.DiscardPolicy
        decorate:

# application.yml
bucket4j:
  # redis 地址, redis://{redisId}/{database}，必填
  redis-uri: redis://diystore-dev/1
  # 非幂等限流规则，默认1分钟1次
  idempotence-check:
    token: 1 # 请求次数
    periodSeconds: 60 #间隔秒数


summer-cache:
  log-interval-in-millis: 20000
  prefer-serializer: jackson
  redis-namespace:
    prefix: "kam:cache:dev:"
    prefix-with-version: false
  redis-global:
    allow-null-values: false
    disturb-ratio: 0.1
  redis:
    default:
      # 接入文档http://coops.gitlabpages.qunhequnhe.com/redis-doc/docs/client/spore2/summer-cache/
      redisId: diystore-dev # https://coops.qunhequnhe.com/rops/#/
      database: 1
      pool:
        max-total: 100
  caches:
    diyDesignInfoV2_designId:
      redis:
        prefix: "diyDesignInfoV2:designId:"
        expiration: 60
        keyClass: java.lang.Long
        valueClass: com.qunhe.diybe.dms.data.DiyDesignInfo
    allVrcAppIdList:
      redis:
        prefix: "allVrcAppIdList:"
        expiration: 3600
        keyClass: java.lang.String
        valueClass: com.qunhe.diy.tool.project.service.biz.cache.VrcAppIdCacheData



---

spring:
  profiles: prod_test

bucket4j:
  redis-uri: redis://diy-cache-prod/3


summer-cache:
  redis-namespace:
    prefix: "kam:cache:prod:"
  redis:
    default:
      redisId: kam-cache-prod
      database: 3
      pool:
        max-total: 100

---

spring:
  profiles: prod

bucket4j:
  redis-uri: redis://diy-cache-prod/4

summer-cache:
  redis-namespace:
    prefix: "kam:cache:prod:"
  redis:
    default:
      redisId: kam-cache-prod
      database: 3
      pool:
        max-total: 100