/*
 * DowngradingService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.project.service.client.client.DowngradingClient;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;

/**
 * Function: 降级策略
 *
 * <AUTHOR>
 */
@Service
public class DowngradingService {

    private static final QHLogger LOG = QHLogger.getLogger(ProjectNotifyService.class);

    @Resource
    private DowngradingClient downgradingClient;

    public void refreshSearchData(List<Object> list) {
        try {
            downgradingClient.refresh(list);
        } catch (Exception e) {
            //异常处理
            LOG.message("新增热点数据发送失败。").warn();
        }
    }

    @Async("downgradingExecutor")
    public void addHotData(ToolProjectSaveParam toolProjectSaveParam,
            final ToolProjectHomeDesign toolProjectHomeDesign, Long userId) {
        try {
            //因为这个数据是降级使用增加热点数据所以这里做异步。
            ProjectDesign messageDesign = new ProjectDesign();
            messageDesign.setDesignId(toolProjectHomeDesign.getDesignId());
            messageDesign.setUserId(userId);
            messageDesign.setPlanId(toolProjectHomeDesign.getPlanId());
            if (!CollectionUtils.isEmpty(toolProjectHomeDesign.getLevels())) {
                messageDesign.setModelDataId(
                        toolProjectHomeDesign.getLevels().get(0).getLevelId());
            }
            messageDesign.setDesignName(toolProjectSaveParam.getName());
            this.refreshSearchData(Arrays.asList(messageDesign));
        } catch (Exception e) {
            //刷写热点数据失败场景
            LOG.message("refresh hot data fail", e).warn();
        }
    }

}
