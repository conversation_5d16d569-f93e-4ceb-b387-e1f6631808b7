package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.diy.tool.project.service.web.config.WebConfig;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipData;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipType;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.i18n.locale.context.FileMessageSource;
import com.qunhe.i18n.locale.helper.LocaleHelper;
import com.qunhe.monitor.faros.api.Histogram;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Locale;

import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.APP_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.ASK_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.COOP;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.DESIGN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.FORCE_UPDATE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.LEVEL_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.OBS_SER_PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_TYPE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_BIM;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_FINISH;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_URL;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.STAGE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.STAY_DIY;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.TOOL_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.TRE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@WebMvcTest(ToolPageController.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = {
        WebConfig.class,
        ToolPageController.class,
})
public class ToolPageControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BimToolPageService bimToolPageService;

    @MockBean
    private DiyToolPageService diyToolPageService;

    @MockBean
    private TrialUserFacade trialUserFacade;

    @MockBean
    private VrcService vrcService;

    @MockBean
    private FileMessageSource fileMessageSource;

    @MockBean
    private ErrorTipService errorTipService;

    // Mocks for static methods
    private MockedStatic<UserDb> mockedUserDb;
    private MockedStatic<LocaleHelper> mockedLocaleHelper;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private Histogram mockHistogram;


    private final Long DEFAULT_USER_ID = 12345L;
    private final String CUSTOM_VERIFY_PARAM = "obsorderdesignid";
    private final Long CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED = 67890L;
    private final String CUSTOM_VERIFY_PARAM_VALUE_ENCRYPTED = LongCipher.DEFAULT.encrypt(CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED);

    @Before
    public void setUp() throws AccessAuthenticatorException, YunDesignException, IOException {
        MockitoAnnotations.openMocks(this);

        mockedUserDb = Mockito.mockStatic(UserDb.class);
        mockedUserDb.when(() -> UserDb.getUserIdBySession(anyBoolean())).thenReturn(
                DEFAULT_USER_ID);

        mockedLocaleHelper = Mockito.mockStatic(LocaleHelper.class);
        when(LocaleHelper.getLocale()).thenReturn(Locale.SIMPLIFIED_CHINESE);
    }

    @After
    public void tearDown() {
        mockedUserDb.close();
        mockedLocaleHelper.close();
    }

    /**
     * 测试获取酷家乐BIM工具页面信息 - 试用用户场景 - 带自定义验证参数
     * 此测试用例覆盖试用用户访问，并且URL中包含自定义验证参数的情况。
     */
    @Test
    public void testGetKujialeBimToolPageInfo_TrialUser_WithCustomVerifyParam() throws Exception {
        // 中文注释：模拟trialUserFacade返回true，表示当前是试用用户
        when(trialUserFacade.isTrialUser()).thenReturn(true);
        ToolPageResult expectedServiceResult = ToolPageResult.success("Mocked Trial Response");
        // 中文注释：模拟bimToolPageService的handlePageForTrial方法行为
        when(bimToolPageService.handlePageForTrial(any(HttpServletRequest.class),
                any(BimPageParam.class)))
                .thenReturn(expectedServiceResult);

        // 中文注释：构建请求
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/bim/kujiale")
                .param(DESIGN_ID, "testDesignId")
                .param(CUSTOM_VERIFY_PARAM, CUSTOM_VERIFY_PARAM_VALUE_ENCRYPTED);

        // 中文注释：执行请求并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("Mocked Trial Response"));

        // 中文注释：验证UserDb.getUserIdBySession被调用
        mockedUserDb.verify(() -> UserDb.getUserIdBySession(true));
        // 中文注释：验证trialUserFacade.isTrialUser被调用
        verify(trialUserFacade).isTrialUser();

        ArgumentCaptor<BimPageParam> captor = ArgumentCaptor.forClass(BimPageParam.class);
        // 中文注释：验证bimToolPageService.handlePageForTrial被调用，并捕获参数
        verify(bimToolPageService).handlePageForTrial(any(HttpServletRequest.class),
                captor.capture());
        // 中文注释：验证捕获到的BimPageParam中的orderDesignId是否正确
        assertEquals("捕获到的orderDesignId应为解密后的值", CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED,
                captor.getValue().getOrderDesignId());
        // 中文注释：验证捕获到的BimPageParam中的userId是否正确
        assertEquals("捕获到的userId应为默认用户ID", DEFAULT_USER_ID,
                captor.getValue().getUserId());
        // 中文注释：验证捕获到的BimPageParam中的trialUser是否为true
        assertTrue("捕获到的trialUser应为true", captor.getValue().isTrialUser());
    }

    /**
     * 测试获取酷家乐BIM工具页面信息 - 试用用户场景 - 无自定义验证参数
     * 此测试用例覆盖试用用户访问，但URL中不包含自定义验证参数的情况。
     */
    @Test
    public void testGetKujialeBimToolPageInfo_TrialUser_NoCustomVerifyParam() throws Exception {
        // 中文注释：模拟trialUserFacade返回true
        when(trialUserFacade.isTrialUser()).thenReturn(true);
        ToolPageResult expectedServiceResult = ToolPageResult.success("Trial No Custom Param");
        when(bimToolPageService.handlePageForTrial(any(HttpServletRequest.class),
                any(BimPageParam.class)))
                .thenReturn(expectedServiceResult);

        // 中文注释：构建请求，不含CUSTOM_VERIFY_PARAM
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/bim/kujiale")
                .param(DESIGN_ID, "testDesignId");

        // 中文注释：执行并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("Trial No Custom Param"));

        ArgumentCaptor<BimPageParam> captor = ArgumentCaptor.forClass(BimPageParam.class);
        // 中文注释：验证bimToolPageService.handlePageForTrial被调用
        verify(bimToolPageService).handlePageForTrial(any(HttpServletRequest.class),
                captor.capture());
        // 中文注释：验证捕获到的BimPageParam中的orderDesignId为null
        assertNull("orderDesignId应为null", captor.getValue().getOrderDesignId());
    }

    /**
     * 测试获取酷家乐BIM工具页面信息 - 正式用户场景
     * 此测试用例覆盖正式用户访问的情况。
     */
    @Test
    public void testGetKujialeBimToolPageInfo_NonTrialUser() throws Exception {
        // 中文注释：模拟trialUserFacade返回false，表示当前是正式用户
        when(trialUserFacade.isTrialUser()).thenReturn(false);
        ToolPageResult expectedServiceResult = ToolPageResult.success("Mocked Non-Trial Response");
        // 中文注释：模拟bimToolPageService的handlePageForBim方法行为
        when(bimToolPageService.handlePageForBim(any(HttpServletRequest.class),
                any(BimPageParam.class)))
                .thenReturn(expectedServiceResult);

        // 中文注释：构建请求
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/bim/kujiale")
                .param(DESIGN_ID, "testDesignId")
                .param(REDIRECT_URL, "http://example.com/redirect")
                .param(LEVEL_ID, "level1")
                .param(OBS_SER_PLAN_ID, "srcPlan1")
                .param(PLAN_NAME, "My Plan")
                .param(ASK_ID, "ask123")
                .param(PLAN_TYPE, "1")
                .param(STAGE, "2")
                .param(COOP, "true")
                .param(APP_ID, "appXYZ")
                .param(REDIRECT_FINISH, "true") // redirectFinish is specific to kujialeBim
                .param(CUSTOM_VERIFY_PARAM, CUSTOM_VERIFY_PARAM_VALUE_ENCRYPTED);

        // 中文注释：执行请求并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("Mocked Non-Trial Response"));

        // 中文注释：验证trialUserFacade.isTrialUser被调用
        verify(trialUserFacade).isTrialUser();
        ArgumentCaptor<BimPageParam> captor = ArgumentCaptor.forClass(BimPageParam.class);
        // 中文注释：验证bimToolPageService.handlePageForBim被调用
        verify(bimToolPageService).handlePageForBim(any(HttpServletRequest.class),
                captor.capture());
        BimPageParam capturedParam = captor.getValue();
        // 中文注释：验证捕获到的BimPageParam中的各项参数是否正确设置
        assertEquals("obsDesignId不匹配", "testDesignId", capturedParam.getObsDesignId());
        assertEquals("redirectUrl不匹配", "http://example.com/redirect",
                capturedParam.getRedirectUrl());
        assertEquals("levelId不匹配", "level1", capturedParam.getLevelId());
        // ... (其他参数断言)
        assertEquals("orderDesignId不匹配", CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED,
                capturedParam.getOrderDesignId());
        assertEquals("userId不匹配", DEFAULT_USER_ID, capturedParam.getUserId());
        assertFalse("trialUser应为false", capturedParam.isTrialUser());
        assertFalse("fromCoohom应为false", capturedParam.isFromCoohom()); // Kujiale endpoint

    }



    /**
     * 测试获取Coohom BIM工具页面信息 - 正式用户场景
     */
    @Test
    public void testGetCoohomBimToolPageInfo_NonTrialUser() throws Exception {
        // 中文注释：模拟正式用户
        when(trialUserFacade.isTrialUser()).thenReturn(false);
        ToolPageResult expectedServiceResult = ToolPageResult.success("Coohom Non-Trial");
        when(bimToolPageService.handlePageForBim(any(HttpServletRequest.class),
                any(BimPageParam.class)))
                .thenReturn(expectedServiceResult);

        // 中文注释：构建请求
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/bim/coohom")
                .param(DESIGN_ID, "coohomDesign2");

        // 中文注释：执行并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("Coohom Non-Trial"));

        ArgumentCaptor<BimPageParam> captor = ArgumentCaptor.forClass(BimPageParam.class);
        // 中文注释：验证bimToolPageService.handlePageForBim被调用
        verify(bimToolPageService).handlePageForBim(any(HttpServletRequest.class),
                captor.capture());
        BimPageParam captured = captor.getValue();
        // 中文注释：验证fromCoohom参数在BimPageParam中为true
        assertEquals("fromCoohom应为true", true, captured.isFromCoohom());
    }


    /**
     * 测试获取VRC应用配置 - 无可用应用
     * 当vrcService返回空列表时，期望返回空Map。
     */
    @Test
    public void testGetVrcAppConfig_NoApps() throws Exception {
        // 中文注释：模拟vrcService返回空列表
        when(vrcService.getAvailableVrcAppIdList()).thenReturn(Collections.emptyList());

        // 中文注释：执行请求并验证返回空JSON对象
        mockMvc.perform(MockMvcRequestBuilders.get("/tps/api/vrc_app_config"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().json("{}"));

        // 中文注释：验证vrcService.getAvailableVrcAppIdList被调用
        verify(vrcService).getAvailableVrcAppIdList();
    }

    /**
     * 测试获取VRC应用配置 - 返回多个应用配置
     * 覆盖VrcAppIdData到VrcAppIdDataVO的转换逻辑，包括区域处理和加密。
     */
    @Test
    public void testGetVrcAppConfig_WithMultipleApps() throws Exception {
        VrcAppIdData app1 = new VrcAppIdData();
        app1.setAppKey("key1");
        app1.setAppName("App One");
        app1.setAppId(101);
        app1.setVrc("vrc1");
        app1.setAccessPoint(201);
        app1.setRegion("US, EU");

        VrcAppIdData app2 = new VrcAppIdData();
        app2.setAppKey("key2");
        app2.setAppName("App Two");
        app2.setAppId(102);
        app2.setVrc("vrc2");
        app2.setRegion("ASIA");


        VrcAppIdData app3WithNullKey = new VrcAppIdData();
        app3WithNullKey.setAppName("App Three Invalid Key"); // Key is null, should be filtered

        VrcAppIdData app4SkipAP = new VrcAppIdData(); // This app will have skipAPCheck = true
        app4SkipAP.setAppKey("key4");
        app4SkipAP.setAppName("App Four Skip AP");
        app4SkipAP.setAppId(104);
        app4SkipAP.setVrc("vrc4");
        // No AccessPoint set for app4, or vrcService.skipAPCheck(app4) will be true

        // 中文注释：模拟vrcService返回应用列表
        when(vrcService.getAvailableVrcAppIdList()).thenReturn(
                Arrays.asList(app1, app2, app3WithNullKey, app4SkipAP));
        // 中文注释：模拟skipAPCheck行为
        when(vrcService.skipAPCheck(app1)).thenReturn(false); // app1 has AccessPoint
        when(vrcService.skipAPCheck(app2)).thenReturn(
                true);  // app2 will skip AP (e.g. AccessPoint is 0 or some other condition)
        when(vrcService.skipAPCheck(app4SkipAP)).thenReturn(true);


        // 中文注释：执行请求
        mockMvc.perform(MockMvcRequestBuilders.get("/tps/api/vrc_app_config"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1").exists())
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1.name").value("App One"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1.appIdNum").value(101))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1.appId").value("3FO4K4VY1Q5P"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1.vrc").value("vrc1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key1.saasAP")
                        .value(201)) // app1 has saasAP
                .andExpect(MockMvcResultMatchers.jsonPath("$.key2").exists())
                .andExpect(MockMvcResultMatchers.jsonPath("$.key2.name").value("App Two"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key2.appId").value("3FO4K4VY1G4M"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.key2.saasAP")
                        .doesNotExist()); // app4 skips saasAP

        // 中文注释：验证vrcService.getAvailableVrcAppIdList被调用
        verify(vrcService).getAvailableVrcAppIdList();
    }

    /**
     * 测试获取VRC应用配置 - 单条解析失败不影响其他
     * 模拟其中一个VrcAppIdData在转换时（例如LongCipher加密时）抛出异常。
     */
    @Test
    public void testGetVrcAppConfig_SingleItemParseFailure() throws Exception {
        VrcAppIdData appGood = new VrcAppIdData();
        appGood.setAppKey("goodApp");
        appGood.setAppName("Good Application");
        appGood.setAppId(201);
        appGood.setVrc("vrc_good");

        VrcAppIdData appBadEncrypt = new VrcAppIdData();
        appBadEncrypt.setAppKey("badEncryptApp");
        appBadEncrypt.setAppName("Bad Encrypt App");
        appBadEncrypt.setAppId(202);
        appBadEncrypt.setVrc("vrc_bad_encrypt");

        // 中文注释：模拟vrcService返回应用列表
        when(vrcService.getAvailableVrcAppIdList()).thenReturn(
                Arrays.asList(appGood, appBadEncrypt));
        // 中文注释：简化，假设所有app都跳过AP检查或有AP
        when(vrcService.skipAPCheck(any(VrcAppIdData.class))).thenReturn(false);


        // 中文注释：执行请求
        mockMvc.perform(MockMvcRequestBuilders.get("/tps/api/vrc_app_config"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.goodApp").exists()) // goodApp应存在
                .andExpect(MockMvcResultMatchers.jsonPath("$.goodApp.appId").value("3FO4K4VYI69M")); // badEncryptApp因异常被过滤

    }


    /**
     * 测试获取页面加载错误提示
     * 验证ErrorTipService和FileMessageSource的调用及结果转换。
     */
    @Test
    public void testGetPageLoadingErrorTip() throws Exception {
        String errorId = "someErrorId";
        ErrorTipData mockErrorTipData = new ErrorTipData();
        // 中文注释：创建一个 mock 的 ErrorTipType，因为枚举的实际值可能不可用或复杂
        ErrorTipType mockTipType = mock(ErrorTipType.class);
        when(mockTipType.getTitleLocaleId()).thenReturn("title.id");
        when(mockTipType.getDescLocaleId()).thenReturn("desc.id");
        when(mockTipType.getTitleFallbackMessage()).thenReturn("Default Title");
        when(mockTipType.getDescFallbackMessage()).thenReturn("Default Description");

        mockErrorTipData.setErrorTipType(mockTipType);
        mockErrorTipData.setTitleArgs(new String[]{ "arg1" });
        mockErrorTipData.setDescArgs(new String[]{ "arg2" });

        String expectedTitle = "错误标题";
        String expectedDesc = "错误描述详情";

        // 中文注释：模拟errorTipService.get返回数据
        when(errorTipService.get(errorId)).thenReturn(mockErrorTipData);
        // 中文注释：模拟fileMessageSource获取国际化消息
        when(fileMessageSource.getMessageWithFallback(
                eq("title.id"),
                eq(mockErrorTipData.getTitleArgs()),
                eq("Default Title"),
                any(Locale.class)))
                .thenReturn(expectedTitle);
        when(fileMessageSource.getMessageWithFallback(
                eq("desc.id"),
                eq(mockErrorTipData.getDescArgs()),
                eq("Default Description"),
                any(Locale.class)))
                .thenReturn(expectedDesc);

        // 中文注释：执行请求并验证
        mockMvc.perform(MockMvcRequestBuilders.get("/tps/api/load/error-tip").param("id", errorId))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.c").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.d.title").value(expectedTitle))
                .andExpect(MockMvcResultMatchers.jsonPath("$.d.desc").value(expectedDesc));

        // 中文注释：验证errorTipService.get被调用
        verify(errorTipService).get(errorId);
        // 中文注释：验证LocaleHelper.getLocale被调用两次 (标题和描述各一次)
        mockedLocaleHelper.verify(LocaleHelper::getLocale, times(2));
    }


    /**
     * 测试获取酷家乐DIY工具页面信息 - 包含所有参数
     * 验证diyToolPageService.handlePage被正确调用，且DiyPageParam构建正确。
     */
    @Test
    public void testGetKujialeDiyToolPageInfo_AllParams() throws Exception {
        ToolPageResult serviceResult = ToolPageResult.builder()
                                                .statusCode(HttpStatus.FOUND.value())
                                                .body("DIY Kujiale Page")
                                                .build();
        // 中文注释：模拟diyToolPageService.handlePage方法
        when(diyToolPageService.handlePage(any(HttpServletRequest.class), any(),
                any(DiyPageParam.class)))
                .thenReturn(serviceResult);

        // 中文注释：构建请求，包含所有可能的参数
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/diy/kujiale")
                .param(DESIGN_ID, "diyDesignId")
                .param(PLAN_ID, "diyPlanId")
                .param(REDIRECT_URL, "http://example.com/diy-redirect")
                .param(LEVEL_ID, "diyLevel")
                .param(OBS_SER_PLAN_ID, "diySrcPlan")
                .param(PLAN_NAME, "My DIY Plan")
                .param(ASK_ID, "diyAsk")
                .param(PLAN_TYPE, "2")
                .param(TOOL_NAME, "advancedDiy")
                .param(STAY_DIY, "true")
                .param(STAGE, "3")
                .param(REDIRECT_BIM, "false")
                .param(COOP, "true")
                .param(APP_ID, "diyAppId")
                .param(FORCE_UPDATE, "true")
                .param(TRE, "feature1", "feature2")
                .param(CUSTOM_VERIFY_PARAM, CUSTOM_VERIFY_PARAM_VALUE_ENCRYPTED);

        // 中文注释：执行并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isFound())
                .andExpect(MockMvcResultMatchers.content().string("DIY Kujiale Page"));

        // 中文注释：验证UserDb.getUserIdBySession被调用
        mockedUserDb.verify(() -> UserDb.getUserIdBySession(true));

        ArgumentCaptor<DiyPageParam> captor = ArgumentCaptor.forClass(DiyPageParam.class);
        // 中文注释：捕获传递给diyToolPageService.handlePage的DiyPageParam
        verify(diyToolPageService).handlePage(any(HttpServletRequest.class), any(),
                captor.capture());
        DiyPageParam capturedParam = captor.getValue();

        // 中文注释：断言DiyPageParam中的各个字段是否按预期设置
        assertEquals("obsDesignId不匹配", "diyDesignId", capturedParam.getObsDesignId());
        assertEquals("obsPlanId不匹配", "diyPlanId", capturedParam.getObsPlanId());
        // ... (其他参数断言)
        assertFalse("fromCoohom应为false", capturedParam.isFromCoohom());
        assertEquals("orderDesignId不匹配", CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED,
                capturedParam.getOrderDesignId());
        assertNotNull("tre列表不应为null", capturedParam.getTre());
        assertEquals("tre列表大小不匹配", 2, capturedParam.getTre().size());
    }



    /**
     * 测试获取Coohom DIY工具页面信息
     * 与Kujiale DIY类似，但针对Coohom的端点，且DiyPageParam中的fromCoohom应为true。
     */
    @Test
    public void testGetCoohomDiyToolPageInfo() throws Exception {
        ToolPageResult serviceResult = ToolPageResult.success("DIY Coohom Page");
        // 中文注释：模拟diyToolPageService.handlePage方法
        when(diyToolPageService.handlePage(any(HttpServletRequest.class), any(),
                any(DiyPageParam.class)))
                .thenReturn(serviceResult);

        // 中文注释：构建请求
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get("/tps/api/page/tool/diy/coohom")
                .param(DESIGN_ID, "coohomDiyDesign")
                .param(CUSTOM_VERIFY_PARAM, CUSTOM_VERIFY_PARAM_VALUE_ENCRYPTED);


        // 中文注释：执行并验证
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().string("DIY Coohom Page"));

        ArgumentCaptor<DiyPageParam> captor = ArgumentCaptor.forClass(DiyPageParam.class);
        // 中文注释：捕获参数
        verify(diyToolPageService).handlePage(any(HttpServletRequest.class), any(),
                captor.capture());
        DiyPageParam capturedParam = captor.getValue();

        // 中文注释：验证fromCoohom在DiyPageParam中为true
        assertTrue("fromCoohom应为true", capturedParam.isFromCoohom());
        // 中文注释：验证orderDesignId
        assertEquals("orderDesignId不匹配", CUSTOM_VERIFY_PARAM_VALUE_DECRYPTED,
                capturedParam.getOrderDesignId());
        // 中文注释：验证userId
        assertEquals("userId不匹配", DEFAULT_USER_ID, capturedParam.getUserId());
    }
}