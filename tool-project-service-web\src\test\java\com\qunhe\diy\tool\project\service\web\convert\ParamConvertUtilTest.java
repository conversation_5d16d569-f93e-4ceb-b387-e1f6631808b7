/*
 * ParamConvertUtilTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.convert;

import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.request.ToolProjectSaveRequest;
import com.qunhe.utils.apiencrypt2.cipher.LongCipher;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;


public class ParamConvertUtilTest {

    @Test
    public void testConvert() {
        ToolProjectSaveRequest request = new ToolProjectSaveRequest();
        request.setSourceArea(111d);
        request.setObsUserId(LongCipher.DEFAULT.encrypt(2L));
        request.setObsCommId(LongCipher.DEFAULT.encrypt(2L));

        ToolProjectSaveParam param = ParamConvertUtil.convert(request);
        assertThat(param.getSrcArea()).isEqualTo(request.getSourceArea());

        assertThat(param.getUserId()).isEqualTo(2L);
        assertThat(param.getCommId()).isEqualTo(2L);
    }
}
