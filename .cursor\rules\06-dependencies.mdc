---
description:
globs:
alwaysApply: false
---
# 项目依赖说明

## 主要依赖

### 内部依赖
- [pom.xml](mdc:pom.xml) - 项目主 POM 文件
- com.qunhe.middleware:pilot-boot-dependencies - 基础依赖管理
- com.qunhe.hunter:hunter-core - 内部服务框架
- com.qunhe.render:renderengine - 渲染引擎
- com.qunhe.diybe.module:restapi-core - REST API 核心库
- com.qunhe.instdeco.diy:drsapi - 工具 API
- com.qunhe.instdeco.diy:diyrenderclient - 渲染客户端
- com.qunhe.project-platform:project-search-client - 方案中台搜索客户端
- com.qunhe.middleplatform:project-management-client - 方案中台管理客户端

### 工具依赖
- com.qunhe.middleware:toad-client - 配置中心客户端
- com.qunhe.instdeco.diy:diyutils - 工具类库
- com.qunhe.instdeco:interceptors - 拦截器
- com.qunhe.usergrowth:uic-rpc - 用户信息客户端
- com.qunhe.diybe:diymanage-client - DIY 管理客户端

## 依赖版本管理
项目使用 Maven 属性管理依赖版本：

- java.version: 1.8
- soa.version: 1.2.0
- soa-starter.version: 1.1.4
- diyrender.version: 1.8.0
- tddl5.version: 5.3.5
- mybatis-tddl-starter.version: 2.3.4-RELEASE
- tddl-starter.version: 2.3.4-RELEASE
- meter.version: 1.2.0
- dms.version: 2.3.32
- hunter.version: 4.0.13
- hunter-componet.version: 2.8.1
- restapi-core.version: 0.0.11

## 模块依赖
- tool-project-service-common - 基础模块
- tool-project-service-client - 客户端模块
- tool-project-service-web - Web 服务模块
- tool-project-service-biz - 业务逻辑模块

## 第三方依赖
- Spring Boot - 应用框架
- MyBatis - ORM 框架
- Logback - 日志框架
- Jackson - JSON 处理
