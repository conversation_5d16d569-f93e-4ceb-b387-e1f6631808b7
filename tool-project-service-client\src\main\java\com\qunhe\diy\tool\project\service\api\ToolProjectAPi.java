/*
 * ProjectAPi.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.api;

import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.constant.SoaConstant;
import com.qunhe.diy.tool.project.service.common.data.BatchDeleteDTO;
import com.qunhe.diy.tool.project.service.common.data.BatchOperatorVO;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectCopyParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.data.LevelBatchUpdateResponse;
import com.qunhe.diybe.dms.data.LevelDesignCreateResponse;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.rpc.common.utils.Pair;
import com.qunhe.rpc.proxy.annotation.ClientProperties;
import com.qunhe.rpc.proxy.annotation.Content;
import com.qunhe.rpc.proxy.annotation.Header;
import com.qunhe.rpc.proxy.annotation.Http;
import com.qunhe.rpc.proxy.annotation.ParamPairs;
import com.qunhe.rpc.proxy.annotation.Timeout;
import com.qunhe.web.standard.data.Result;

import java.util.List;

import static com.qunhe.rpc.common.Constants.HEADER_X_QH_ID;

/**
 * <AUTHOR>
 */
@ClientProperties(serviceVip = SoaConstant.SERVICE_VIP)
public interface ToolProjectAPi {

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_CREATE_API, method = Http.HttpMethod.POST)
    Result<ToolProjectHomeDesign> create(@Header(HEADER_X_QH_ID) Long userId,
            @Content final ToolProjectSaveParam toolProjectSaveParam);


    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_COPY_API, method = Http.HttpMethod.POST)
    Result<ToolProjectHomeDesign> copy(@Header(HEADER_X_QH_ID) Long userId,
            @Content final ToolProjectCopyParam toolProjectCopyParam);


    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_DELETE_API, method = Http.HttpMethod.POST)
    Result<Boolean> recycleProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_BATCH_DELETE_API, method = Http.HttpMethod.POST)
    Result<BatchDeleteDTO> batchRecycleProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);


    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_RECOVER_API, method = Http.HttpMethod.POST)
    Result<Boolean> recoverProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_BATCH_RECOVER_API, method = Http.HttpMethod.POST)
    Result<Boolean> batchRecoverProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Http(uri = ApiConstant.PROJECT_BATCH_REMOVE_API, method = Http.HttpMethod.POST)
    Result<String> batchRemoveProject(@Content final BatchOperatorVO batchOperatorVO);


    @Http(uri = ApiConstant.PROJECT_BATCH_ROLLBACK_API, method = Http.HttpMethod.POST)
    Result<String> batchRollbackRemoveProject(@Content final BatchOperatorVO batchOperatorVO);

    @Http(uri = ApiConstant.PROJECT_BATCH_OPERATE_STATUS_API, method = Http.HttpMethod.GET)
    Result<BatchOperationStatusEnum> getBatchOperateStatus(
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_RESTORE_API, method = Http.HttpMethod.POST)
    Result<Boolean> restoreProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_REMOVE_API, method = Http.HttpMethod.POST)
    Result<Boolean> deleteProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_ROLLBACK_API, method = Http.HttpMethod.POST)
    Result<Boolean> recoverDeleteProjectDesign(@Header(HEADER_X_QH_ID) Long userId,
            @ParamPairs List<Pair<String, Object>> params);

    @Timeout(value = 3000)
    @Http(uri = ApiConstant.PROJECT_HANDOVER_API, method = Http.HttpMethod.POST)
    Result<Boolean> handoverProject(@Content final List<Long> fromUserIds,
            @ParamPairs List<Pair<String, Object>> params);

    @Http(uri = ApiConstant.PROJECT_LEVEL_BATCH_UPDATE, method = Http.HttpMethod.POST)
    LevelBatchUpdateResponse batchCreateOrUpdateLevels(@ParamPairs List<Pair<String, Object>> params,
            @Content List<LevelInfo> levelInfos);

    @Http(uri = ApiConstant.PROJECT_LEVEL_CREATE, method = Http.HttpMethod.POST)
    LevelDesignCreateResponse createLevel(@ParamPairs List<Pair<String, Object>> params);

    @Http(uri = ApiConstant.PROJECT_LEVEL_UPDATE, method = Http.HttpMethod.POST)
    Boolean updateLevel(@ParamPairs List<Pair<String, Object>> params,
                                @Content List<LevelInfo> levelInfos);

    @Http(uri = ApiConstant.PROJECT_LEVEL_DELETE, method = Http.HttpMethod.DELETE)
    Boolean deleteLevel(@ParamPairs List<Pair<String, Object>> params);

    @Http(uri = ApiConstant.PROJECT_LEVEL_RECOVER, method = Http.HttpMethod.POST)
    Boolean recoverDeletedLevel(@ParamPairs List<Pair<String, Object>> params);

    @Http(uri = ApiConstant.PROJECT_NEED_DESIGN_REVISION, method = Http.HttpMethod.GET)
    Boolean needDesignRevision(@ParamPairs List<Pair<String, Object>> params);

}
