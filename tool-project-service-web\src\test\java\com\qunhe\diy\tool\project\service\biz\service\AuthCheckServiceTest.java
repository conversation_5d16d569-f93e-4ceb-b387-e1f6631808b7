/*
 * AuthCheckServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.google.common.collect.Sets;
import com.qunhe.cooperate.helper.CooperateHelper;
import com.qunhe.custom.dcs.order.client.exception.DcsOrderApiException;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.DcsOrderAuthFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.web.context.ToolTypeContextHolder;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.project.platform.project.auth.enums.AuthCheckType;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Set;

import static com.qunhe.diy.tool.project.service.biz.service.AuthCheckService.COOHOM_USER_APIC_TAG;
import static com.qunhe.diy.tool.project.service.biz.service.AuthCheckService.COOHOM_USER_TAG;
import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AuthCheckService 的单元测试类。
 * <AUTHOR>
 * @date 2025/5/12
 */
@RunWith(MockitoJUnitRunner.class)
public class AuthCheckServiceTest {

    private static final Long TEST_USER_ID = 123L;
    private static final Long TEST_DESIGN_ID = 456L;
    private static final Long TEST_ORDER_DESIGN_ID = 789L;
    private static final Long RECYCLE_BIN_USER_ID = 1L;

    @InjectMocks
    private AuthCheckService authCheckService;

    @Mock
    private ProjectDesignFacadeDb mockPlanDb;

    @Mock
    private ProjectAuthService mockProjectAuthFacade;

    @Mock
    private DcsOrderAuthFacade mockDcsOrderAuthFacade;

    @Mock
    private BusinessAccountDb mockBusinessAccountDb;

    @Mock
    private SaaSConfigService mockSaaSConfigService;

    @Mock
    private UserInfoFacade mockUserInfoFacade;

    @Mock
    private ToadProperties mockToadProperties;

    @Mock
    private HttpServletRequest mockRequest;

    @Mock
    private DiyDesignInfo mockYunDesign;

    private MockedStatic<ToolTypeContextHolder> mockToolTypeContextHolder;
    private MockedStatic<CooperateHelper> mockCooperateHelper;


    @Before
    public void setUp() {
        // Mock 静态方法
        mockToolTypeContextHolder = Mockito.mockStatic(ToolTypeContextHolder.class);
        mockCooperateHelper = Mockito.mockStatic(CooperateHelper.class);

        // 通用 YunDesign 设置
        when(mockYunDesign.getDesignId()).thenReturn(TEST_DESIGN_ID);
        when(mockYunDesign.getUserId()).thenReturn(TEST_USER_ID); // 默认设计属于测试用户
    }

    // --- checkAuthFromDesignId 方法的测试用例 ---

    /**
     * 测试 checkAuthFromDesignId 方法：当设计方案不存在时。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthFromDesignId_DesignNotFound_ShouldReturnFalse() {
        // 中文注释：模拟 planDb.getDiyDesignInfo 返回 null，表示设计不存在
        when(mockPlanDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(null);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthFromDesignId(TEST_USER_ID, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, false);

        // 中文注释：断言结果为 false
        assertFalse("设计不存在时，鉴权应失败", result);
        // 中文注释：验证 checkAuthInternal 没有被调用
        verify(mockProjectAuthFacade, never()).checkAuth(anyLong(), anyLong(), anyBoolean(),
                anyBoolean(), anyLong(), any(YunDesign.class), any(AuthCheckType.class));
    }

    /**
     * 测试 checkAuthFromDesignId 方法：当设计方案在回收站时。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthFromDesignId_DesignInRecycleBin_ShouldReturnFalse() {
        // 中文注释：模拟设计在回收站中 (userId 为 1L)
        when(mockYunDesign.getUserId()).thenReturn(RECYCLE_BIN_USER_ID);
        when(mockPlanDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(mockYunDesign);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthFromDesignId(TEST_USER_ID, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, false);

        // 中文注释：断言结果为 false
        assertFalse("回收站方案鉴权应失败", result);
        // 中文注释：验证 checkAuthInternal 没有被调用
        verify(mockProjectAuthFacade, never()).checkAuth(anyLong(), anyLong(), anyBoolean(),
                anyBoolean(), anyLong(), any(YunDesign.class), any(AuthCheckType.class));
    }

    /**
     * 测试 checkAuthFromDesignId 方法：成功授权的场景。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckAuthFromDesignId_AuthSuccess_ShouldReturnTrue()
            throws DcsOrderApiException {
        // 中文注释：模拟 planDb.getDiyDesignInfo 返回有效设计
        when(mockPlanDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(mockYunDesign);
        // 中文注释：模拟内部鉴权成功
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(
                false); // 确保 grantAuthByDcsOrder 逻辑被执行
        when(mockProjectAuthFacade.checkAuth(eq(TEST_DESIGN_ID), eq(TEST_USER_ID), eq(false),
                eq(true), eq(TEST_ORDER_DESIGN_ID), eq(mockYunDesign),
                eq(AuthCheckType.WRITE))).thenReturn(true);


        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthFromDesignId(TEST_USER_ID, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, false);

        // 中文注释：断言结果为 true
        assertTrue("正常流程鉴权成功", result);
        // 中文注释：验证 dcsOrderAuthFacade.checkAndUpdateDesignAuth 被调用
        verify(mockDcsOrderAuthFacade, times(1)).checkAndUpdateDesignAuth(TEST_DESIGN_ID,
                TEST_ORDER_DESIGN_ID, TEST_USER_ID);
    }

    /**
     * 测试 checkAuthFromDesignId 方法：授权失败的场景。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthFromDesignId_AuthFailed_ShouldReturnFalse()
            throws DcsOrderApiException {
        // 中文注释：模拟 planDb.getDiyDesignInfo 返回有效设计
        when(mockPlanDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(mockYunDesign);
        // 中文注释：模拟内部鉴权失败
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(true); // 简化，不走 dcsOrderAuthFacade
        when(mockProjectAuthFacade.checkAuth(eq(TEST_DESIGN_ID), eq(TEST_USER_ID), eq(false),
                eq(true), eq(TEST_ORDER_DESIGN_ID), eq(mockYunDesign),
                eq(AuthCheckType.WRITE))).thenReturn(false);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthFromDesignId(TEST_USER_ID, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, false);

        // 中文注释：断言结果为 false
        assertFalse("正常流程鉴权失败", result);
    }

    // --- checkAuthInternal 方法的测试用例 ---

    /**
     * 测试 checkAuthInternal 方法：当 userId 为 null 时。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthInternal_UserIdNull_ShouldReturnFalse() {
        // 中文注释：执行被测试方法，userId 为 null
        boolean result = authCheckService.checkAuthInternal(false, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, mockYunDesign, null);
        // 中文注释：断言结果为 false
        assertFalse("userId 为 null 时，鉴权应失败", result);
    }

    /**
     * 测试 checkAuthInternal 方法：DCS订单，需要协作，授权成功。
     * 预期结果：返回 true。
     * toadProperties.isDcsOrderGrantAuth() 为 false，会调用 dcsOrderAuthFacade。
     */
    @Test
    public void testCheckAuthInternal_DcsOrder_Cooperate_AuthSuccess_GrantAuthCalled()
            throws DcsOrderApiException {
        // 中文注释：模拟是DCS订单 (orderDesignId 不为 null)
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        // 中文注释：模拟 toadProperties.isDcsOrderGrantAuth() 返回 false
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(false);
        // 中文注释：模拟 projectAuthFacade.checkAuth 成功
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, true, true,
                TEST_ORDER_DESIGN_ID, mockYunDesign, AuthCheckType.WRITE)).thenReturn(true);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(true, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("DCS订单+协作+授权成功，结果应为true", result);
        // 中文注释：验证 dcsOrderAuthFacade.checkAndUpdateDesignAuth 被调用
        verify(mockDcsOrderAuthFacade, times(1)).checkAndUpdateDesignAuth(TEST_DESIGN_ID,
                TEST_ORDER_DESIGN_ID, TEST_USER_ID);
        // 中文注释：验证 CooperateHelper.putCooperate 被调用
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(true, TEST_USER_ID),
                times(1));
    }

    /**
     * 测试 checkAuthInternal 方法：DCS订单，需要协作，授权成功。
     * toadProperties.isDcsOrderGrantAuth() 为 true，不会调用 dcsOrderAuthFacade。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckAuthInternal_DcsOrder_Cooperate_AuthSuccess_GrantAuthSkipped()
            throws DcsOrderApiException {
        // 中文注释：模拟是DCS订单 (orderDesignId 不为 null)
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        // 中文注释：模拟 toadProperties.isDcsOrderGrantAuth() 返回 true
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(true);
        // 中文注释：模拟 projectAuthFacade.checkAuth 成功
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, true, true,
                TEST_ORDER_DESIGN_ID, mockYunDesign, AuthCheckType.WRITE)).thenReturn(true);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(true, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("DCS订单+协作+授权成功（跳过grant），结果应为true", result);
        // 中文注释：验证 dcsOrderAuthFacade.checkAndUpdateDesignAuth 未被调用
        verify(mockDcsOrderAuthFacade, never()).checkAndUpdateDesignAuth(anyLong(), anyLong(),
                anyLong());
        // 中文注释：验证 CooperateHelper.putCooperate 被调用
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(true, TEST_USER_ID),
                times(1));
    }

    /**
     * 测试 checkAuthInternal 方法：DCS订单，dcsOrderAuthFacade.checkAndUpdateDesignAuth 抛出异常，但后续鉴权成功。
     * 预期结果：返回 true (异常被捕获，不影响后续鉴权)。
     */
    @Test
    public void testCheckAuthInternal_DcsOrder_GrantAuthException_AuthSuccess()
            throws DcsOrderApiException {
        // 中文注释：模拟是DCS订单
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        // 中文注释：模拟 toadProperties.isDcsOrderGrantAuth() 返回 false
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(false);
        // 中文注释：模拟 dcsOrderAuthFacade.checkAndUpdateDesignAuth 抛出异常
        Mockito.doThrow(new DcsOrderApiException("DCS Auth Error")).when(mockDcsOrderAuthFacade)
                .checkAndUpdateDesignAuth(TEST_DESIGN_ID, TEST_ORDER_DESIGN_ID, TEST_USER_ID);
        // 中文注释：模拟 projectAuthFacade.checkAuth 成功
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, true,
                TEST_ORDER_DESIGN_ID, mockYunDesign, AuthCheckType.WRITE)).thenReturn(true);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(false, TEST_ORDER_DESIGN_ID,
                TEST_DESIGN_ID, mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("DCS订单grant异常但后续鉴权成功，结果应为true", result);
        // 中文注释：验证 CooperateHelper.putCooperate 未被调用 (因为 cooperate 为 false)
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(anyBoolean(), anyLong()),
                never());
    }


    /**
     * 测试 checkAuthInternal 方法：云设计定制，不需要协作，授权失败。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthInternal_YunDesignCustom_NoCooperate_AuthFailed()
            throws DcsOrderApiException {
        // 中文注释：模拟是云设计定制
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(true);
        // 中文注释：模拟 toadProperties.isDcsOrderGrantAuth() 返回 false (orderDesignId 为 null 时也会调用
        // isCustomVerifyDesign)
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(false);
        // 中文注释：模拟 projectAuthFacade.checkAuth 失败
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, true, null,
                mockYunDesign, AuthCheckType.WRITE)).thenReturn(false);


        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(false, null, TEST_DESIGN_ID,
                mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("云设计定制+无协作+授权失败，结果应为false", result);
        // 中文注释：验证 dcsOrderAuthFacade.checkAndUpdateDesignAuth 被调用 (因为 isCustomVerifyDesign 为 true)
        verify(mockDcsOrderAuthFacade, times(1)).checkAndUpdateDesignAuth(TEST_DESIGN_ID, null,
                TEST_USER_ID);
        // 中文注释：验证 CooperateHelper.putCooperate 未被调用
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(anyBoolean(), anyLong()),
                never());
    }

    /**
     * 测试 checkAuthInternal 方法：非DCS订单，不需要协作，授权成功。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckAuthInternal_NoDcs_NoCooperate_AuthSuccess() throws DcsOrderApiException {
        // 中文注释：模拟非DCS订单 (orderDesignId 为 null 且非云设计定制)
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        // 中文注释：模拟 projectAuthFacade.checkAuth 成功
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, false, false, null,
                mockYunDesign, AuthCheckType.WRITE)).thenReturn(true);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(false, null, TEST_DESIGN_ID,
                mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("非DCS+无协作+授权成功，结果应为true", result);
        // 中文注释：验证 dcsOrderAuthFacade.checkAndUpdateDesignAuth 未被调用
        verify(mockDcsOrderAuthFacade, never()).checkAndUpdateDesignAuth(anyLong(), anyLong(),
                anyLong());
        // 中文注释：验证 CooperateHelper.putCooperate 未被调用
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(anyBoolean(), anyLong()),
                never());
    }

    /**
     * 测试 checkAuthInternal 方法：需要协作，但 projectAuthFacade.checkAuth 返回 false。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckAuthInternal_Cooperate_AuthFailed_ShouldReturnFalse() {
        // 中文注释：模拟非DCS订单 (orderDesignId 为 null 且非云设计定制)
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        // 中文注释：模拟 projectAuthFacade.checkAuth 失败
        when(mockProjectAuthFacade.checkAuth(TEST_DESIGN_ID, TEST_USER_ID, true, false, null,
                mockYunDesign, AuthCheckType.WRITE)).thenReturn(false);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkAuthInternal(true, null, TEST_DESIGN_ID,
                mockYunDesign, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("协作但鉴权失败，结果应为false", result);
        // 中文注释：验证 CooperateHelper.putCooperate 被调用，参数为 false
        mockCooperateHelper.verify(() -> CooperateHelper.putCooperate(false, TEST_USER_ID),
                times(1));
    }


    // --- checkCoohomUser 方法的测试用例 ---

    /**
     * 测试 checkCoohomUser 方法：userInfoFacade.matchUserTags 返回 null，通过 host 判断为 coohom 用户。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomUser_MatchTagsFails_CheckByHostTrue_ShouldReturnTrue() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回 null
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(null);
        // 中文注释：模拟 request.getHeader 返回 coohom 域名
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.coohom.com");

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("matchUserTags为null，通过host判断为coohom用户", result);
    }

    /**
     * 测试 checkCoohomUser 方法：userInfoFacade.matchUserTags 返回 null，通过 host 判断非 coohom 用户。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUser_MatchTagsFails_CheckByHostFalse_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回 null
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(null);
        // 中文注释：模拟 request.getHeader 返回非 coohom 域名
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.other.com");

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("matchUserTags为null，通过host判断非coohom用户", result);
    }

    /**
     * 测试 checkCoohomUser 方法：企业根账号不为 null。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUser_RootAccountNotNull_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回包含 COOHOM_USER_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet(COOHOM_USER_TAG));
        // 中文注释：模拟 businessAccountDb.getRootAccountForBAndC 返回非 null
        when(mockBusinessAccountDb.getRootAccountForBAndC(TEST_USER_ID)).thenReturn(999L);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("企业根账户不为null，不应是coohom个人用户", result);
    }

    /**
     * 测试 checkCoohomUser 方法：企业根账号为 null，但用户标签不包含 COOHOM_USER_TAG。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUser_RootAccountNull_NoCoohomTag_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回不包含 COOHOM_USER_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet("OTHER_TAG"));
        // 中文注释：模拟 businessAccountDb.getRootAccountForBAndC 返回 null
        when(mockBusinessAccountDb.getRootAccountForBAndC(TEST_USER_ID)).thenReturn(null);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("根账户为null但无COOHOM_USER_TAG，不应是coohom个人用户", result);
    }

    /**
     * 测试 checkCoohomUser 方法：企业根账号为 null，且用户标签包含 COOHOM_USER_TAG。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomUser_RootAccountNull_HasCoohomTag_ShouldReturnTrue() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回包含 COOHOM_USER_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet(COOHOM_USER_TAG));
        // 中文注释：模拟 businessAccountDb.getRootAccountForBAndC 返回 null
        when(mockBusinessAccountDb.getRootAccountForBAndC(TEST_USER_ID)).thenReturn(null);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 true
        assertTrue("根账户为null且有COOHOM_USER_TAG，应是coohom个人用户", result);
    }

    /**
     * 测试 checkCoohomUser 方法：用户标签包含 COOHOM_USER_APIC_TAG 但不包含 COOHOM_USER_TAG。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUser_HasCoohomUserApicTagOnly_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回只包含 COOHOM_USER_APIC_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet(COOHOM_USER_APIC_TAG));
        // 中文注释：模拟 businessAccountDb.getRootAccountForBAndC 返回 null
        when(mockBusinessAccountDb.getRootAccountForBAndC(TEST_USER_ID)).thenReturn(null);

        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUser(mockRequest, TEST_USER_ID);

        // 中文注释：断言结果为 false
        assertFalse("仅有COOHOM_USER_APIC_TAG，不应是coohom个人用户", result);
    }


    // --- checkCoohomUserByHost 方法的测试用例 ---

    /**
     * 测试 checkCoohomUserByHost 方法：request.getHeader 返回 null。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUserByHost_HostNull_ShouldReturnFalse() {
        // 中文注释：模拟 request.getHeader 返回 null
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(null);
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByHost(mockRequest);
        // 中文注释：断言结果为 false
        assertFalse("Host为null时，应返回false", result);
    }

    /**
     * 测试 checkCoohomUserByHost 方法：request.getHeader 返回空字符串。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUserByHost_HostEmpty_ShouldReturnFalse() {
        // 中文注释：模拟 request.getHeader 返回空字符串
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("");
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByHost(mockRequest);
        // 中文注释：断言结果为 false
        assertFalse("Host为空字符串时，应返回false", result);
    }

    /**
     * 测试 checkCoohomUserByHost 方法：request.getHeader 返回包含 "coohom" 的域名。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomUserByHost_HostContainsCoohom_ShouldReturnTrue() {
        // 中文注释：模拟 request.getHeader 返回包含 coohom 的域名
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("www.coohom.com");
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByHost(mockRequest);
        // 中文注释：断言结果为 true
        assertTrue("Host包含coohom时，应返回true", result);
    }

    /**
     * 测试 checkCoohomUserByHost 方法：request.getHeader 返回不包含 "coohom" 的域名。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUserByHost_HostNotContainsCoohom_ShouldReturnFalse() {
        // 中文注释：模拟 request.getHeader 返回不包含 coohom 的域名
        when(mockRequest.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("www.example.com");
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByHost(mockRequest);
        // 中文注释：断言结果为 false
        assertFalse("Host不包含coohom时，应返回false", result);
    }

    // --- checkCoohomBimAuthPoint 方法的测试用例 ---

    /**
     * 测试 checkCoohomBimAuthPoint 方法：用户拥有 BIM 权限点。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomBimAuthPoint_HasAuth_ShouldReturnTrue()
            throws AccessAuthenticatorException {
        // 中文注释：模拟 saaSConfigService.checkBusinessAccessPoint 返回 true
        when(mockSaaSConfigService.checkBusinessAccessPoint(eq(TEST_USER_ID), eq(null), eq(null),
                eq(AuthCheckService.COOHOM_BIM_ACCESS_POINTS))).thenReturn(true);
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomBimAuthPoint(TEST_USER_ID);
        // 中文注释：断言结果为 true
        assertTrue("拥有BIM权限点，应返回true", result);
    }

    /**
     * 测试 checkCoohomBimAuthPoint 方法：用户没有 BIM 权限点。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomBimAuthPoint_NoAuth_ShouldReturnFalse()
            throws AccessAuthenticatorException {
        // 中文注释：模拟 saaSConfigService.checkBusinessAccessPoint 返回 false
        when(mockSaaSConfigService.checkBusinessAccessPoint(eq(TEST_USER_ID), eq(null), eq(null),
                eq(AuthCheckService.COOHOM_BIM_ACCESS_POINTS))).thenReturn(false);
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomBimAuthPoint(TEST_USER_ID);
        // 中文注释：断言结果为 false
        assertFalse("没有BIM权限点，应返回false", result);
    }

    /**
     * 测试 checkCoohomBimAuthPoint 方法：saaSConfigService.checkBusinessAccessPoint 抛出
     * AccessAuthenticatorException。
     * 预期结果：抛出 AccessAuthenticatorException。
     */
    @Test(expected = AccessAuthenticatorException.class)
    public void testCheckCoohomBimAuthPoint_ThrowsException() throws AccessAuthenticatorException {
        // 中文注释：模拟 saaSConfigService.checkBusinessAccessPoint 抛出异常
        when(mockSaaSConfigService.checkBusinessAccessPoint(eq(TEST_USER_ID), eq(null), eq(null),
                eq(AuthCheckService.COOHOM_BIM_ACCESS_POINTS))).thenThrow(
                new AccessAuthenticatorException("Test Exception"));
        // 中文注释：执行被测试方法，预期抛出异常
        authCheckService.checkCoohomBimAuthPoint(TEST_USER_ID);
    }

    // --- checkCoohomUserByUserId 方法的测试用例 ---

    /**
     * 测试 checkCoohomUserByUserId 方法：userInfoFacade.matchUserTags 返回空集合。
     * 预期结果：返回 false。
     */
    @Test
    public void testCheckCoohomUserByUserId_TagsEmpty_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回空集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Collections.emptySet());
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByUserId(TEST_USER_ID);
        // 中文注释：断言结果为 false
        assertFalse("用户标签为空集合时，应返回false", result);
    }

    /**
     * 测试 checkCoohomUserByUserId 方法：userInfoFacade.matchUserTags 返回包含 COOHOM_USER_TAG 的非空集合。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomUserByUserId_TagsNotEmptyWithCoohomTag_ShouldReturnTrue() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回包含 COOHOM_USER_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet(COOHOM_USER_TAG));
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByUserId(TEST_USER_ID);
        // 中文注释：断言结果为 true
        assertTrue("用户标签包含COOHOM_USER_TAG时，应返回true", result);
    }

    /**
     * 测试 checkCoohomUserByUserId 方法：userInfoFacade.matchUserTags 返回包含 COOHOM_USER_APIC_TAG 的非空集合。
     * 预期结果：返回 true。
     */
    @Test
    public void testCheckCoohomUserByUserId_TagsNotEmptyWithApicTag_ShouldReturnTrue() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回包含 COOHOM_USER_APIC_TAG 的集合
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(
                Sets.newHashSet(COOHOM_USER_APIC_TAG));
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByUserId(TEST_USER_ID);
        // 中文注释：断言结果为 true
        assertTrue("用户标签包含COOHOM_USER_APIC_TAG时，应返回true", result);
    }

    /**
     * 测试 checkCoohomUserByUserId 方法：userInfoFacade.matchUserTags 返回 null。
     * 预期结果：返回 false (CollectionUtils.isNotEmpty(null) is false)。
     */
    @Test
    public void testCheckCoohomUserByUserId_TagsNull_ShouldReturnFalse() {
        // 中文注释：模拟 userInfoFacade.matchUserTags 返回 null
        when(mockUserInfoFacade.matchUserTags(eq(TEST_USER_ID), any(Set.class))).thenReturn(null);
        // 中文注释：执行被测试方法
        boolean result = authCheckService.checkCoohomUserByUserId(TEST_USER_ID);
        // 中文注释：断言结果为 false
        assertFalse("用户标签为null时，应返回false", result);
    }

    /**
     * 测试 isCustomVerifyDesign 方法的间接覆盖: orderDesignId 不为 null
     */
    @Test
    public void testIsCustomVerifyDesign_OrderDesignIdNotNull() throws DcsOrderApiException {
        // 此方法是 private, 通过 checkAuthInternal 调用来测试其逻辑
        // 中文注释：设置 orderDesignId 不为 null
        Long orderDesignId = 1L;
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(
                false); // 确保不是因为这个条件为 true
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(true); // 简化
        when(mockProjectAuthFacade.checkAuth(anyLong(), anyLong(), anyBoolean(), eq(true),
                anyLong(), any(), any())).thenReturn(true);

        // 中文注释：执行调用
        authCheckService.checkAuthInternal(false, orderDesignId, TEST_DESIGN_ID, mockYunDesign,
                TEST_USER_ID);

        // 中文注释：验证 projectAuthFacade.checkAuth 的 needCheckDcs 参数为 true
        verify(mockProjectAuthFacade).checkAuth(anyLong(), anyLong(), anyBoolean(), eq(true),
                anyLong(), any(), any());
    }

    /**
     * 测试 isCustomVerifyDesign 方法的间接覆盖: ToolTypeContextHolder.isYunDesignCustom() 为 true
     */
    @Test
    public void testIsCustomVerifyDesign_ToolTypeContextHolderTrue() throws DcsOrderApiException {
        // 此方法是 private, 通过 checkAuthInternal 调用来测试其逻辑
        // 中文注释：设置 orderDesignId 为 null
        Long orderDesignId = null;
        // 中文注释：设置 ToolTypeContextHolder.isYunDesignCustom() 返回 true
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(true);
        when(mockToadProperties.isDcsOrderGrantAuth()).thenReturn(true); // 简化
        when(mockProjectAuthFacade.checkAuth(anyLong(), anyLong(), anyBoolean(), eq(true), any(),
                any(), any())).thenReturn(true);

        // 中文注释：执行调用
        authCheckService.checkAuthInternal(false, orderDesignId, TEST_DESIGN_ID, mockYunDesign,
                TEST_USER_ID);

        // 中文注释：验证 projectAuthFacade.checkAuth 的 needCheckDcs 参数为 true
        verify(mockProjectAuthFacade).checkAuth(anyLong(), anyLong(), anyBoolean(), eq(true), any(),
                any(), any());
    }


    /**
     * 测试 isCustomVerifyDesign 方法的间接覆盖: 两者都为 false
     */
    @Test
    public void testIsCustomVerifyDesign_BothFalse() {
        // 此方法是 private, 通过 checkAuthInternal 调用来测试其逻辑
        // 中文注释：设置 orderDesignId 为 null
        Long orderDesignId = null;
        // 中文注释：设置 ToolTypeContextHolder.isYunDesignCustom() 返回 false
        mockToolTypeContextHolder.when(ToolTypeContextHolder::isYunDesignCustom).thenReturn(false);
        when(mockProjectAuthFacade.checkAuth(anyLong(), anyLong(), anyBoolean(), eq(false), any(),
                any(), any())).thenReturn(true);

        // 中文注释：执行调用
        authCheckService.checkAuthInternal(false, orderDesignId, TEST_DESIGN_ID, mockYunDesign,
                TEST_USER_ID);

        // 中文注释：验证 projectAuthFacade.checkAuth 的 needCheckDcs 参数为 false
        verify(mockProjectAuthFacade).checkAuth(anyLong(), anyLong(), anyBoolean(), eq(false),
                any(), any(), any());
    }


    @org.junit.After
    public void tearDown() {
        // 关闭静态 mock
        mockToolTypeContextHolder.close();
        mockCooperateHelper.close();
    }
}