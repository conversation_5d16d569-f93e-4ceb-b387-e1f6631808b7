/*
 * ToolProjectCopyParam.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolProjectCopyParam {
    private Long scrPlanId;
    private Long srcDesignId;
    private Long dstUserId;
    private String name;
    private Boolean reworked;
    private Byte sourceId;

    /**
     * 是否需要复制被删除的方案 （deleted = true or userId = 1）
     */
    private Boolean includeDeleted = false;
    /**
     * 如果非空则指定目标方案的planType
     */
    private Integer planType;

    /**
     * @link com.qunhe.diybe.dms.data.Region
     */
    private Integer region;

    private Integer createdAppId;
}
