/*
 * ToolType.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/14
 */
@Getter
@RequiredArgsConstructor
public enum VrcRegion {

    UNKNOWN("unknown"),

    K<PERSON><PERSON><PERSON><PERSON>("kujiale"),

    COOHOM("coohom"),


    ;

    private final String value;

    public static VrcRegion fromValue(String value) {
        for (VrcRegion region : values()) {
            if (region.getValue().equals(value)) {
                return region;
            }
        }
        return UNKNOWN;
    }

}
