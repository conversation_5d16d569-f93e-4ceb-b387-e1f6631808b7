/*
 * ToolProjectBizzException.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.exception;

import com.qunhe.web.standard.data.Result;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class ToolProjectBizzException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String code;

    private String desc;

    private HttpStatus status;

    private Object[] args;

    public Result<?> asResult() {
        return Result.error(this.getCode(), this.getMessage());
    }

    public ToolProjectBizzException(Exception e) {
        super(e);
    }

    public ToolProjectBizzException(String message) {
        super(message);
    }

    public ToolProjectBizzException(String message, Exception e) {
        super(message, e);
    }


}
