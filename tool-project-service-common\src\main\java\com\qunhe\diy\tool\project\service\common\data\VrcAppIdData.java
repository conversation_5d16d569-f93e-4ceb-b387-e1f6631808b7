/*
 * VrcAppIdData.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class VrcAppIdData {
    private String vrc;
    private String vType;
    private String rType;
    private int appId;
    private int accessPoint;
    private String appName;
    private String creator;
    private String stage;
    private String description;
    private String appKey;
    /**
     * 标识vrc映射所属
     * kujiale, coohom
     */
    private String region;
}
