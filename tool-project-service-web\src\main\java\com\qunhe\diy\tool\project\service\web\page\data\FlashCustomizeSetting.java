/*
 * FlashCustomizeSetting.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("PMD")
public class FlashCustomizeSetting {
    public static final FlashCustomizeSetting EMPTY_SETTING = new FlashCustomizeSetting();
    private Boolean show;

    private String picUrl;

    private String text;

    private String link;

    abstract public static class OutputMixin {
        @JsonProperty("url")
        abstract String getLink();
    }
}
