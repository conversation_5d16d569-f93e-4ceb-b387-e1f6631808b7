/*
 * VrcErrorException.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.exception;


/**
 * <AUTHOR>
 */
public class VrcMappingNotFoundException extends RuntimeException{
    private static final long serialVersionUID = 1520689007956882620L;

    public VrcMappingNotFoundException(final Long planId, final String vrc, final String obsAppId) {
        super(String.format("planId <%d> : vrc <%s> with appId <%s> not found", planId, vrc,
                obsAppId));
    }
}
