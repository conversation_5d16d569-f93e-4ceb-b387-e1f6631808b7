/*
 * AppType.java
 * Copyright 2020 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.diy;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Getter
public enum AppType {
    PUBLIC_DECORATION(5L, "3FO4K4VYAW5G", "V0150R0401"),
    SUPER_HUXING(25L, "3FO4K4VYAIRC", "V0150R0501"),
    COOHOM(31L, "3FO4K4VY9XP6", "V0140R0201"),
    PHOTO_STUDIO_1_0(10L, "3FO4K4VYBR8P", "V0140R0301"),
    ;
    private final Long appId;

    private final String code;

    private final String vrc;

    AppType(final Long appId, final String code, final String vrc) {
        this.appId = appId;
        this.code = code;
        this.vrc = vrc;
    }

    public static Long getAppIdByVrc(final String vrc) {
        for (final AppType appType : AppType.values()) {
            if (Objects.equals(vrc, appType.getVrc())) {
                return appType.appId;
            }
        }
        return null;
    }


    public static boolean isBimApp(final Long appId) {
        if (appId == null) {
            return false;
        }
        return Objects.equals(appId, PUBLIC_DECORATION.appId) || Objects.equals(appId,
                SUPER_HUXING.appId);
    }
}
