/*
 * AbstractBimPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.handler;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.qunhe.diy.designinfoservice.common.data.user.DesignOwnerStatusResult;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SynergyFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.biz.util.JsonMapper;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.diy.tool.project.service.web.page.DesignService;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.VrcRewriteService;
import com.qunhe.diy.tool.project.service.web.page.VrcService;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.DesignInfo;
import com.qunhe.diy.tool.project.service.web.page.data.FavorIcon;
import com.qunhe.diy.tool.project.service.web.page.data.FlashCustomizeSetting;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diy.tool.project.service.web.page.data.PlanHelpConfig;
import com.qunhe.diy.tool.project.service.web.page.data.PlanInfo;
import com.qunhe.diy.tool.project.service.web.page.data.PlanInfoConfig;
import com.qunhe.diy.tool.project.service.web.page.data.RootAccount;
import com.qunhe.diy.tool.project.service.web.page.data.ToolJsConfig;
import com.qunhe.diy.tool.project.service.web.page.data.User;
import com.qunhe.diybe.dms.data.YunDesign;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.hunter.util.IpUtil;
import com.qunhe.instdeco.businessaccount.sdk.data.TobBusinessAccount;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfig;
import com.qunhe.instdeco.plan.businessconfig.BusinessConfigMap;
import com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigElement;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.http.HttpStatus;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.ALL_TOOL_CONFIG_LANGUAGE_TYPE;
import static com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService.COMMON_CONFIG_FAVOR_ICON;

/**
 * 抽象页面处理器，定义通用的页面处理流程
 * <AUTHOR>
 */
public abstract class AbstractBimPageHandler {
    protected static final QHLogger LOG = QHLogger.getLogger(AbstractBimPageHandler.class);
    protected static final String BIM_VERSION_NAME = "bim";
    protected static final String BIM_VERSION_URL = "//qhstaticssl.kujiale.com/h5tool/bim/";
    protected static final String BIM_APP_KEY = "yunDesign_BIM";
    protected static final String COOKIE_HEADER_KEY = "qhdi";
    protected static final Long PARAMETRIC_WALL_PANEL_ACCESS_POINT = 11001L;
    protected static final Long PARAMETRIC_WALL_PANEL_PRIVATE_LIB_ACCESS_POINT = 11019L;
    protected static final String ERROR_CODE_KEY = "errorcode";
    protected static final String YUN_DESIGN_NOT_SUPPORT_ERROR_CODE = "YUN_DESIGN_NOT_SUPPORT";
    protected static final String BIM_VTYPE = "V0150";

    protected final BusinessAccountDb businessAccountDb;
    protected final DesignService designService;
    protected final UserDb userDb;
    protected final ProjectDesignFacadeDb projectDesignFacadeDb;
    protected final SynergyFacade synergyFacade;
    protected final VrcRewriteService vrcRewriteService;
    protected final SessionConflictHandler sessionConflictHandler;
    protected final ToadProperties toadProperties;
    protected final SaaSConfigService saaSConfigService;
    protected final ToolLinkConfigCache toolLinkConfigCache;
    protected final UserInfoFacade userInfoFacade;
    protected final BusinessAccessService businessAccessService;
    protected final VrcService vrcService;
    protected final AuthCheckService authCheckService;

    public AbstractBimPageHandler(BusinessAccountDb businessAccountDb, DesignService designService,
            UserDb userDb, ProjectDesignFacadeDb projectDesignFacadeDb,
            SynergyFacade synergyFacade, VrcRewriteService vrcRewriteService,
            SessionConflictHandler sessionConflictHandler, ToadProperties toadProperties,
            SaaSConfigService saaSConfigService, ToolLinkConfigCache toolLinkConfigCache,
            UserInfoFacade userInfoFacade, BusinessAccessService businessAccessService,
            VrcService vrcService, AuthCheckService authCheckService) {
        this.businessAccountDb = businessAccountDb;
        this.designService = designService;
        this.userDb = userDb;
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.synergyFacade = synergyFacade;
        this.vrcRewriteService = vrcRewriteService;
        this.sessionConflictHandler = sessionConflictHandler;
        this.toadProperties = toadProperties;
        this.saaSConfigService = saaSConfigService;
        this.toolLinkConfigCache = toolLinkConfigCache;
        this.userInfoFacade = userInfoFacade;
        this.businessAccessService = businessAccessService;
        this.vrcService = vrcService;
        this.authCheckService = authCheckService;
    }

    /**
     * 处理页面请求的模板方法
     */
    @NotNull
    public ToolPageResult handlePage(HttpServletRequest request, BimPageParam param)
            throws AccessAuthenticatorException {
        Long userId = param.getUserId();
        String obsDesignId = param.getObsDesignId();
        String levelId = param.getLevelId();
        Long rootAccountId = getRootAccountId(userId);

        final H5Model h5Model = new H5Model();
        fillBasicInfo(request, param.getRedirectUrl(), param.getStage(), userId, h5Model);
        fillParametricWallInfo(userId, rootAccountId, null, h5Model);

        // 处理设计信息
        if (StringUtil.isNotEmpty(obsDesignId)) {
            ToolPageResult response = handleExistingDesign(request, param, userId, obsDesignId,
                    levelId, h5Model);
            if (response != null) {
                return response;
            }
        } else {
            // 处理新建方案
            ToolPageResult response = handleNewDesign(request, param, userId, rootAccountId);
            if (response != null) {
                return response;
            }
        }

        try {
            // 填充用户信息
            fillUserInfo(userId, h5Model);
            fillAppInfo(h5Model);
            fillToolJsConfig(h5Model);
            fillBusinessFavorIcon(userId, h5Model);
            fillAccountInfo(userId, rootAccountId, h5Model);
            fillPlanInfo(param.getObsSrcPlanId(), param.getPlanName(), param.getAskId(),
                    param.getPlanType(), h5Model);
            fillCustomerServiceSetting(h5Model);

            return ToolPageResult.success(JsonMapper.writeValueAsString(h5Model));
        } catch (JsonProcessingException e) {
            LOG.message("h5DiyPageApi")
                    .with("levelId", levelId)
                    .with("designId", obsDesignId)
                    .with(e)
                    .warn();
            return ToolPageResult.create(HttpStatus.INTERNAL_SERVER_ERROR, null, "server error");
        }
    }

    /**
     * 处理已有设计的方法，由子类实现
     */
    protected abstract ToolPageResult handleExistingDesign(HttpServletRequest request,
            BimPageParam param,
            Long userId, String obsDesignId, String levelId, H5Model h5Model);

    /**
     * 处理新建设计的方法，由子类实现
     */
    protected abstract ToolPageResult handleNewDesign(HttpServletRequest request,
            BimPageParam param,
            Long userId, Long rootAccountId);

    /**
     * 获取根账户ID
     */
    protected Long getRootAccountId(Long userId) {
        try {
            return businessAccountDb.getRootAccountForBAndC(userId);
        } catch (Exception e) {
            // 忽略异常，当获取 accountId 异常时，也能打开页面，但获取不到商家相关的配置
            LOG.message("error to get account id", e)
                    .with("userId", userId)
                    .error();
            return null;
        }
    }

    /**
     * 检查设计所有者状态
     */
    @Nullable
    protected ToolPageResult checkDesignOwnerStatus(String obsDesignId) {
        try {
            DesignOwnerStatusResult designOwnerStatus =
                    synergyFacade.getDesignOwnerStatus(LongCipher.DEFAULT.decrypt(obsDesignId));
            if (designOwnerStatus != null && !designOwnerStatus.getStatus().isNormal()) {
                return ToolPageResult.redirect(designOwnerStatus.getRedirectUrl());
            }
        } catch (Exception e) {
            // 忽略异常
            LOG.message("getDesignOwnerStatus failed", e)
                    .with("designId", obsDesignId)
                    .error();
        }
        return null;
    }

    /**
     * 填充设计信息
     */
    protected void fillDesignInfo(H5Model h5Model, String obsDesignId, YunDesign yunDesign,
            @Nullable String levelId, boolean isSynergy, final Long copyParentId) {
        final DesignInfo designInfo = new DesignInfo();
        designInfo.setObsDesignId(obsDesignId);
        h5Model.setDesignInfo(designInfo);
        if (yunDesign == null) {
            return;
        }
        designInfo.setObsDesignId(LongCipher.DEFAULT.encrypt(yunDesign.getDesignId()));
        designInfo.setObsPlanId(LongCipher.DEFAULT.encrypt(yunDesign.getPlanId()));
        designInfo.setPlanName(yunDesign.getName());
        if (levelId == null) {
            try {
                levelId = getDefaultLevelId(yunDesign);
            } catch (Exception e) {
                // 捕获异常，继续执行
                LOG.message("getDefaultLevelId failed")
                        .with("designId", yunDesign.getDesignId())
                        .with(e)
                        .warn();
            }
        }
        designInfo.setLevelId(levelId);
        Long currentUserId = UserDb.getUserIdBySession(false);
        designInfo.setIsOwner(Objects.equals(currentUserId, yunDesign.getUserId()));
        designInfo.setIsSynergy(isSynergy);
        if (copyParentId != null) {
            designInfo.setObsParentId(LongCipher.DEFAULT.encrypt(copyParentId));
        }
        if (isSynergy) {
            designInfo.setIsSynergyIsolate(synergyFacade.isIsolateDesign(yunDesign.getDesignId()));
        }
    }

    /**
     * 获取默认层级ID
     */
    @SneakyThrows(DiyManageServiceException.class)
    protected String getDefaultLevelId(YunDesign yunDesign) {
        // levelid为空时，写一个默认1层的levelid进去（通过diyDesignInfo拿到levelid）
        // 优先获取上次打开的levelId
        final Long planId = yunDesign.getPlanId();
        String lastOpenedLevelId = designService.getLastOpenedLevelIdByPlanId(planId);
        if (StringUtils.isNotEmpty(lastOpenedLevelId) &&
                designService.isValidLevelId(yunDesign.getDesignId(), lastOpenedLevelId)) {
            return lastOpenedLevelId;
        } else {
            return yunDesign.generateLevelId();
        }
    }

    /**
     * 重定向到协同错误页面
     */
    @NotNull
    protected ToolPageResult redirectToSynergyErrorPage() {
        URI uri = UriComponentsBuilder.newInstance()
                .uri(URI.create(toadProperties.getLoadingPageErrorRedirectUrl()))
                .queryParam(ERROR_CODE_KEY, YUN_DESIGN_NOT_SUPPORT_ERROR_CODE)
                .build().toUri();
        return ToolPageResult.redirect(uri.toString());
    }

    /**
     * 检查应用访问权限
     */
    protected boolean checkAppAccess(final String obsAppId, final Long userId) {
        if (org.apache.commons.lang.StringUtils.isBlank(obsAppId)) {
            return businessAccessService.checkBimAccess(userId);
        }
        long appId = LongCipher.DEFAULT.decrypt(obsAppId);
        VrcAppIdData vrcAppIdData = vrcService.getByVTypeAndAppId(BIM_VTYPE, appId);
        // 不在记录中的 appid 非法
        if (vrcAppIdData == null) {
            LOG.message("invalid appid for 5.0 tool")
                    .with("appId", appId)
                    .error();
            return false;
        }
        // 无需权限点校验，直接通过
        if (vrcService.skipAPCheck(vrcAppIdData)) {
            LOG.message("no need check access point")
                    .with("appId", appId)
                    .info();
            return true;
        }
        return businessAccessService.checkAccess(userId, (long) vrcAppIdData.getAccessPoint());
    }

    /**
     * 填充基本信息
     */
    protected void fillBasicInfo(HttpServletRequest request, String redirectUrl, Integer stage,
            Long userId, H5Model h5Model) {
        h5Model.setStage(stage);
        h5Model.setProjectStage(toadProperties.getProjectStage());
        h5Model.setRedirectUrl(redirectUrl);
        h5Model.setVrcConfig(toadProperties.getVrcConfig());
        h5Model.setAbTestConfig(
                saaSConfigService.getAbTestResult(BIM_APP_KEY, request.getHeader(COOKIE_HEADER_KEY),
                        userId, IpUtil.getUserIp(request)));
    }

    /**
     * 填充参数化墙面信息
     */
    protected void fillParametricWallInfo(Long userId, Long rootAccountId, Long accountId,
            H5Model h5Model) throws AccessAuthenticatorException {
        h5Model.setParametricWallPanelAccess(saaSConfigService
                .checkBusinessAccessPoint(userId, rootAccountId, accountId,
                        Collections.singletonList(PARAMETRIC_WALL_PANEL_ACCESS_POINT)).toString());
        h5Model.setParametricWallPanelPrivateLibAccess(saaSConfigService
                .checkBusinessAccessPoint(userId, rootAccountId, accountId,
                        Collections.singletonList(PARAMETRIC_WALL_PANEL_PRIVATE_LIB_ACCESS_POINT))
                .toString());
    }

    /**
     * 填充应用信息
     */
    protected void fillAppInfo(H5Model h5Model) {
        final com.qunhe.diybe.module.toolversion.data.H5AppVer appVer =
                new com.qunhe.diybe.module.toolversion.data.H5AppVer();
        appVer.setBranch("dev");
        appVer.setName(BIM_VERSION_NAME);
        appVer.setUrl(BIM_VERSION_URL);

        h5Model.setAppInfo(appVer);
    }

    /**
     * 填充用户信息
     */
    protected void fillUserInfo(Long userId, H5Model h5Model) {
        final User modelUser = new User();

        modelUser.setObsUserId(LongCipher.DEFAULT.encrypt(userId));
        final UserDto user = userDb.getUserBySession();
        if (user != null) {
            Long score = user.getScore();
            if (score != null) {
                modelUser.setScore(score);
            }
            Set<Long> tags = userInfoFacade.getUserInfoTags(userId);
            if (tags != null) {
                modelUser.setUserTags(tags);
            }
            final String name = userInfoFacade.getUserNameByUserId(userId);
            final String nickName = user.getUserName();
            modelUser.setName(nickName);
            h5Model.setUserContactName(name == null ? nickName : name);
        }
        h5Model.setUser(modelUser);
    }

    /**
     * 填充工具JS配置
     */
    protected void fillToolJsConfig(H5Model h5Model) throws JsonProcessingException {
        final BusinessConfig toolLinkConfig = toolLinkConfigCache.getDefaultConfig();
        final FlashCustomizeSetting diyCreate3dPano = new FlashCustomizeSetting();
        final FlashCustomizeSetting renderFeedback = new FlashCustomizeSetting();
        final FlashCustomizeSetting recentChanges = new FlashCustomizeSetting();

        final ToolJsConfig toolJsConfig = new ToolJsConfig();

        if (toolLinkConfig != null) {
            final BusinessConfigMap allToolConfigs = toolLinkConfig.getAllToolConfig();
            final BusinessConfigMap diyConfigs = toolLinkConfig.getDiyConfig();

            try {
                //BusinessConfigKeys中有些常量未更新,但是这些Key确实存在于toolLinksConfig
                final Boolean recentChangesShow = allToolConfigs.get("recentChanges").getShow();
                final Boolean renderFeedbackShow = diyConfigs.get("renderFeedback").getShow();
                final Boolean diyCreate3dPanoShow = diyConfigs.get("diyCreate3dPano").getShow();

                recentChanges.setShow(recentChangesShow);
                renderFeedback.setShow(renderFeedbackShow);
                diyCreate3dPano.setShow(diyCreate3dPanoShow);
            } catch (final Exception e) {
                LOG.message("h5DiyPageApi")
                        .with(e)
                        .with("empty config keys in", "recentChanges")
                        .with("renderFeedback", "diyCreate3dPano")
                        .warn();
                recentChanges.setShow(true);
                renderFeedback.setShow(true);
                diyCreate3dPano.setShow(true);
            }
        } else {
            //set default
            recentChanges.setShow(true);
            renderFeedback.setShow(true);
            diyCreate3dPano.setShow(true);
        }

        toolJsConfig.setDiyCreate3dPano(diyCreate3dPano);
        toolJsConfig.setRecentChanges(recentChanges);
        toolJsConfig.setRenderFeedback(renderFeedback);

        h5Model.setToolJsConfigJson(JsonMapper.writeValueAsString(toolJsConfig));
    }

    /**
     * 填充商家图标
     */
    protected void fillBusinessFavorIcon(Long userId, H5Model h5Model) {
        final com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap businessConfigMap =
                saaSConfigService.getBusinessConfigMap(userId, COMMON_CONFIG_FAVOR_ICON);
        if (businessConfigMap != null && businessConfigMap.isNotEmpty()) {
            final BusinessConfigElement businessConfigElement = businessConfigMap.get(
                    COMMON_CONFIG_FAVOR_ICON);
            if (businessConfigElement != null &&
                    Boolean.TRUE.equals(businessConfigElement.getShow())) {
                final FavorIcon favorIcon = new FavorIcon();
                favorIcon.setPicUrl(businessConfigElement.getPicUrl());
                h5Model.setFavorIcon(favorIcon);
            }
        }
    }

    /**
     * 填充账户信息
     */
    protected void fillAccountInfo(Long userId, Long rootAccountId, H5Model h5Model) {
        final com.qunhe.instdeco.plan.site.data.businessconfig.BusinessConfigMap
                moduleConfigMapOfLang = saaSConfigService.getBusinessConfigMap(userId,
                ALL_TOOL_CONFIG_LANGUAGE_TYPE);
        if (moduleConfigMapOfLang == null || moduleConfigMapOfLang.isEmpty()) {
            return;
        }
        final BusinessConfigElement languageConfig = moduleConfigMapOfLang.get(
                ALL_TOOL_CONFIG_LANGUAGE_TYPE);
        if (languageConfig == null || languageConfig.getStatus() == null) {
            return;
        }
        final RootAccount rootAccount = new RootAccount();
        //0 zh_CN 1 en_US
        if (languageConfig.getStatus() == 1) {
            rootAccount.setLang("en_US");
        } else if (languageConfig.getStatus() == 0) {
            rootAccount.setLang("zh_CN");
        }
        if (rootAccountId != null) {
            final TobBusinessAccount businessAccount =
                    businessAccountDb.getBusinessAccountById(rootAccountId);
            if (businessAccount != null) {
                rootAccount.setBusinessAccountName(businessAccount.getName());
                rootAccount.setObsUserId(LongCipher.DEFAULT.encrypt(businessAccount.getUserId()));
            }
        }
        h5Model.setRootAccount(rootAccount);
    }

    /**
     * 填充方案信息
     */
    protected void fillPlanInfo(String obsSrcPlanId, String planName, String askId, Byte planType,
            H5Model h5Model) {
        final PlanInfoConfig planConfig = new PlanInfoConfig();
        planConfig.setPlanName(planName);
        planConfig.setPlanType(planType);
        final PlanHelpConfig planHelpConfig = new PlanHelpConfig();
        planHelpConfig.setAskId(askId);
        planHelpConfig.setObsSrcPlanId(obsSrcPlanId);
        h5Model.setPlanInfo(new PlanInfo(planConfig, planHelpConfig));
    }

    /**
     * 填充客服设置
     */
    protected void fillCustomerServiceSetting(H5Model h5Model) throws JsonProcessingException {
        final FlashCustomizeSetting customerService = new FlashCustomizeSetting();
        customerService.setShow(true);
        h5Model.setCustomerServiceJson(JsonMapper.writeValueAsString(customerService));
    }
} 