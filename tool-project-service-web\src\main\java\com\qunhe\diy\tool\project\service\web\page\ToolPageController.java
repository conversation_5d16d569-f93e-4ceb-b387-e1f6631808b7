/*
 * ToolProjectController.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.cooperate.annotation.Cooperate;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.common.enums.VrcRegion;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.DiyPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.VrcAppDataVO;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipData;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipVO;
import com.qunhe.diybe.module.representation.model.exception.YunDesignException;
import com.qunhe.hunter.biz.annotation.BizTrace;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.i18n.locale.context.FileMessageSource;
import com.qunhe.i18n.locale.helper.LocaleHelper;
import com.qunhe.instdeco.plan.annotations.LoginPageInterceptor;
import com.qunhe.log.QHLogger;
import com.qunhe.monitor.faros.api.Faros;
import com.qunhe.monitor.faros.api.MetricMeta;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.search.common.Timer;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.BIM_TOOL_PAGE_COOHOM;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.BIM_TOOL_PAGE_KUJIALE;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.BIM_TOOL_RPC_PAGE_COOHOM;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.BIM_TOOL_RPC_PAGE_KUJIALE;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.DIY_TOOL_PAGE_COOHOM;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.DIY_TOOL_PAGE_KUJIALE;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.DIY_TOOL_RPC_PAGE_COOHOM;
import static com.qunhe.diy.tool.project.service.common.constant.ApiConstant.DIY_TOOL_RPC_PAGE_KUJIALE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.APP_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.ASK_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.COOP;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.DESIGN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.FORCE_UPDATE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.LEVEL_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.OBS_SER_PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_ID;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.PLAN_TYPE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_BIM;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.REDIRECT_URL;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.STAGE;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.STAY_DIY;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.TOOL_NAME;
import static com.qunhe.diy.tool.project.service.common.constant.PageParamConstant.TRE;

/**
 * 工具承载页接口
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@SuppressWarnings({ "PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException" })
public class ToolPageController {

    private final static QHLogger LOG = QHLogger.getLogger(ToolPageController.class);

    private static final String CUSTOM_VERIFY_PARAM = "obsorderdesignid";

    private final BimToolPageService bimToolPageService;

    private final DiyToolPageService diyToolPageService;

    private final TrialUserFacade trialUserFacade;

    private final VrcService vrcService;

    private final FileMessageSource fileMessageSource;

    private final ErrorTipService errorTipService;


    @GetMapping(BIM_TOOL_PAGE_KUJIALE)
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @BizTrace(bizName = "打开酷家乐5.0方案承载页")
    public ResponseEntity<?> getKujialeBimToolPageInfo(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId
    ) throws AccessAuthenticatorException {
        ToolPageResult result = getKujialeBimToolPageResult(request, response, obsDesignId,
                redirectUrl, levelId, obsSrcPlanId,
                planName, askId, planType, stage, cooperate, obsAppId);
        return result.toResponseEntity();
    }

    @GetMapping(BIM_TOOL_RPC_PAGE_KUJIALE)
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @BizTrace(bizName = "打开酷家乐5.0方案承载页")
    public ToolPageResult getKujialeBimToolPageResult(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId
    ) throws AccessAuthenticatorException {
        boolean isTrialUser = trialUserFacade.isTrialUser();
        final Long userId = UserDb.getUserIdBySession(true);
        Long orderDesignId = Optional.ofNullable(request.getParameter(CUSTOM_VERIFY_PARAM))
                .map(LongCipher.DEFAULT::decrypt)
                .orElse(null);
        BimPageParam param = BimPageParam.builder()
                .obsDesignId(obsDesignId)
                .redirectUrl(redirectUrl)
                .levelId(levelId)
                .obsSrcPlanId(obsSrcPlanId)
                .planName(planName)
                .askId(askId)
                .planType(planType)
                .cooperate(cooperate)
                .stage(stage)
                .userId(userId)
                .trialUser(isTrialUser)
                .obsAppId(obsAppId)
                .fromCoohom(false)
                .orderDesignId(orderDesignId)
                .build();
        // 识别游客用户
        ToolPageResult result;
        if (isTrialUser) {
            LOG.message("trial user open design page")
                    .with("userId", userId)
                    .with("qhdi", request.getHeader("qhdi"))
                    .with("ip", HunterContext.getContext().get("X-Forwarded-For"))
                    .info();
            Timer timer = Timer.create();
            // 因为pub暂时没法支持同一个bim工具调用不同承载页，需要在这里做隔离和熔断
            // ResponseEntity<?> responseEntity = bimToolPageService.handlePageForTrial(request,
            // param);
            result = bimToolPageService.handlePageForTrial(request, param);
            Faros.histogram(MetricMeta.builder("trial_page_api_duration")
                    .alias("游客工具承载页耗时")
                    .build()).observe((double) timer.elapsedMillis() / 1000);
        } else {
            result = bimToolPageService.handlePageForBim(request, param);
        }
        return result;
    }

    @GetMapping(BIM_TOOL_PAGE_COOHOM)
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @BizTrace(bizName = "打开coohom 5.0方案承载页")
    public ResponseEntity<?> getCoohomBimToolPageInfo(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId
    ) throws AccessAuthenticatorException {
        ToolPageResult result = getCoohomBimToolPageResult(request, response, obsDesignId,
                redirectUrl, levelId, obsSrcPlanId, planName, askId, planType, stage, cooperate,
                obsAppId);
        return result.toResponseEntity();
    }

    @GetMapping(BIM_TOOL_RPC_PAGE_COOHOM)
    @SuppressWarnings("PMD.ExcessiveParameterList")
    @BizTrace(bizName = "打开coohom 5.0方案承载页")
    public ToolPageResult getCoohomBimToolPageResult(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId
    ) throws AccessAuthenticatorException {
        final Long userId = UserDb.getUserIdBySession(true);
        Long orderDesignId = Optional.ofNullable(request.getParameter(CUSTOM_VERIFY_PARAM))
                .map(LongCipher.DEFAULT::decrypt)
                .orElse(null);
        BimPageParam param = BimPageParam.builder()
                .obsDesignId(obsDesignId)
                .redirectUrl(redirectUrl)
                .levelId(levelId)
                .obsSrcPlanId(obsSrcPlanId)
                .planName(planName)
                .askId(askId)
                .planType(planType)
                .cooperate(cooperate)
                .stage(stage)
                .userId(userId)
                .trialUser(false)
                .obsAppId(obsAppId)
                .fromCoohom(true)
                .orderDesignId(orderDesignId)
                .build();
        ToolPageResult result = bimToolPageService.handlePageForBim(request, param);
        return result;
    }

    @GetMapping("/tps/api/vrc_app_config")
    @ResponseBody
    public Map<String, VrcAppDataVO> getVrcAppConfig() {
        return vrcService.getAvailableVrcAppIdList().stream()
                .filter(Objects::nonNull)
                .filter(data -> data.getAppKey() != null)
                .map(data -> {
                    try {
                        VrcAppDataVO vo = new VrcAppDataVO();
                        vo.setName(data.getAppName());
                        vo.setAppIdNum((long) data.getAppId());
                        vo.setAppId(LongCipher.DEFAULT.encrypt((long) data.getAppId()));
                        vo.setVrc(data.getVrc());
                        if (!vrcService.skipAPCheck(data)) {
                            vo.setSaasAP((long) data.getAccessPoint());
                        }
                        vo.setKey(data.getAppKey());
                        if (StringUtils.isNotBlank(data.getRegion())) {
                            vo.setRegion(Arrays.stream(data.getRegion().split(","))
                                    .map(String::trim)
                                    .map(str -> VrcRegion.fromValue(str).getValue())
                                    .collect(Collectors.toList()));
                        }
                        return vo;
                    } catch (Exception e) {
                        // 单条解析失败不影响后续解析
                        LOG.message("parse vrc app config failed", e)
                                .with("appKey", data.getAppKey())
                                .error();
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(VrcAppDataVO::getKey, k -> k, (a, b) -> a));
    }

    @GetMapping("tps/api/load/error-tip")
    @ResponseBody
    public Result<ErrorTipVO> getPageLoadingErrorTip(@RequestParam String id) {
        ErrorTipData errorTipData = errorTipService.get(id);
        String title = fileMessageSource.getMessageWithFallback(
                errorTipData.getErrorTipType().getTitleLocaleId(),
                errorTipData.getTitleArgs(),
                errorTipData.getErrorTipType().getTitleFallbackMessage(),
                LocaleHelper.getLocale());
        String desc = fileMessageSource.getMessageWithFallback(
                errorTipData.getErrorTipType().getDescLocaleId(),
                errorTipData.getDescArgs(), errorTipData.getErrorTipType().getDescFallbackMessage(),
                LocaleHelper.getLocale());
        ErrorTipVO errorTipVO = new ErrorTipVO();
        errorTipVO.setTitle(title);
        errorTipVO.setDesc(desc);
        return Result.ok(errorTipVO);
    }

    @LoginPageInterceptor
    @GetMapping(value = DIY_TOOL_PAGE_KUJIALE)
    @BizTrace(bizName = "打开酷家乐4.0方案承载页")
    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ResponseEntity<?> getKujialeDiyToolPageInfo(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = PLAN_ID, required = false) final String obsPlanId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) final String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = TOOL_NAME, defaultValue = "h5diy", required = false)
            final String toolName,
            @RequestParam(value = STAY_DIY, defaultValue = "false", required = false)
            final Boolean stayDiy,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @RequestParam(value = REDIRECT_BIM, defaultValue = "true") final boolean redirectBim,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId,
            @RequestParam(value = FORCE_UPDATE, required = false, defaultValue = "false")
            final boolean forceUpdate,
            @RequestParam(value = TRE, required = false) List<String> tre)
            throws IOException, YunDesignException, AccessAuthenticatorException {
        ToolPageResult result = getKujialeDiyToolPageResult(request, response, obsDesignId,
                obsPlanId, redirectUrl, levelId, obsSrcPlanId, planName, askId, planType,
                toolName, stayDiy, stage, redirectBim, cooperate, obsAppId, forceUpdate, tre);

        return result.toResponseEntity();
    }

    @LoginPageInterceptor
    @GetMapping(value = DIY_TOOL_RPC_PAGE_KUJIALE)
    @BizTrace(bizName = "打开酷家乐4.0方案承载页")
    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getKujialeDiyToolPageResult(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = PLAN_ID, required = false) final String obsPlanId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) final String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = TOOL_NAME, defaultValue = "h5diy", required = false)
            final String toolName,
            @RequestParam(value = STAY_DIY, defaultValue = "false", required = false)
            final Boolean stayDiy,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @RequestParam(value = REDIRECT_BIM, defaultValue = "true") final boolean redirectBim,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId,
            @RequestParam(value = FORCE_UPDATE, required = false, defaultValue = "false")
            final boolean forceUpdate,

            @RequestParam(value = TRE, required = false) List<String> tre)
            throws IOException, YunDesignException, AccessAuthenticatorException {
        final Long userId = UserDb.getUserIdBySession(true);
        Long orderDesignId = Optional.ofNullable(request.getParameter(CUSTOM_VERIFY_PARAM))
                .map(LongCipher.DEFAULT::decrypt)
                .orElse(null);
        DiyPageParam param = DiyPageParam.builder()
                .obsDesignId(obsDesignId)
                .obsPlanId(obsPlanId)
                .redirectUrl(redirectUrl)
                .levelId(levelId)
                .obsSrcPlanId(obsSrcPlanId)
                .planName(planName)
                .askId(askId)
                .stayDiy(stayDiy)
                .toolName(toolName)
                .planType(planType)
                .cooperate(cooperate)
                .stage(stage)
                .userId(userId)
                .obsAppId(obsAppId)
                .fromCoohom(false)
                .orderDesignId(orderDesignId)
                .redirectBim(redirectBim)
                .tre(tre)
                .forceUpdate(forceUpdate)
                .build();
        ToolPageResult result = diyToolPageService.handlePage(request, response, param);
        return result;
    }

    @LoginPageInterceptor
    @GetMapping(value = DIY_TOOL_PAGE_COOHOM)
    @BizTrace(bizName = "打开coohom 4.0方案承载页")
    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ResponseEntity<?> getCoohomDiyToolPageInfo(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = PLAN_ID, required = false) final String obsPlanId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) final String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = TOOL_NAME, defaultValue = "h5diy", required = false)
            final String toolName,
            @RequestParam(value = STAY_DIY, defaultValue = "false", required = false)
            final Boolean stayDiy,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @RequestParam(value = REDIRECT_BIM, defaultValue = "true") final boolean redirectBim,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId,
            @RequestParam(value = FORCE_UPDATE, required = false, defaultValue = "false")
            final boolean forceUpdate,
            @RequestParam(value = TRE, required = false) List<String> tre)
            throws IOException, YunDesignException, AccessAuthenticatorException {
        ToolPageResult result = getCoohomDiyToolPageResult(request, response, obsDesignId,
                obsPlanId, redirectUrl, levelId, obsSrcPlanId, planName, askId, planType,
                toolName, stayDiy, stage, redirectBim, cooperate, obsAppId, forceUpdate, tre);
        return result.toResponseEntity();
    }

    @LoginPageInterceptor
    @GetMapping(value = DIY_TOOL_RPC_PAGE_COOHOM)
    @BizTrace(bizName = "打开coohom 4.0方案承载页")
    @SuppressWarnings("PMD.ExcessiveParameterList")
    public ToolPageResult getCoohomDiyToolPageResult(final HttpServletRequest request,
            final HttpServletResponse response,
            @RequestParam(value = DESIGN_ID, required = false) final String obsDesignId,
            @RequestParam(value = PLAN_ID, required = false) final String obsPlanId,
            @RequestParam(value = REDIRECT_URL, required = false) final String redirectUrl,
            @RequestParam(value = LEVEL_ID, required = false) final String levelId,
            @RequestParam(value = OBS_SER_PLAN_ID, required = false) final String obsSrcPlanId,
            @RequestParam(value = PLAN_NAME, required = false) final String planName,
            @RequestParam(value = ASK_ID, required = false) final String askId,
            @RequestParam(value = PLAN_TYPE, required = false) final Byte planType,
            @RequestParam(value = TOOL_NAME, defaultValue = "h5diy", required = false)
            final String toolName,
            @RequestParam(value = STAY_DIY, defaultValue = "false", required = false)
            final Boolean stayDiy,
            @RequestParam(value = STAGE, required = false) final Integer stage,
            @RequestParam(value = REDIRECT_BIM, defaultValue = "true") final boolean redirectBim,
            @Cooperate
            @RequestParam(value = COOP, defaultValue = "false") final boolean cooperate,
            @RequestParam(value = APP_ID, required = false) final String obsAppId,
            @RequestParam(value = FORCE_UPDATE, required = false, defaultValue = "false")
            final boolean forceUpdate,

            @RequestParam(value = TRE, required = false) List<String> tre)
            throws IOException, YunDesignException, AccessAuthenticatorException {
        final Long userId = UserDb.getUserIdBySession(true);
        Long orderDesignId = Optional.ofNullable(request.getParameter(CUSTOM_VERIFY_PARAM))
                .map(LongCipher.DEFAULT::decrypt)
                .orElse(null);
        DiyPageParam param = DiyPageParam.builder()
                .obsDesignId(obsDesignId)
                .obsPlanId(obsPlanId)
                .redirectUrl(redirectUrl)
                .levelId(levelId)
                .obsSrcPlanId(obsSrcPlanId)
                .planName(planName)
                .askId(askId)
                .stayDiy(stayDiy)
                .toolName(toolName)
                .planType(planType)
                .cooperate(cooperate)
                .stage(stage)
                .userId(userId)
                .obsAppId(obsAppId)
                .fromCoohom(true)
                .orderDesignId(orderDesignId)
                .redirectBim(redirectBim)
                .tre(tre)
                .forceUpdate(forceUpdate)
                .build();
        ToolPageResult result = diyToolPageService.handlePage(request, response, param);
        return result;
    }


}
