/*
 * RedisProperties.java
 * Copyright 2019 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * Created on 2018.10.31.
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "qunhe.redis")
public class RedisProperties {
    public static final int TIMEOUT = 5000;
    public static final int MAX_TOTAL = 200;
    public static final int MAX_IDLE = 100;
    public static final int MAX_WAIT_MILLIS = 1000;

    private Redis diyCacheRedis;
    private Redis diyRedis;

    @Getter
    @Setter
    public static class Redis {
        private String redisId;
        private int database;

        /**
         * 用于 hunter
         */
        private String serviceName;

        /**
         * 默认超时时间（毫秒）
         */
        private long timeout = TIMEOUT;


        private int maxIdle = MAX_IDLE;
        private int maxTotal = MAX_TOTAL;
        private int maxWaitMillis = MAX_WAIT_MILLIS;
        private boolean testOnBorrow = true;
    }
}
