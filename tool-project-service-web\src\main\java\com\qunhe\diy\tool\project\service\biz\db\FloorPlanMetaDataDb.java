/*
 * FloorPlanMetaDataDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb.FloorPlanMetaDataMapper;
import com.qunhe.diy.tool.project.service.common.data.FloorPlanMetaData;
import com.qunhe.log.QHLogger;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Function:
 *
 * <AUTHOR>
 * 2024/5/31
 */
@Service
@RequiredArgsConstructor
public class FloorPlanMetaDataDb {

    private static final QHLogger LOG = QHLogger.getLogger(FloorPlanMetaDataDb.class);

    private final FloorPlanMetaDataMapper floorPlanMetaDataMapper;

    public void addOrUpdateDwDrawingVersion(final Long planId, final String dwDrawingVersion) {
        floorPlanMetaDataMapper.addOrUpdateDwDrawingVersion(planId, dwDrawingVersion);
    }

    public void copyDwDrawingVersion(final Long srcPlanId, final Long targetPlanId) {
        FloorPlanMetaData floorPlanMetaData = floorPlanMetaDataMapper.selectByPlanId(srcPlanId);
        // 复制projectDesign时会走一遍create的逻辑，此时的dwDrawingVersion已经为1
        // 复制后方案的dwDrawingVersion需要和源方案一致
        if (floorPlanMetaData != null) {
            LOG.message("copyDwDrawingVersion")
                    .with("srcPlanId", srcPlanId)
                    .with("targetPlanId", targetPlanId)
                    .with("dwDrawingVersion", floorPlanMetaData.getDwDrawingVersion())
                    .info();
            floorPlanMetaDataMapper.addOrUpdateDwDrawingVersion(targetPlanId,
                    floorPlanMetaData.getDwDrawingVersion());
        }
    }

}

