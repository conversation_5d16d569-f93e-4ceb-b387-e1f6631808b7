# 文档

`Pilot`官方文档 [http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/)

修改 /_appConfig/cicd.yml，设置启动参数。


****

# Pilot 公共 API

`Pilot` 提供了几个公共 `API`，用来获取应用部署的环境信息。

[http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-common-api](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-common-api)

# Pilot OPS

访问[https://coops.qunhequnhe.com/pilot#/ops](https://coops.qunhequnhe.com/pilot#/ops) 查看应用中间件版本。

使用方式 [http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-ops](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-ops)

# 不同环境配置内容

`Pilot` 推荐将区分环境的配置托管到 `toad`;

# 多数据源配置

[http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-multiple-dataSource](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-multiple-dataSource)

# 常见问题

[http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-qa](http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/pilot-qa)

# 其它文档

SOA      http://manual.k8s-new.qunhequnhe.com/soa/1.0.0/                       
 
HUNTER   http://coops.gitlabpages.qunhequnhe.com/hunter-doc/

Pilot    http://manual.k8s-new.qunhequnhe.com/pilot/1.0.0/            
          
TDDL     http://manual.k8s-new.qunhequnhe.com/tddl-doc/1.0.0/         
          
TOAD     http://coops.gitlabpages.qunhequnhe.com/toad-doc/         
             
SPORE    http://manual.k8s-new.qunhequnhe.com/redis-doc/1.0.0/spore/usage  
