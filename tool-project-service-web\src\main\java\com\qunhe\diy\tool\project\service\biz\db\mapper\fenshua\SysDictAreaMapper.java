/*
 * SysDictAreaMapper.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua;

import com.qunhe.diy.tool.project.service.common.param.CommunityInfo;
import com.qunhe.instdeco.plan.data.SysDictArea;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@SuppressWarnings("PMD")
public interface SysDictAreaMapper {
    CommunityInfo getPidAndFullNameById(@Param("areaId") int areaId);

    String getFullName(@Param("areaId") Long var1);

    SysDictArea getById(@Param("areaId") Long var1);

    List<SysDictArea> getByName(@Param("name") String var1, @Param("level") Integer var2);

    List<SysDictArea> getChildren(@Param("areaId") Long var1);
}
