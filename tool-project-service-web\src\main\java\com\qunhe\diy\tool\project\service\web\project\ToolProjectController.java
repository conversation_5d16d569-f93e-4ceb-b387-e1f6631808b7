/*
 * ToolProjectController.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.project;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.common.constant.ApiConstant;
import com.qunhe.diy.tool.project.service.common.data.ToolProjectHomeDesign;
import com.qunhe.diy.tool.project.service.common.enums.ErrorCode;
import com.qunhe.diy.tool.project.service.common.exception.ToolProjectCreateException;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diy.tool.project.service.web.convert.ParamConvertUtil;
import com.qunhe.diy.tool.project.service.web.request.ToolProjectSaveRequest;
import com.qunhe.diy.tool.project.service.web.utils.RequestUtils;
import com.qunhe.hunter.biz.annotation.BizTrace;
import com.qunhe.i18n.locale.annotation.Locale;
import com.qunhe.i18n.locale.annotation.LocaleSupport;
import com.qunhe.instdeco.diy.apiencrypt.data.EncryptData;
import com.qunhe.instdeco.plan.annotations.LoginApiInterceptor;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import com.qunhe.web.standard.data.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.SignatureDeclareThrowsException"})
public class ToolProjectController {

    private final static QHLogger LOG = QHLogger.getLogger(ToolProjectController.class);

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private final ToolProjectService toolProjectService;

    private final ToadProperties toadProperties;

    @Autowired
    public ToolProjectController(final ToolProjectService toolProjectService,
            final ToadProperties toadProperties) {
        this.toolProjectService = toolProjectService;
        this.toadProperties = toadProperties;
    }

    @LoginApiInterceptor
    @PostMapping(value = ApiConstant.PROJECT_CREATE_ENCRYPT_API)
    @LocaleSupport
    @BizTrace(bizName = "创建方案")
    public EncryptData createProject(
            @Locale
            @RequestParam(value = "locale", required = false) final String locale,
            @RequestBody final EncryptData encryptData, HttpServletRequest request)
            throws Exception {
        final boolean isCoohom = RequestUtils.isCoohom(request);

        ToolProjectSaveRequest saveRequest = MAPPER.readValue(encryptData.getBody(),
                ToolProjectSaveRequest.class);
        ParamConvertUtil.validRequest(saveRequest, toadProperties.isUserRangeCheck());

        try {
            ToolProjectSaveParam toolProjectSaveParam = ParamConvertUtil.convert(saveRequest);
            ToolProjectHomeDesign result = toolProjectService.create(toolProjectSaveParam,
                    isCoohom);
            return new EncryptData(MAPPER.writeValueAsString(Result.ok(
                    ParamConvertUtil.convert2Response(result))));

        } catch (ToolProjectCreateException e) {
            return new EncryptData(MAPPER.writeValueAsString(
                    Result.error(ErrorCode.CREATE_PROJECT_COUNT_ERROR.getCode(),
                            ErrorCode.CREATE_PROJECT_COUNT_ERROR.getMsg())));
        }
    }

    @GetMapping(value = ApiConstant.PROJECT_SEARCHABLE_STATUS)
    public Result<Integer> getDesignSearchableStatus(@RequestParam("design_id") String designId) {
        try {
            return Result.ok(toolProjectService.getDesignSearchableStatus(LongCipher.DEFAULT.decrypt(designId)).getValue());
        } catch (Exception e) {
            // 捕获异常，返回错误信息
            LOG.message("getDesignSearchableStatus error", e)
                    .with("designId", designId)
                    .error();
            return Result.error("系统异常");
        }
    }

}
