/*
 * ToolPageResult.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PageServiceResult 类用于封装服务层返回的结果信息，包括状态码、响应头、响应体和重定向URL。
 * <AUTHOR>
 * @date 2025/4/29
 */
@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ToolPageResult {
    private static final Logger LOGGER = LoggerFactory.getLogger(ToolPageResult.class);

    private int statusCode;
    @Builder.Default
    private Map<String, List<String>> responseHeaders = new HashMap<>();
    private Object body;
    private String redirectUrl; // 用于重定向的URL字符串

    /**
     * 创建一个表示成功的 PageServiceResult，包含响应体。
     * @param body 响应体
     * @return PageServiceResult 实例
     */
    public static ToolPageResult success(Object body) {
        return ToolPageResult.builder()
                .statusCode(HttpStatus.OK.value())
                .body(body)
                .build();
    }

    /**
     * 创建一个表示成功的 PageServiceResult，包含响应体和自定义头部。
     * @param body 响应体
     * @param springHeaders Spring HttpHeaders
     * @return PageServiceResult 实例
     */
    public static ToolPageResult success(Object body, HttpHeaders springHeaders) {
        Map<String, List<String>> serializableHeaders = new HashMap<>(1);
        if (springHeaders != null) {
            springHeaders.forEach(serializableHeaders::put);
        }
        return ToolPageResult.builder()
                .statusCode(HttpStatus.OK.value())
                .body(body)
                .responseHeaders(serializableHeaders)
                .build();
    }

    /**
     * 创建一个表示重定向的 PageServiceResult。
     * @param redirectUrl 重定向的目标URL
     * @return PageServiceResult 实例
     */
    public static ToolPageResult redirect(String redirectUrl) {
        return ToolPageResult.builder()
                .statusCode(HttpStatus.FOUND.value()) // Default to 302 Found for redirects
                .redirectUrl(redirectUrl)
                .build();
    }

    /**
     * 创建一个具有特定状态、头部和响应体的 PageServiceResult。
     * @param status HTTP状态
     * @param springHeaders Spring HttpHeaders
     * @param body 响应体
     * @return PageServiceResult 实例
     */
    public static ToolPageResult create(HttpStatus status, HttpHeaders springHeaders,
            Object body) {
        Map<String, List<String>> serializableHeaders = new HashMap<>(1);
        if (springHeaders != null) {
            serializableHeaders.putAll(springHeaders);
        }
        return ToolPageResult.builder()
                .statusCode(status.value())
                .responseHeaders(serializableHeaders)
                .body(body)
                .build();
    }

    /**
     * Converts this PageServiceResult to a Spring ResponseEntity.
     * This method is intended for use in the controller layer of the *current* service.
     * The receiving service would have its own logic to interpret the deserialized
     * PageServiceResult.
     */
    public ResponseEntity<?> toResponseEntity() {
        HttpHeaders springHttpHeaders = new HttpHeaders();
        if (this.responseHeaders != null) {
            springHttpHeaders.putAll(this.responseHeaders);
        }

        HttpStatus resolvedStatus = HttpStatus.valueOf(this.statusCode);
        if (resolvedStatus == null) {
            // This indicates an issue: PageServiceResult was constructed with an invalid
            // statusCode.
            // Log this as an error. For safety, default to INTERNAL_SERVER_ERROR if not a redirect.
            LOGGER.error("PageServiceResult: Unresolvable statusCode {}. Defaulting status.",
                    this.statusCode);
            resolvedStatus =
                    (this.redirectUrl != null && !this.redirectUrl.isEmpty()) ? HttpStatus.FOUND
                            : HttpStatus.INTERNAL_SERVER_ERROR;
        }

        if (this.redirectUrl != null && !this.redirectUrl.isEmpty()) {
            try {
                springHttpHeaders.setLocation(new URI(this.redirectUrl));
                // Body is typically null for redirects. The status code (e.g., 302) is in
                // resolvedStatus.
                return new ResponseEntity<>(null, springHttpHeaders, resolvedStatus);
            } catch (URISyntaxException e) {
                // This should ideally be prevented by validation when redirectUrl is set, or
                // during construction.
                LOGGER.error("PageServiceResult: Invalid redirect URL syntax {}. Error: {}",
                        this.redirectUrl, e.getMessage(), e);
                return new ResponseEntity<>(
                        "Invalid redirect URL format in PageServiceResult: " + this.redirectUrl,
                        HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        return new ResponseEntity<>(this.body, springHttpHeaders, resolvedStatus);
    }

}