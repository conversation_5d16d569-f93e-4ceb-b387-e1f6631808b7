/*
 * ProjectHandoverService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverDesignMessage;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverFailDetail;
import com.qunhe.diy.tool.project.service.biz.properties.HandoverJobRspMessage;
import com.qunhe.diy.tool.project.service.biz.properties.TaskInfo;
import com.qunhe.log.QHLogger;
import com.qunhe.middleware.rocketmq.client.QunheMQProducer;
import com.qunhe.project.platform.project.search.client.DesignTagClient;
import com.qunhe.project.platform.project.search.common.bo.Tag;
import com.qunhe.project.platform.project.search.common.param.batch.BatchParam;
import com.qunhe.project.platform.project.search.common.param.batch.QueryCondition;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.*;
import javax.annotation.Resource;

/**
 * Function: 方案移交服务
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.CognitiveComplexity"})
public class ProjectHandoverService {

    public static final QHLogger LOG = QHLogger.getLogger(ProjectHandoverService.class);

    private static final String SAAS_MARK_TOKEN = "c3Q7dLErF9BzMdT4bES8w7MDQhIJZ811";

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final DesignTagClient designTagClient;

    @Autowired
    ToadProperties toadProperties;

    @Autowired
    private QunheMQProducer userMQProducer;

    @Autowired
    private QunheMQProducer designMQProducer;

    @Autowired
    ProjectAuthService projectAuthService;

    @Resource(name = "handoverExecutor")
    private ExecutorService handoverExecutor;

    public ProjectHandoverService(
            final ProjectDesignFacadeDb projectDesignFacadeDb,
            final DesignTagClient designTagClient) {
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.designTagClient = designTagClient;
    }

    @Async("handoverExecutor")
    public void handoverProject(final Long toUserId, final List<Long> fromUserIds,
            final Long taskId) {
        List<Long> errorUserIds = new CopyOnWriteArrayList<>();
        List<HandoverFailDetail> failDetails = new CopyOnWriteArrayList<>();
        Integer totalSize = 0;
        for (Long fromUserId : fromUserIds) {
            unMarkByUserId(fromUserId);
            totalSize += handoverByUser(toUserId, taskId, errorUserIds, failDetails, fromUserId);
        }
        for (Long errorUser : errorUserIds) {
            //type1是用户级别
            failDetails.add(HandoverFailDetail.builder().userId(errorUser.toString()).name(
                    "用户级别错误").Reason("轮询发生故障，建议重试").type(2).build());
        }
        //填充返回消息,category是在用户方写死的。
        HandoverJobRspMessage handoverJobRspMessage =
                fillHandoverJobRsp(HandoverJobRspMessage.builder().category(1).taskId(taskId)
                        .taskInfo(TaskInfo.builder().totalCount(totalSize).failedList(failDetails)
                                .build()).build());
        sendFeedbackMessage(handoverJobRspMessage, toUserId);
    }

    private void unMarkByUserId(final Long fromUserId) {
        //因为清空标签这一步操作实际上不会影响主流业务，所以这个故障仅进行日志报错
        try {
            Tag tag = new Tag();
            //仅对商家后台的标签进行清空
            tag.setBusinessGroupId(2L);
            List<Tag> tags = Lists.newArrayList(tag);
            designTagClient.batchSyncReplace(BatchParam.builder().token(SAAS_MARK_TOKEN)
                    .queryCondition(
                            QueryCondition.builder().userId(fromUserId).build()).tagList(tags)
                    .build());
        } catch (Exception e) {
            // 用户标签清空失败，不影响业务，仅记录日志
            LOG.message("UnMark fail", e).with("userId",
                    fromUserId).error();
        }
    }

    private int handoverByUser(final Long toUserId, final Long taskId,
            final List<Long> errorUserIds,
            final List<HandoverFailDetail> failDetails, final Long fromUserId) {
        int totalSize = 0;
        try {
            List<Long> designIds = projectDesignFacadeDb.getDesignsByUserId(fromUserId);
            totalSize += designIds.size();
            List<Future<Long>> resultList = new ArrayList<>();
            for (Long designId : designIds) {
                Task task = new Task(projectDesignFacadeDb, projectAuthService, toUserId, fromUserId, designId,
                        failDetails);
                resultList.add(handoverExecutor.submit(task));
            }
            long startTime = System.currentTimeMillis();
            while (!resultList.isEmpty()) {
                Iterator<Future<Long>> iterable = resultList.iterator();
                while (iterable.hasNext()) {
                    Future<Long> future = iterable.next();
                    if (future.isDone() && !future.isCancelled()) {
                        Long designId = future.get();
                        iterable.remove();
                        if (failDetails.stream().noneMatch(x -> x.getId().equals(designId))) {
                            //向下游服务广播消息
                            LOG.message("开始发送消息").with("designId",
                                    designId).info();
                            sendDesignMessage(HandoverDesignMessage.builder().fromUserId(
                                            fromUserId).toUserId(toUserId).designId(designId.toString())
                                    .taskId(taskId.toString()).build());
                            LOG.message("消息发送成功").with("designId",
                                    designId).info();
                        }
                    } else {
                        //这里休息10毫秒，防止cpu工作过快
                        Thread.sleep(100);
                    }
                }
                //每个任务运行时间限制5分钟,超时任务默认成功
                if (System.currentTimeMillis() - startTime > 300000) {
                    break;
                }
            }
        } catch (Exception e) {
            // 单个失败，不影响其他用户，仅记录日志
            LOG.message("HandoverByUser fail", e).with("fromUserId",
                    fromUserId).with("taskId", taskId).warn();
            errorUserIds.add(fromUserId);
        }
        return totalSize;
    }


    public HandoverJobRspMessage fillHandoverJobRsp(
            final HandoverJobRspMessage handoverJobRspMessage) {
        try {
            List<HandoverFailDetail> handoverFailDetails =
                    handoverJobRspMessage.getTaskInfo().getFailedList();
            for (HandoverFailDetail handoverFailDetail : handoverFailDetails) {
                if (handoverFailDetail.getType().equals(2)) {
                    handoverFailDetail.setName(projectDesignFacadeDb
                            .getProjectByDesignId(Long.valueOf(handoverFailDetail.getId()))
                            .getDesignName());
                }
            }
            TaskInfo taskInfo = handoverJobRspMessage.getTaskInfo();
            taskInfo.setFailedList(handoverFailDetails);
            handoverJobRspMessage.setTaskInfo(taskInfo);
        } catch (Exception e) {
            // 不阻碍主流程，仅记录日志
            LOG.message("fill HandoverJobRsp fail", e).with("taskTd",
                    handoverJobRspMessage.getTaskId()).error();
        }
        return handoverJobRspMessage;
    }

    public void sendFeedbackMessage(final HandoverJobRspMessage handoverJobRspMessage,
            Long toUserId) {
        try {
            String json = JSON.toJSONString(handoverJobRspMessage);
            //这个地方应该是可以转变的
            userMQProducer.send(
                    new Message(toadProperties.getFeedbackTopic(), null, toUserId.toString(),
                            json.getBytes(
                                    StandardCharsets.UTF_8)));
        } catch (Exception e) {
            // 不阻碍主流程，仅记录日志
            LOG.message("sendFeedbackMessage", e).with("taskId",
                    handoverJobRspMessage.getTaskId()).error();
        }
    }

    public void sendDesignMessage(final HandoverDesignMessage handoverDesignMessage) {
        try {
            String json = JSON.toJSONString(handoverDesignMessage);
            //这个地方应该是可以转变的
            designMQProducer.send(new Message(toadProperties.getProjectHandoverTopic(), null,
                    handoverDesignMessage.getFromUserId().toString(), json.getBytes(
                    StandardCharsets.UTF_8)));
        } catch (Exception e) {
            // 发送失败，不影响主流程，仅记录日志
            LOG.message("sendDesignMessage", e).with("designId",
                    handoverDesignMessage.getDesignId()).error();
        }
    }

}


class Task implements Callable<Long> {


    private final ProjectDesignFacadeDb projectDesignFacadeDb;
    private final ProjectAuthService projectAuthService;
    private final Long fromUserId;
    private final Long toUserId;
    private final Long designId;
    List<HandoverFailDetail> failDetails;

    Task(final ProjectDesignFacadeDb projectDesignFacadeDb, ProjectAuthService projectAuthService, final Long toUserId,
            final Long fromUserId, final Long designId, List<HandoverFailDetail> failDetails) {
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.projectAuthService = projectAuthService;
        this.fromUserId = fromUserId;
        this.toUserId = toUserId;
        this.designId = designId;
        this.failDetails = failDetails;
    }

    @Override
    public Long call() {
        try {
            if (!projectDesignFacadeDb.switchDesignUser(fromUserId, toUserId, designId)) {
                failDetails.add(HandoverFailDetail.builder().userId(
                        fromUserId.toString()).Reason(
                        "方案移交超时").id(designId.toString()).type(1).build());
            }
            try {
                projectAuthService.transferUserAuth(fromUserId, toUserId, designId);
            } catch (Exception e) {
                // 变更权限有异常，则做一个补偿操作：将 userId 再变更回来
                projectDesignFacadeDb.switchDesignUser(toUserId, fromUserId, designId);
                ProjectHandoverService.LOG.message("方案移交切换用户权限失败", e)
                        .with("designId", designId)
                        .with("fromUserId", fromUserId)
                        .with("toUserId", toUserId)
                        .error();
                failDetails.add(HandoverFailDetail.builder().userId(
                        fromUserId.toString()).Reason(
                        "方案移交切换用户权限失败").id(designId.toString()).type(1).build());
                return designId;
            }

        } catch (Exception e) {
            // 封装状态
            ProjectHandoverService.LOG.message("方案移交切换用户失败", e).with("designId", designId).error();
            failDetails.add(HandoverFailDetail.builder().userId(
                    fromUserId.toString()).Reason(
                    "方案移交超时").id(designId.toString()).type(1).build());
            return designId;
        }
        return designId;
    }
}