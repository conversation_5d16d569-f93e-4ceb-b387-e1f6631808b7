/*
 * DesignService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.db.DesignMetaDataDb;
import com.qunhe.diy.tool.project.service.biz.db.HomeDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.web.page.data.CoohomCreateDesignSwitch;
import com.qunhe.diybe.dms.data.HomeDesignData;
import com.qunhe.diybe.dms.data.LevelInfo;
import com.qunhe.diybe.dms.exception.DiyManageServiceException;
import com.qunhe.kam.client.layoutdesigncommon.LayoutMultiDesignClient;
import com.qunhe.kam.client.layoutdesigncommon.exception.LayoutDesignCommonException;
import com.qunhe.log.QHLogger;
import com.qunhe.mdw.sth.core.util.SwitchUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
@RequiredArgsConstructor
public class DesignService {


    private static final QHLogger LOG = QHLogger.getLogger(DesignService.class);

    private final LayoutMultiDesignClient layoutMultiDesignClient;

    private final HomeDesignFacadeDb homeDesignFacadeDb;

    private final DesignMetaDataDb designMetaDataDb;

    /**
     * 获取当前方案的多方案主方案id
     *
     * @param designId 当前方案id
     * @return 对应多方案内的主方案id
     */
    @SentinelResource(value = "getMainDesignId", fallback = "getMainDesignIdFallBack")
    @SneakyThrows(LayoutDesignCommonException.class)
    public Long getMainDesignId(Long designId, Long userId) {
        return layoutMultiDesignClient.getMainDesignId(designId, userId);
    }

    Long getMainDesignIdFallBack(Long designId, Long userId, Throwable e) {
        LOG.message("getMainDesignId降级", e)
                .with("designId", designId)
                .with("userId", userId)
                .info();
        return null;
    }

    public boolean isValidLevelId(final Long designId, final String levelId)
            throws DiyManageServiceException {
        final HomeDesignData homeDesign = homeDesignFacadeDb.getHomeDesign(designId);
        final Set<String> levelIds = homeDesign.getLevelInfos().stream().map(LevelInfo::getLevelId)
                .collect(Collectors.toSet());
        return levelIds.contains(levelId);
    }

    @SentinelResource(value = "getLastOpenedLevelIdByPlanId",
            fallback = "getLastOpenedLevelIdByPlanIdFallback")
    public String getLastOpenedLevelIdByPlanId(Long planId) {
        return designMetaDataDb.getLastOpenedLevelId(planId);
    }

    public String getLastOpenedLevelIdByPlanIdFallback(Long planId, Throwable e) {
        LOG.message("getLastOpenedLevelIdByPlanIdFallback - error", e)
                .with("planId", planId)
                .error();
        return null;
    }

    /**
     * 判断是否匹配Coohom创建设计的条件
     */
    public boolean isCoohomCreateDesignMatch(Long rootAccountId) {
        try {
            return SwitchUtil.isOn("coohom.createDesign.switch", false,
                    CoohomCreateDesignSwitch.class, s -> {
                        if (!s.isOpen()) {
                            return false;
                        }
                        if (CollectionUtils.isNotEmpty(s.getWhiteUserIdList())) {
                            boolean result = s.getWhiteUserIdList().contains(
                                    UserDb.getUserIdBySession());
                            if (result) {
                                LOG.message("coohom.createDesign.switch - white user id match")
                                        .with("userId", UserDb.getUserIdBySession())
                                        .with("rootAccountId", rootAccountId)
                                        .info();
                            }
                            return result;
                        } else {
                            if (CollectionUtils.isNotEmpty(s.getWhiteAccountList()) &&
                                    s.getWhiteAccountList().contains(rootAccountId)) {
                                LOG.message("coohom.createDesign.switch - white account id match")
                                        .with("userId", UserDb.getUserIdBySession())
                                        .with("rootAccountId", rootAccountId)
                                        .info();
                                return true;
                            }
                            return false;
                        }
                    });
        } catch (Exception e) {
            LOG.message("isCoohomCreateDesignMatch - error", e)
                    .error();
            return false;
        }
    }

}
