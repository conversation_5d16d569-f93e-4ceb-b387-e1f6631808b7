<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ starter-pom.xml
  ~ Copyright 2018 Qunhe Tech, all rights reserved.
  ~ Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tool-project-service-common</artifactId>
    <version>0.0.18-SNAPSHOT</version>

    <parent>
        <groupId>com.qunhe.diy</groupId>
        <artifactId>tool-project-service-parent</artifactId>
        <version>0.0.18-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.middleplatform</groupId>
            <artifactId>project-management-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>diymanage-data</artifactId>
                    <groupId>com.qunhe.diybe</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>diymanage-data</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
            <groupId>com.qunhe.diybe</groupId>
            <version>2.3.18</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.utils</groupId>
            <artifactId>webstandard</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunhe.diybe.module</groupId>
            <artifactId>restapi-core-common</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>qunhe-releases</id>
            <name>Qunhe Release Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>qunhe-snapshots</id>
            <name>Qunhe Snapshot Repository</name>
            <url>http://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
