/*
 * BatchRemoveOrRestoreServiceTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.diy.tool.project.service.biz.db.BatchOperationRecordDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.common.data.BatchOperationData;
import com.qunhe.diy.tool.project.service.common.enums.BatchOperationStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
public class BatchRemoveOrRestoreServiceTest {

    @Mock
    ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    GeneralProperties generalProperties;
    @Mock
    BatchOperationRecordDb batchOperationRecordDb;

    @InjectMocks
    BatchRemoveOrRestoreService batchRemoveOrRestoreService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testBatchOperateDesignsInPartition_delete_success() {
        final String recordId = "request1";
        final List<Long> designIds = Arrays.asList(1L);
        final List<Long> totalDealedIdList = new ArrayList<>(1);

        when(projectDesignFacadeDb.batchDeleteProject(designIds)).thenReturn(1);
        when(batchOperationRecordDb.updateCompleteCountAndStatus(1,
                BatchOperationStatusEnum.GOING.getCode(), recordId)).thenReturn(1);

        batchRemoveOrRestoreService.batchOperateDesignsInPartition(recordId,
                designIds, totalDealedIdList, false);
        verify(batchOperationRecordDb, times(1)).updateCompleteCountAndStatus(1,
                BatchOperationStatusEnum.GOING.getCode(), recordId);
    }

    @Test
    public void testBatchOperateDesignsInPartition_delete_failed() {
        final String recordId = "request1";
        final List<Long> designIds = Arrays.asList(1L);
        final List<Long> totalDealedIdList = new ArrayList<>(1);

        when(projectDesignFacadeDb.batchDeleteProject(designIds)).thenReturn(0);

        batchRemoveOrRestoreService.batchOperateDesignsInPartition(recordId, designIds,
                totalDealedIdList, false);
        verify(projectDesignFacadeDb, times(1)).batchDeleteProject(designIds);
        verify(batchOperationRecordDb, times(0)).updateCompleteCountAndStatus(0,
                BatchOperationStatusEnum.GOING.getCode(), recordId);
    }

    @Test
    public void testSingleBatchOperateProjects_success() {
        final String recordId = "request2";
        final List<Long> designIds = Arrays.asList(1L, 2L, 3L, 257L);
        final List<Long> totalDealedIdList = new ArrayList<>(4);
        BatchOperationData batchOperationData = new BatchOperationData();
        batchOperationData.setUserId(11L);
        batchOperationData.setOriginCount(100);
        batchOperationData.setRecordId(recordId);

        when(generalProperties.getMaxPatitionSizeForBatchOperate()).thenReturn(1);
        when(projectDesignFacadeDb.batchDeleteProject(any())).thenReturn(0);

        batchRemoveOrRestoreService.singleBatchOperateProjects(recordId, batchOperationData,
                designIds,
                totalDealedIdList);
        //分成4批
        verify(projectDesignFacadeDb, times(4)).batchDeleteProject(any());
    }
}
