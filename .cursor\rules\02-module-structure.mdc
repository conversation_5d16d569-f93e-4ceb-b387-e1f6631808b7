---
description:
globs:
alwaysApply: false
---
# 项目模块结构

## 模块说明

### tool-project-service-common
该模块包含项目中共享的基础代码，如：
- 实体类 (Entity)
- 常量定义 (Constants)
- 枚举类型 (Enums)
- 工具类 (Utils)
- 异常类 (Exceptions)

### tool-project-service-client
该模块提供给其他服务调用的客户端接口，包含：
- API 接口定义
- DTO 对象
- 客户端实现

### tool-project-service-web
该模块是 Web 服务实现，包含：
- Controller 层
- 配置类
- 拦截器
- 过滤器
- 应用启动类

### tool-project-service-biz
该模块包含核心业务逻辑实现，包含：
- 服务实现 (Service)
- 数据访问层 (DAO)
- 业务逻辑处理

## 模块依赖关系
- common: 基础模块，被其他所有模块依赖
- client: 依赖 common 模块
- biz: 依赖 common 模块
- web: 依赖 biz、client 和 common 模块
