/*
 * SynergyFacade.java
 * Copyright 2022 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.qunhe.diy.designinfoservice.client.clients.DesignInfoClient;
import com.qunhe.diy.designinfoservice.client.exception.DesignInfoServiceException;
import com.qunhe.diy.designinfoservice.common.data.user.DesignOwnerStatusResult;
import com.qunhe.diy.synergy.service.client.SynergyDesignInfoClient;
import com.qunhe.diy.synergy.service.common.exception.SynergyBizException;
import com.qunhe.utils.HunterLogger;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
public class SynergyFacade {

    private static final HunterLogger LOG = HunterLogger.getLogger(SynergyFacade.class);

    private final SynergyDesignInfoClient synergyDesignInfoClient;

    private final DesignInfoClient designInfoClient;

    @SentinelResource(value = "isSynergyDesign", blockHandler = "isSynergyDesignFallback")
    @SneakyThrows(SynergyBizException.class)
    public boolean isSynergyDesign(Long designId, Long userId) {
        return synergyDesignInfoClient.isSynergyDesign(designId, userId);
    }

    public boolean isSynergyDesignFallback(Long designId, Long userId,
            final BlockException e) {
        LOG.message("query isSynergyDesign - fallback", e)
                .with("designId", designId)
                .warn();
        return false;
    }

    @SneakyThrows(SynergyBizException.class)
    @SentinelResource(value = "isIsolateDesign", fallback = "isIsolateDesignFallback")
    public boolean isIsolateDesign(Long designId) {
        return synergyDesignInfoClient.isIsolateDesign(designId);
    }

    public boolean isIsolateDesignFallback(Long designId, final Throwable e) {
        LOG.message("query isIsolateDesign - fallback", e)
                .with("designId", designId)
                .warn();
        return false;
    }

    @Nullable
    @SneakyThrows(DesignInfoServiceException.class)
    @SentinelResource(value = "getDesignOwnerStatus", blockHandler = "getDesignOwnerStatusFallback")
    public DesignOwnerStatusResult getDesignOwnerStatus(Long designId) {
        return designInfoClient.getDesignOwnerStatus(designId);
    }

    public DesignOwnerStatusResult getDesignOwnerStatusFallback(Long designId, Throwable e) {
        LOG.message("getDesignOwnerStatus - fallback", e)
                .with("designId", designId)
                .error();
        return null;
    }

}
