/*
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.service.facade.SessionFacade;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipData;
import com.qunhe.diy.tool.project.service.web.page.errortip.ErrorTipType;
import com.qunhe.diybe.diyservice.common.data.SessionExistRes;
import com.qunhe.diybe.diyservice.common.data.SessionStatusInfo;
import com.qunhe.diybe.diyservice.common.data.enums.ClientSessionType;
import com.qunhe.diybe.diyservice.exception.DiyServiceException;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.HunterLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2024/7/16
 */
@Component
@RequiredArgsConstructor
public class SessionConflictHandler {

    private static final HunterLogger LOG = HunterLogger.getLogger(SessionConflictHandler.class);

    private static final String ERROR_TIP_PARAM_KEY = "id";

    private final ToadProperties toadProperties;

    private final SessionFacade sessionFacade;

    private final ErrorTipService errorTipService;

    private final UserDb userDb;


    @SentinelResource(value = "sessionConflictHandler", fallback = "checkSessionFallback")
    @Nullable
    @SneakyThrows(DiyServiceException.class)
    public URI checkSessionConflict(Long designId, Long userId, boolean isFop) {
        if (isFop) {
            return null;
        }
        SessionExistRes sessionExistRes = sessionFacade.querySessionStatus(designId);
        if (sessionExistRes == null || CollectionUtils.isEmpty(sessionExistRes.getInfo())) {
            return null;
        }
        SessionStatusInfo fopSession = sessionExistRes.getInfo().stream()
                .filter(session -> ClientSessionType.FOP.name().equals(session.getType())).findFirst().orElse(null);
        // 如果已经存在fop session，则返回错误提示页面url
        if (fopSession != null && fopSession.isExist()) {
            URI uri = buildErrorTipUri(fopSession);
            LOG.message("fop session conflict, will be redirect to error page")
                    .with("designId", designId)
                    .with("userId", userId)
                    .with("uri", uri)
                    .with("session", fopSession)
                    .info();
            return uri;
        }
        return null;
    }

    public URI checkSessionFallback(Long designId, Long userId, boolean isFop, Throwable e) {
        LOG.message("checkSessionFallback, session pass", e)
                .with("designId", designId)
                .with("userId", userId)
                .error();
        return null;
    }

    private URI buildErrorTipUri(SessionStatusInfo fopSession) {
        ErrorTipData errorTipData = new ErrorTipData();
        errorTipData.setErrorTipType(ErrorTipType.FOP_SESSION_CONFLICT);
        if (CollectionUtils.isNotEmpty(fopSession.getEditorUserIdList())) {
            UserDto user =
                    userDb.getUser(LongCipher.DEFAULT.decrypt(fopSession.getEditorUserIdList().iterator().next()));
            if (user != null) {
                errorTipData.setDescArgs(new Object[]{user.getUserName()});
            }
        }
        String id = errorTipService.save(errorTipData);
        return UriComponentsBuilder.newInstance()
                .uri(URI.create(toadProperties.getLoadingPageSessionErrorRedirectUrl()))
                .queryParam(ERROR_TIP_PARAM_KEY, id)
                .build().toUri();
    }

}
