/*
 * VrcService.java
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.service.facade.VrcAppIdDataDb;
import com.qunhe.diy.tool.project.service.common.data.VrcAppIdData;
import com.qunhe.log.QHLogger;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class VrcService {

    private static final QHLogger LOG = QHLogger.getLogger(VrcService.class);

    private static final String APP_STAGE = "APP_STAGE";

    @Autowired
    private VrcAppIdDataDb vrcAppIdDataDb;

    /**
     * 根据AppId和Vrc的V映射唯一的VrcCode
     * @param obsAppId
     * @param vrc
     * @return
     */
    public String getCorrectVrc(String obsAppId, String vrc) {
        final long appId = LongCipher.DEFAULT.decrypt(obsAppId);
        final int index = vrc.indexOf('R');
        return vrcAppIdDataDb.getVrcByVtypeAndAppId(
                vrc.substring(0, index), Math.toIntExact(appId), System.getenv(APP_STAGE));
    }

    public VrcAppIdData getByVTypeAndAppId(String vType, Long appId) {
        return vrcAppIdDataDb.getByVTypeAndAppId(
                vType, Math.toIntExact(appId), System.getenv(APP_STAGE));
    }

    public List<VrcAppIdData> getAvailableVrcAppIdList() {
        List<VrcAppIdData> vrcAppIdDataList =
                vrcAppIdDataDb.getAllVrcAppIdList(System.getenv(APP_STAGE)).getVrcAppIdDataList();
        LOG.message("available Vrc AppId List")
                .with("data", vrcAppIdDataList)
                .with("stage", System.getenv(APP_STAGE))
                .info();
        return vrcAppIdDataList;
    }

    public boolean skipAPCheck(VrcAppIdData vrcAppIdData) {
        return vrcAppIdData.getAccessPoint() <= 0;
    }

}
