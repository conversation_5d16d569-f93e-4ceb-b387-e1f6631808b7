/*
 * CoohomBimPageHandler.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.VrcMappingNotFoundException;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SynergyFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.DesignService;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.VrcRewriteService;
import com.qunhe.diy.tool.project.service.web.page.VrcService;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.log.QHLogger;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;

/**
 * <AUTHOR>
 * Coohom平台的页面处理器实现
 */
@Component
public class CoohomBimPageHandler extends AbstractBimPageHandler {

    private static final QHLogger LOG = QHLogger.getLogger(CoohomBimPageHandler.class);
    private static final String COOHOM_MY_DESIGN_PATH = "/pub/saas/apps/project/list";
    private static final String COOHOM_BIM_PATH = "/pub/tool/bim/cloud";

    public CoohomBimPageHandler(BusinessAccountDb businessAccountDb, DesignService designService,
            UserDb userDb, ProjectDesignFacadeDb projectDesignFacadeDb,
            SynergyFacade synergyFacade, VrcRewriteService vrcRewriteService,
            SessionConflictHandler sessionConflictHandler, ToadProperties toadProperties,
            SaaSConfigService saaSConfigService, ToolLinkConfigCache toolLinkConfigCache,
            UserInfoFacade userInfoFacade, BusinessAccessService businessAccessService,
            VrcService vrcService, AuthCheckService authCheckService) {
        super(businessAccountDb, designService, userDb, projectDesignFacadeDb, synergyFacade,
                vrcRewriteService, sessionConflictHandler, toadProperties, saaSConfigService,
                toolLinkConfigCache, userInfoFacade, businessAccessService, vrcService,
                authCheckService);
    }

    @Override
    protected ToolPageResult handleExistingDesign(HttpServletRequest request, BimPageParam param,
            Long userId, String obsDesignId, String levelId, H5Model h5Model) {
        final Long designId = LongCipher.DEFAULT.decrypt(obsDesignId);

        // 鉴权
        if (!authCheckService.checkAuthFromDesignId(userId, param.getOrderDesignId(),
                designId, param.isCooperate())) {
            return redirectCoohomDefaultPage(request);
        }


        final DiyDesignInfo yunDesign = projectDesignFacadeDb.getDiyDesignInfo(designId);

        if (yunDesign == null) {
            return ToolPageResult.create(org.springframework.http.HttpStatus.BAD_REQUEST, null, "design not exist");
        }

        // WARNING：planDb.getDiyDesignInfo 获取到的 vrc 有缓存，这里要去查最新的，避免vrc更新后(如bim切换到酷空间)，获取不到最新的值！
        final ProjectDesign projectDesignWithVrcAndParentId =
                projectDesignFacadeDb.getVrcAndParentId(yunDesign.getDesignId());
        String vrc = projectDesignWithVrcAndParentId == null
                ? null : projectDesignWithVrcAndParentId.getVrc();

        //特殊vrc版本进行修正
        try {
            vrc = vrcRewriteService.correctVrc(vrc, yunDesign.getPlanId(), param);
            h5Model.setVrc(vrc);
        } catch (VrcMappingNotFoundException e) {
            return ToolPageResult.create(org.springframework.http.HttpStatus.BAD_REQUEST, null, "update vrc error");
        }

        boolean isSynergy = synergyFacade.isSynergyDesign(designId, userId);

        fillDesignInfo(h5Model, obsDesignId, yunDesign, levelId, isSynergy,
                projectDesignWithVrcAndParentId == null ||
                        projectDesignWithVrcAndParentId.getCopyLogParentId() == null ? null
                        : Long.valueOf(projectDesignWithVrcAndParentId.getCopyLogParentId()));

        return null;
    }

    @Override
    protected ToolPageResult handleNewDesign(HttpServletRequest request, BimPageParam param,
            Long userId, Long rootAccountId) {
        try {
            if (!designService.isCoohomCreateDesignMatch(rootAccountId)) {
                final boolean isCoohom = authCheckService.checkCoohomUser(request, userId);
                // 权限点不通过的coohom用户，回coohom列表页
                if (isCoohom && !authCheckService.checkCoohomBimAuthPoint(userId)) {
                    LOG.message("redirect coohom default page when no coohom bim auth")
                            .with("obsAppId", param.getObsAppId())
                            .with("userId", userId)
                            .warn();
                    return redirectCoohomDefaultPage(request);
                }
            }
            return null;
        } catch (AccessAuthenticatorException e) {
            LOG.message("handleNewDesign - error checking auth", e)
                    .with("userId", userId)
                    .error();
            return ToolPageResult.create(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, null, "auth check error");
        }
    }
    /**
     * 重定向到coohom默认页面
     */
    private ToolPageResult redirectCoohomDefaultPage(final HttpServletRequest request) {
        try {
            final String originalHost = request.getHeader(ORIGINAL_HOST_HEADER_NAME);
            String host = "";
            if (StringUtils.isNotBlank(originalHost)) {
                host = "//" + originalHost;
            }
            final String url = host + COOHOM_MY_DESIGN_PATH;
            new URI(url); // Validate syntax
            return ToolPageResult.redirect(url);
        } catch (URISyntaxException e) {
            LOG.message("redirectCoohomDefaultPage - error")
                    .with(e)
                    .error();
            return ToolPageResult.create(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR, null, "redirect error");
        }
    }


} 