/*
 * GeneralProperties.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

//c4f2e7a43fa484f4dec46dddb5eb9136
package com.qunhe.diy.tool.project.service.biz.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "qunhe.tps")
@Component
public class GeneralProperties {
    private Boolean countCheck = false;
    private long countLimit = 5;


    /**
     * 方案保存时小区位置信息开关
     */
    private Boolean enableFphIpLocation = false;

    /**
     * 省市ip解析开放的创建入口
     */
    private List<Integer> legalSourceIdsForFphIpLocation = Arrays.asList(6,7);

    /**
     * 禁止游客访问
     */
    private boolean disableTrialUser = false;

    /**
     * 方案信息编辑-社区字段同步开关
     */
    private boolean commIdSyncByOceanus = false;

    /**
     * 批量逻辑删除/回滚彻底删除时，单次查询的方案数量（包含回收站）
     */
    private int singleQuerySizeForBatchOperate = 500;

    /**
     * 批量逻辑删除/回滚彻底删除时，单批操作的方案数量
     */
    private int maxPatitionSizeForBatchOperate = 30;

    /**
     * 方案删除后再次查询的间隔时间(es延迟时间)
     */
    private long maxGapTimeForBatchOperate = 1000L;

    /**
     * 允许批量操作的上游白名单
     */
    private List<String> legalVipForBatchOperation;

}

