/*
 * LevelDesignFacadeDbTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.qunhe.diybe.dms.client.LevelDesignClient;
import com.qunhe.diybe.dms.data.LevelDesignData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
@RunWith(MockitoJUnitRunner.class)
public class LevelDesignFacadeDbTest {

    @Mock
    private LevelDesignClient levelDesignClient;

    @InjectMocks
    private LevelDesignFacadeDb levelDesignFacadeDb;

    private LevelDesignData levelDesignData;
    private LevelDesignData levelDesignData1;
    private LevelDesignData levelDesignData2;

    @Before
    public void setUp() {
        levelDesignData = new LevelDesignData();
        levelDesignData.setLevelId("testLevelId");
        levelDesignData1 = new LevelDesignData();
        levelDesignData1.setLevelId("levelId1");
        levelDesignData2 = new LevelDesignData();
        levelDesignData2.setLevelId("levelId2");
    }

    @Test
    public void getLevelDesign() {
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(null);
        Assert.assertNull(null, levelDesign);

        String levelId = "MN5HNY5MDSK6YAABAAAAADA8";
        levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNull(null, levelDesign);

        List<LevelDesignData> levels = new ArrayList<>();
        levels.add(new LevelDesignData());
        when(levelDesignClient.getLevels(Collections.singletonList(levelId))).thenReturn(levels);
        levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNotNull(null, levelDesign);
    }

    @Test
    public void getLevelDesign_NullLevelId_ReturnsNull() {
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(null);
        Assert.assertNull("should null", levelDesign);
    }

    @Test
    public void getLevelDesign_EmptyLevelId_ReturnsNull() {
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign("");
        Assert.assertNull("should null", levelDesign);
    }

    @Test
    public void getLevelDesign_BlankLevelId_ReturnsNull() {
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign("   ");
        Assert.assertNull("should null", levelDesign);
    }

    @Test
    public void getLevelDesign_ValidLevelId_EmptyList_ReturnsNull() {
        String levelId = "MN5HNY5MDSK6YAABAAAAADA8";
        when(levelDesignClient.getLevels(Collections.singletonList(levelId))).thenReturn(
                Collections.emptyList());
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNull("should null", levelDesign);
    }

    @Test
    public void getLevelDesign_ValidLevelId_NonEmptyList_ReturnsFirstElement() {
        String levelId = "MN5HNY5MDSK6YAABAAAAADA8";
        List<LevelDesignData> levels = new ArrayList<>();
        LevelDesignData expectedLevelDesign = new LevelDesignData();
        levels.add(expectedLevelDesign);
        when(levelDesignClient.getLevels(Collections.singletonList(levelId))).thenReturn(levels);
        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNotNull("should not null", levelDesign);
        Assert.assertEquals("should equal", expectedLevelDesign, levelDesign);
    }

    @Test
    public void saveLevelDesign_SuccessfulSave_ReturnsTrue() {
        when(levelDesignClient.saveLevelDesign(levelDesignData)).thenReturn(true);

        boolean result = levelDesignFacadeDb.saveLevelDesign(levelDesignData);

        Assert.assertTrue("should equal", result);
    }

    @Test
    public void saveLevelDesign_FailedSave_ReturnsFalse() {
        when(levelDesignClient.saveLevelDesign(levelDesignData)).thenReturn(false);

        boolean result = levelDesignFacadeDb.saveLevelDesign(levelDesignData);

        Assert.assertFalse("should equal", result);
    }

    @Test
    public void batchGetLevelDesigns_NullLevelIds_ReturnsEmptyMap() {
        Map<String, LevelDesignData> result = levelDesignFacadeDb.batchGetLevelDesigns(null);
        Assert.assertTrue("should equal", result.isEmpty());
    }

    @Test
    public void batchGetLevelDesigns_EmptyLevelIds_ReturnsEmptyMap() {
        Map<String, LevelDesignData> result = levelDesignFacadeDb.batchGetLevelDesigns(
                Collections.emptyList());
        Assert.assertTrue("should equal", result.isEmpty());
    }

    @Test
    public void batchGetLevelDesigns_ValidLevelIds_ReturnsLevelDesignDataMap() {
        List<String> levelIds = Arrays.asList("levelId1", "levelId2");
        List<LevelDesignData> levelDesigns = Arrays.asList(levelDesignData1, levelDesignData2);
        when(levelDesignClient.getLevels(levelIds)).thenReturn(levelDesigns);

        Map<String, LevelDesignData> result = levelDesignFacadeDb.batchGetLevelDesigns(levelIds);

        Assert.assertEquals("should equal", 2, result.size());
        Assert.assertEquals("should equal", levelDesignData1, result.get("levelId1"));
        Assert.assertEquals("should equal", levelDesignData2, result.get("levelId2"));
    }

    @Test
    public void batchGetLevelDesigns_DuplicateLevelIds_ReturnsFirstEncountered() {
        List<String> levelIds = Arrays.asList("levelId1", "levelId1");
        List<LevelDesignData> levelDesigns = Arrays.asList(levelDesignData1);
        when(levelDesignClient.getLevels(levelIds)).thenReturn(levelDesigns);

        Map<String, LevelDesignData> result = levelDesignFacadeDb.batchGetLevelDesigns(levelIds);

        Assert.assertEquals("should equal", 1, result.size());
        Assert.assertEquals("should equal", levelDesignData1, result.get("levelId1"));
    }

    @Test
    public void getLevelDesign_NonNullLevelId_EmptyList_ReturnsNull() {
        String levelId = "MN5HNY5MDSK6YAABAAAAADA8";
        Mockito.when(levelDesignClient.getLevels(Collections.singletonList(levelId))).thenReturn(
                Collections.emptyList());

        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNull("should equal", levelDesign);
    }

    @Test
    public void getLevelDesign_NonNullLevelId_NonEmptyList_ReturnsFirstLevelDesign() {
        String levelId = "MN5HNY5MDSK6YAABAAAAADA8";
        List<LevelDesignData> levels = new ArrayList<>();
        LevelDesignData expectedLevelDesign = new LevelDesignData();
        levels.add(expectedLevelDesign);

        Mockito.when(levelDesignClient.getLevels(Collections.singletonList(levelId))).thenReturn(
                levels);

        LevelDesignData levelDesign = levelDesignFacadeDb.getLevelDesign(levelId);
        Assert.assertNotNull("should equal", levelDesign);
        Assert.assertEquals("should equal", expectedLevelDesign, levelDesign);
    }
}
