/*
 * CommunityService.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diy.tool.project.service.biz.db.CommunityDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua.SysDictAreaMapper;
import com.qunhe.diy.tool.project.service.biz.properties.GeneralProperties;
import com.qunhe.diy.tool.project.service.biz.service.facade.FphFloorplanService;
import com.qunhe.diy.tool.project.service.biz.service.facade.HasSwitchFacade;
import com.qunhe.diy.tool.project.service.common.constant.CommonConstant;
import com.qunhe.diy.tool.project.service.common.param.LocationParam;
import com.qunhe.diy.tool.project.service.common.param.ToolProjectSaveParam;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.house.property.floorplan.home.common.enums.AreaGradeEnum;
import com.qunhe.house.property.floorplan.home.common.model.AreaItemDTO;
import com.qunhe.hunter.helper.HunterContext;
import com.qunhe.hunter.helper.SpanContextHolder;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import com.qunhe.log.QHLogger;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.CognitiveComplexity"})
public class CommunityService {
    private static final QHLogger LOG = QHLogger.getLogger(CommunityService.class);

    static final String X_FORWARDED_FOR = "X-Forwarded-For";

    public static final String SWITCH_NAME = "fhpIpLocationSwitch";

    private final CommunityDb communityDb;

    private final SysDictAreaMapper sysDictAreaMapper;

    private final GeneralProperties generalProperties;

    private final ProjectDesignFacadeDb projectDesignFacadeDb;

    private final FphFloorplanService fphFloorplanService;

    private final HasSwitchFacade switchFacade;

    @Autowired
    public CommunityService(
            final CommunityDb communityDb,
            final SysDictAreaMapper sysDictAreaMapper,
            final GeneralProperties generalProperties,
            final ProjectDesignFacadeDb projectDesignFacadeDb,
            final FphFloorplanService fphFloorplanService,
            final HasSwitchFacade switchFacade) {
        this.communityDb = communityDb;
        this.sysDictAreaMapper = sysDictAreaMapper;
        this.generalProperties = generalProperties;
        this.projectDesignFacadeDb = projectDesignFacadeDb;
        this.fphFloorplanService = fphFloorplanService;
        this.switchFacade = switchFacade;
    }

    /**
     * 检查当前创建是否符合精准省市定位要求，若符合，则将commid修改成用户真实ip小区（小区名是"未知小区"）
     *
     * @param isCoohom
     * @param toolProjectSaveParam
     */
    @SentinelResource(value="updateCommidIfPreciseIpSupport", fallback = "updateCommidIfPreciseIpSupportFallback")
    public void updateCommidIfPreciseIpSupport(final boolean isCoohom,
            final ToolProjectSaveParam toolProjectSaveParam) {
        if (toolProjectSaveParam.getCommId() == null) {
            final LocationParam locationParam =
                    getLocationParam(isCoohom, toolProjectSaveParam.getUserId(),
                            toolProjectSaveParam.getSourceId());
            if (locationParam == null) {
                return;
            }
            //更新commid，其他社区字段由oceanus任务同步
            final Community community =
                    getOrCreateProjectCommunityByAreaIdAndName(
                            Long.valueOf(locationParam.getAreaId()), locationParam.getName());
            if (community == null) {
                LOG.message("community not found in fenshua123, please add document.")
                        .with("areaId", locationParam.getAreaId())
                        .with("name", locationParam.getName())
                        .error();
                return;
            }
            toolProjectSaveParam.setCommId(community.getCommId());
        }
    }

    public void updateCommidIfPreciseIpSupportFallback(final boolean isCoohom,
            final ToolProjectSaveParam toolProjectSaveParam, Throwable e) {
        LOG.message("updateCommidIfPreciseIpSupport failed", e)
                .with("commId", toolProjectSaveParam.getCommId())
                .with("userId", toolProjectSaveParam.getUserId())
                .error();
    }

    public Community getOrCreateProjectCommunity(final String province,
            final String city, final String communityName) {
        /* Find city */
        final Long areaId = communityDb.getAreaId(province, city);

        /* Find or create community */
        return getOrCreateProjectCommunityByAreaIdAndName(areaId, communityName);
    }

    public Community getOrCreateProjectCommunityByAreaIdAndName(final Long areaId,
            final String communityName) {
        return communityDb.getOrCreateCommunity(areaId, communityName);
    }

    public String getCommNameById(final Long commId) {
        return communityDb.getCommNameByCommId(commId);
    }

    public SysDictArea getSysDictArea(final Long commId) {
        return communityDb.getSysDictArea(commId);
    }

    public LocationParam getLocationParam(final boolean isCoohom, final Long designId,
            final Long userId, final String areaId) {
        final ProjectDesign projectDesign = projectDesignFacadeDb.getProjectByDesignId(
                designId);
        //非首次保存时，以已方案已有位置信息为准
        if (StringUtils.isNotEmpty(areaId)) {
            return LocationParam.builder().name(projectDesign.getCommunityName())
                    .areaId(projectDesign.getCommLogicAreaId())
                    .sysParentAreaId(projectDesign.getCommLogicProvinceId())
                    .build();
        }

        //首次保存，走ip解析
        final LocationParam ipParam = LocationParam.buildDefaultLocation();
        try {
            //开关未开 或 来自coohom的创建, 直接返回默认值
            if (!generalProperties.getEnableFphIpLocation() || isCoohom) {
                return ipParam;
            }
            //自由画/临摹图/cad创建方式走ip soa
            final Byte sourceId = projectDesign.getSourceId();
            final List<Integer> legalSourceIds =
                    generalProperties.getLegalSourceIdsForFphIpLocation();
            if (sourceId != null && !legalSourceIds.contains(sourceId.intValue())) {
                return ipParam;
            }

            //分桶使用
            final Boolean usefphIpLocation = switchFacade.isLegalUserIdOrAccountId(SWITCH_NAME, userId);

            //省市ip
            if (usefphIpLocation) {
                //获取客户端ip
                final HunterContext hunterContext =
                        SpanContextHolder.getCurrentTraceData().copyHunterContext();
                final String currentIp = hunterContext.get(X_FORWARDED_FOR);
                final AreaItemDTO areaItemDTO = fphFloorplanService.getIpLocation(currentIp);
                if (areaItemDTO != null) {
                    final LocationParam finalParam = calculateLocationParamByAreaGrade(areaItemDTO);
                    ipParam.setAreaId(finalParam.getAreaId());
                    ipParam.setSysParentAreaId(finalParam.getSysParentAreaId());
                }
            }
            return ipParam;
        } catch (Exception e) {
            //若发生任意异常，返回默认省市信息
            LOG.message("exception occurs while get LocationParam", e)
                    .with("isCoohom", isCoohom)
                    .with("designid", designId)
                    .with("userid", userId)
                    .with("areaId", areaId)
                    .error();
            return ipParam;
        }
    }

    public LocationParam getLocationParam(final boolean isCoohom, final Long userId,
            final Byte sourceId) {
        final LocationParam ipParam = LocationParam.buildDefaultLocation();
        try {
            //开关未开 或 来自coohom的创建, 直接返回默认值
            if (!generalProperties.getEnableFphIpLocation() || isCoohom) {
                return ipParam;
            }
            //自由画/临摹图/cad创建方式走ip soa
            final List<Integer> legalSourceIds = generalProperties.getLegalSourceIdsForFphIpLocation();
            if (sourceId != null && !legalSourceIds.contains(sourceId.intValue())) {
                return ipParam;
            }

            //分桶使用
            final Boolean usefphIpLocation = switchFacade.isLegalUserIdOrAccountId(SWITCH_NAME
                    , userId);

            //省市ip解析
            if (usefphIpLocation) {
                //获取客户端ip
                final HunterContext hunterContext =
                        SpanContextHolder.getCurrentTraceData().copyHunterContext();
                final String currentIp = hunterContext.get(X_FORWARDED_FOR);
                final AreaItemDTO areaItemDTO = fphFloorplanService.getIpLocation(currentIp);
                if (areaItemDTO != null) {
                    final LocationParam finalParam = calculateLocationParamByAreaGrade(areaItemDTO);
                    ipParam.setAreaId(finalParam.getAreaId());
                    ipParam.setSysParentAreaId(finalParam.getSysParentAreaId());
                }
            }
            return ipParam;
        } catch (Exception e) {
            //若发生任意异常，返回默认省市信息
            LOG.message("exception occurs while get LocationParam", e)
                    .with("isCoohom", isCoohom)
                    .with("userid", userId)
                    .with("sourceid", sourceId)
                    .error();
            return ipParam;
        }
    }

    /**
     * 根据ip soa接口返回的地区级别确认真实的ip地址
     * @param areaItemDTO
     * @return
     */
    public LocationParam calculateLocationParamByAreaGrade(final AreaItemDTO areaItemDTO) {
        final LocationParam ipParam = LocationParam.buildDefaultLocation();
        final Long areaId = areaItemDTO.getAreaId() == null ? ipParam.getAreaId() : areaItemDTO.getAreaId();

        AreaGradeEnum areaGrade = areaItemDTO.getAreaGrade();
        if (areaGrade == null) {
            areaGrade = AreaGradeEnum.CITY;
        }

        int provinceAreaId;
        switch (areaGrade) {
        case NATION:
            //暂时只支持中国，默认"浙江杭州"
            return ipParam;
        case STATE:
            //省市，获取该省中小区名是"未知小区"的commid
            final Community[] communities = communityDb.getCommunities(CommonConstant.DEFAULT_COMMUNITY_NAME,
                    null);
            if (communities.length > 0) {
                final int parentAreaId = getParentAreaId(communities[0].getAreaId());
                if (parentAreaId >= 0) {
                    ipParam.setAreaId(Math.toIntExact(communities[0].getAreaId()));
                    ipParam.setSysParentAreaId(parentAreaId);
                } else {
                    LOG.message("get provinceAreaId occurs error")
                            .with("AreaGradeEnum", AreaGradeEnum.STATE.name())
                            .with("areaId", communities[0].getAreaId())
                            .warn();
                }
            }
            return ipParam;
        case CITY:
            //获取省份信息 根据areaid返回SysdictArea
            provinceAreaId = getParentAreaId(areaId);
            if (provinceAreaId >= 0) {
                ipParam.setSysParentAreaId(provinceAreaId);
                ipParam.setAreaId(Math.toIntExact(areaId));
            } else {
                LOG.message("get provinceAreaId occurs error")
                        .with("AreaGradeEnum", AreaGradeEnum.CITY.name())
                        .with("areaId", areaId)
                        .warn();
            }
            return ipParam;
        case REGION:
            //TODO 查表两次优化
            /**
             * 按照区信息查找城市及其所在省份：
             * 如当前areaId表示西湖区，先按照西湖区areaId查找其所在城市的areaId（杭州areaId=175）；
             * 再根据杭州areaId查一次它所在的省份areaId(浙江省areaId=11)
             */
            final int cityAreaId = getParentAreaId(areaId);
            provinceAreaId = getParentAreaId((long) cityAreaId);
            if (cityAreaId >= 0 && provinceAreaId >= 0) {
                ipParam.setAreaId(cityAreaId);
                ipParam.setSysParentAreaId(provinceAreaId);
            } else {
                LOG.message("get cityAreaId or provinceAreaId occurs error")
                        .with("AreaGradeEnum", AreaGradeEnum.REGION.name())
                        .with("srcAreaId", areaId)
                        .with("cityAreaId", cityAreaId)
                        .with("provinceAreaId", provinceAreaId)
                        .warn();
            }
            return ipParam;
        default:
            return ipParam;
        }
    }

    int getParentAreaId(final Long areaId) {
        SysDictArea sysDictArea = sysDictAreaMapper.getById(areaId);
        if (sysDictArea != null && sysDictArea.getParentAreaId() != null) {
            return Math.toIntExact(sysDictArea.getParentAreaId());
        }
        LOG.message("parentAreaId is null")
                .with("areaId", areaId)
                .error();
        return -1;
    }

}
