/*
 * Copyright 2023 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.web.page.errortip;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/17
 */
@RequiredArgsConstructor
@Getter
public enum ErrorTipType {

    DEFAULT("errortip.default.title", "方案在别处打开", "errortip.default.desc", ""),

    FOP_SESSION_CONFLICT("errortip.fop.title", "该方案在别处打开", "errortip.fop.desc", "该方案关联的订单方案正在被{0}审核中，请稍后再打开"),


    ;

    private final String titleLocaleId;

    private final String titleFallbackMessage;

    private final String descLocaleId;

    private final String descFallbackMessage;

}
