package com.qunhe.diy.tool.project.service.web.page.handler;

import com.qunhe.diy.tool.project.service.biz.config.ToadProperties;
import com.qunhe.diy.tool.project.service.biz.db.BusinessAccountDb;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.db.UserDb;
import com.qunhe.diy.tool.project.service.biz.exception.VrcMappingNotFoundException;
import com.qunhe.diy.tool.project.service.biz.service.AuthCheckService;
import com.qunhe.diy.tool.project.service.biz.service.BusinessAccessService;
import com.qunhe.diy.tool.project.service.biz.service.SaaSConfigService;
import com.qunhe.diy.tool.project.service.biz.service.facade.SynergyFacade;
import com.qunhe.diy.tool.project.service.biz.service.facade.UserInfoFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.DesignService;
import com.qunhe.diy.tool.project.service.web.page.SessionConflictHandler;
import com.qunhe.diy.tool.project.service.web.page.ToolLinkConfigCache;
import com.qunhe.diy.tool.project.service.web.page.VrcRewriteService;
import com.qunhe.diy.tool.project.service.web.page.VrcService;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.data.DesignInfo;
import com.qunhe.diy.tool.project.service.web.page.data.H5Model;
import com.qunhe.diybe.dms.data.DiyDesignInfo;
import com.qunhe.diybe.dms.project.data.ProjectDesign;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.usergrowth.uic.data.dto.UserDto;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

import static com.qunhe.diy.tool.project.service.common.constant.CommonConstant.ORIGINAL_HOST_HEADER_NAME;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CoohomBimPageHandler 的单元测试类
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class CoohomBimPageHandlerTest {

    private CoohomBimPageHandler coohomBimPageHandler;

    @Mock
    private BusinessAccountDb businessAccountDb;
    @Mock
    private DesignService designService;
    @Mock
    private UserDb userDb;
    @Mock
    private ProjectDesignFacadeDb projectDesignFacadeDb;
    @Mock
    private SynergyFacade synergyFacade;
    @Mock
    private VrcRewriteService vrcRewriteService;
    @Mock
    private ToadProperties toadProperties;
    @Mock
    private SaaSConfigService saaSConfigService;
    @Mock
    private UserInfoFacade userInfoFacade;
    @Mock
    private AuthCheckService authCheckService;
    @Mock
    private SessionConflictHandler sessionConflictHandler;
    @Mock
    private ToolLinkConfigCache toolLinkConfigCache;
    @Mock
    private BusinessAccessService businessAccessService;
    @Mock
    private VrcService vrcService;

    @Mock
    private HttpServletRequest request;
    @Mock
    private BimPageParam param;
    @Mock
    private H5Model h5Model;

    // 静态 Mock 控制
    private MockedStatic<UserDb> userDbMockedStatic;


    private static final Long TEST_USER_ID = 12345L;
    private static final Long TEST_ROOT_ACCOUNT_ID = 67890L;

    private static final Long TEST_DESIGN_ID = 1L;
    private static final String TEST_OBS_DESIGN_ID = LongCipher.DEFAULT.encrypt(TEST_DESIGN_ID);
    private static final String TEST_LEVEL_ID = "levelId1";
    private static final String COOHOM_MY_DESIGN_PATH = "/pub/saas/apps/project/list";
    private static final String COOHOM_BIM_PATH = "/pub/tool/bim/cloud";

    @Before
    public void setUp() throws AccessAuthenticatorException {
        MockitoAnnotations.openMocks(this);
        userDbMockedStatic = mockStatic(UserDb.class);

        // 手动创建 CoohomBimPageHandler 实例
        coohomBimPageHandler = new CoohomBimPageHandler(
                businessAccountDb, designService, userDb, projectDesignFacadeDb,
                synergyFacade, vrcRewriteService, sessionConflictHandler, toadProperties,
                saaSConfigService, toolLinkConfigCache, userInfoFacade, businessAccessService,
                vrcService, authCheckService
        );

        lenient().when(toadProperties.getProjectStage()).thenReturn("test_stage");
        lenient().when(toadProperties.getVrcConfig()).thenReturn("test_vrc_config");
        lenient().when(
                        saaSConfigService.getAbTestResult(anyString(), any(), anyLong(),
                                anyString()))
                .thenReturn(Collections.emptyMap());

        UserDto mockUserDto = new UserDto();
        mockUserDto.setUserName("TestUserNick");
        when(userDb.getUserBySession()).thenReturn(mockUserDto);

        userDbMockedStatic.when(() -> UserDb.getUserIdBySession(false)).thenReturn(TEST_USER_ID);
        userDbMockedStatic.when(() -> UserDb.getUserIdBySession()).thenReturn(TEST_USER_ID);

        lenient().when(userInfoFacade.getUserInfoTags(TEST_USER_ID)).thenReturn(
                Collections.emptySet());
        lenient().when(userInfoFacade.getUserNameByUserId(TEST_USER_ID)).thenReturn("TestUserName");

        lenient().when(
                        saaSConfigService.checkBusinessAccessPoint(anyLong(), any(), any(),
                                anyList()))
                .thenReturn(true);

        lenient().when(businessAccountDb.getRootAccountForBAndC(TEST_USER_ID)).thenReturn(
                TEST_ROOT_ACCOUNT_ID);

        lenient().doNothing().when(h5Model).setDesignInfo(any(DesignInfo.class));
        lenient().doNothing().when(h5Model).setVrc(anyString());

        // 设置 vrcRewriteService 的默认行为
        lenient().when(vrcRewriteService.correctVrc(anyString(), anyLong(), any(BimPageParam.class)))
                .thenAnswer(invocation -> invocation.getArgument(0)); // 返回第一个参数（原始 vrc）
    }

    @After
    public void tearDown() {
        userDbMockedStatic.close();
        Mockito.clearAllCaches();
    }

    /**
     * 测试 handleExistingDesign 方法 - 鉴权失败场景
     * 预期：重定向到 Coohom 默认页面
     */
    @Test
    public void testHandleExistingDesign_AuthFailed_ShouldRedirectToCoohomDefault() {
        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID);
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(TEST_DESIGN_ID),
                anyBoolean())).thenReturn(false);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("test.coohom.com");

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertEquals("状态码应为 FOUND", HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("重定向URL应正确", "//test.coohom.com" + COOHOM_MY_DESIGN_PATH,
                result.getRedirectUrl());
        verify(projectDesignFacadeDb, never()).getDiyDesignInfo(anyLong());
        verify(designService, never()).getMainDesignId(anyLong(), anyLong());
    }


    /**
     * 测试 handleExistingDesign 方法 - 设计不存在
     * 预期：返回 BAD_REQUEST 状态和 "design not exist" 消息
     */
    @Test
    public void testHandleExistingDesign_DesignNotExist_ShouldReturnBadRequest() {
        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID);
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(TEST_DESIGN_ID),
                anyBoolean())).thenReturn(true);
        when(designService.getMainDesignId(TEST_DESIGN_ID, TEST_USER_ID)).thenReturn(
                TEST_DESIGN_ID);
        when(projectDesignFacadeDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(null);

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertEquals("状态码应为 BAD_REQUEST", HttpStatus.BAD_REQUEST.value(),
                result.getStatusCode());
        assertEquals("返回体应为 'design not exist'", "design not exist", result.getBody());
    }

    /**
     * 测试 handleExistingDesign 方法 - VRC 修正失败
     * 预期：抛异常 "update vrc error" 消息
     */
    @Test
    public void testHandleExistingDesign_VrcRewriteError_ReturnsBadRequestFromHandler()
            throws VrcMappingNotFoundException, AccessAuthenticatorException {
        DiyDesignInfo mockDiyDesignInfo = mock(DiyDesignInfo.class);
        ProjectDesign mockProjectDesign = mock(ProjectDesign.class);
        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID);
        when(param.getObsAppId()).thenReturn("mockObsAppIdForVrcTest");
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(TEST_DESIGN_ID),
                anyBoolean())).thenReturn(true);
        when(designService.getMainDesignId(TEST_DESIGN_ID, TEST_USER_ID)).thenReturn(
                TEST_DESIGN_ID);
        when(projectDesignFacadeDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(mockDiyDesignInfo);
        when(mockDiyDesignInfo.getDesignId()).thenReturn(TEST_DESIGN_ID);
        when(mockDiyDesignInfo.getPlanId()).thenReturn(100L);
        when(projectDesignFacadeDb.getVrcAndParentId(TEST_DESIGN_ID)).thenReturn(mockProjectDesign);
        when(mockProjectDesign.getVrc()).thenReturn("oldVrc");
        // 重置默认 mock 行为，设置特定的异常
        Mockito.reset(vrcRewriteService);
        when(vrcRewriteService.correctVrc("oldVrc", 100L, param))
            .thenThrow(new VrcMappingNotFoundException(100L, "oldVrc", "mockObsAppIdForVrcTest"));

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertEquals("状态码应为 BAD_REQUEST due to VrcError", HttpStatus.BAD_REQUEST.value(),
                result.getStatusCode());
        assertEquals("返回体应为 'update vrc error'", "update vrc error", result.getBody());
    }

    /**
     * 测试 handleExistingDesign 方法 - 成功获取设计信息，非协同，无父方案ID
     * 预期：返回 null (表示由父类继续处理并最终返回成功响应)
     */
    @Test
    public void testHandleExistingDesign_Success_NoSynergy_NoParentId() throws
            VrcMappingNotFoundException {
        DiyDesignInfo yunDesign = new DiyDesignInfo();
        yunDesign.setDesignId(TEST_DESIGN_ID);
        yunDesign.setPlanId(200L);
        yunDesign.setName("Test Design Name");
        yunDesign.setUserId(TEST_USER_ID);

        ProjectDesign projectDesignWithVrc = new ProjectDesign();
        projectDesignWithVrc.setVrc("correctedVrc");

        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID);
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(TEST_DESIGN_ID),
                anyBoolean())).thenReturn(true);
        when(designService.getMainDesignId(TEST_DESIGN_ID, TEST_USER_ID)).thenReturn(
                TEST_DESIGN_ID);
        when(projectDesignFacadeDb.getDiyDesignInfo(TEST_DESIGN_ID)).thenReturn(yunDesign);
        when(projectDesignFacadeDb.getVrcAndParentId(TEST_DESIGN_ID)).thenReturn(
                projectDesignWithVrc);
        when(vrcRewriteService.correctVrc(anyString(), eq(200L), eq(param))).thenReturn("correctedVrc");
        when(synergyFacade.isSynergyDesign(TEST_DESIGN_ID, TEST_USER_ID)).thenReturn(false);

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertNull("成功路径应返回 null", result);
        verify(h5Model, times(1)).setVrc(any()); // 验证 setVrc 被调用一次
        verify(synergyFacade, times(1)).isSynergyDesign(TEST_DESIGN_ID, TEST_USER_ID);
    }

    /**
     * 测试 handleExistingDesign 方法 - 成功获取设计信息，协同设计，有父方案ID，levelId为null，使用默认levelId
     * 预期：返回 null
     */
    @Test
    public void testHandleExistingDesign_Success_Synergy_WithParentId_NullLevelId_DefaultLevel()
            throws Exception {
        DiyDesignInfo yunDesign = new DiyDesignInfo();
        Long designId = TEST_DESIGN_ID;
        Long planId = 201L;
        Long copyParentId = 301L;
        String defaultLevelId = "defaultLevelFromYunDesign";
        String encryptedObsDesignId = TEST_OBS_DESIGN_ID;
        String encryptedCopyParentId = LongCipher.DEFAULT.encrypt(copyParentId);

        yunDesign.setDesignId(designId);
        yunDesign.setPlanId(planId);
        yunDesign.setName("Synergy Design");
        yunDesign.setUserId(TEST_USER_ID + 1);

        ProjectDesign projectDesignWithVrcAndParent = new ProjectDesign();
        projectDesignWithVrcAndParent.setVrc("vrcForSynergy");
        projectDesignWithVrcAndParent.setCopyLogParentId(copyParentId.intValue());

        when(param.getObsDesignId()).thenReturn(encryptedObsDesignId);
        when(param.getLevelId()).thenReturn(null);

        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(designId),
                anyBoolean())).thenReturn(true);

        when(designService.getMainDesignId(designId, TEST_USER_ID)).thenReturn(designId);
        DiyDesignInfo spyYunDesign = Mockito.spy(yunDesign);
        when(projectDesignFacadeDb.getDiyDesignInfo(designId)).thenReturn(spyYunDesign);
        Mockito.doReturn(defaultLevelId).when(spyYunDesign).generateLevelId();
        when(projectDesignFacadeDb.getVrcAndParentId(designId)).thenReturn(
                projectDesignWithVrcAndParent);
        when(vrcRewriteService.correctVrc(anyString(), eq(planId), eq(param))).thenReturn(
                "vrcForSynergy");
        when(synergyFacade.isSynergyDesign(designId, TEST_USER_ID)).thenReturn(true);
        when(synergyFacade.isIsolateDesign(designId)).thenReturn(false);
        when(designService.getLastOpenedLevelIdByPlanId(planId)).thenReturn("");

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, encryptedObsDesignId, null, h5Model);

        assertNull("成功路径应返回 null", result);
        verify(h5Model, times(1)).setVrc(any()); // 验证 setVrc 被调用一次
        verify(designService).getLastOpenedLevelIdByPlanId(planId);
        verify(spyYunDesign).generateLevelId();

        org.mockito.ArgumentCaptor<DesignInfo> designInfoCaptor =
                org.mockito.ArgumentCaptor.forClass(DesignInfo.class);
        verify(h5Model).setDesignInfo(designInfoCaptor.capture());
        DesignInfo capturedDesignInfo = designInfoCaptor.getValue();

        assertEquals("DesignInfo中的obsDesignId", encryptedObsDesignId,
                capturedDesignInfo.getObsDesignId());
        assertEquals("DesignInfo中的levelId", defaultLevelId, capturedDesignInfo.getLevelId());
        assertEquals("DesignInfo中的isSynergy", true, capturedDesignInfo.getIsSynergy());
        assertEquals("DesignInfo中的obsParentId", encryptedCopyParentId,
                capturedDesignInfo.getObsParentId());
        assertEquals("DesignInfo中的isOwner", false, capturedDesignInfo.getIsOwner());
        assertEquals("DesignInfo中的isSynergyIsolate", false,
                capturedDesignInfo.getIsSynergyIsolate());
    }

    /**
     * 测试 handleExistingDesign 方法 - 成功获取设计信息，levelId为null，使用上次打开的levelId
     * 预期：返回 null
     */
    @Test
    public void testHandleExistingDesign_Success_NullLevelId_LastOpenedLevel() throws Exception {
        DiyDesignInfo yunDesign = new DiyDesignInfo();
        Long designId = TEST_DESIGN_ID;
        Long planId = 202L;
        String lastOpenedLevelId = "lastOpenedLevel123";

        yunDesign.setDesignId(designId);
        yunDesign.setPlanId(planId);
        yunDesign.setName("Design With Last Opened Level");
        yunDesign.setUserId(TEST_USER_ID);

        ProjectDesign projectDesignWithVrc = new ProjectDesign();
        projectDesignWithVrc.setVrc("vrcForLastOpened");
        projectDesignWithVrc.setDesignId(designId);

        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(null);
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), eq(designId),
                anyBoolean())).thenReturn(true);
        when(designService.getMainDesignId(designId, TEST_USER_ID)).thenReturn(designId);
        when(projectDesignFacadeDb.getDiyDesignInfo(designId)).thenReturn(yunDesign);
        when(projectDesignFacadeDb.getVrcAndParentId(designId)).thenReturn(projectDesignWithVrc);
        // VRC 将通过 setUp 中的默认 mock 返回原始值 "vrcForLastOpened"
        when(synergyFacade.isSynergyDesign(designId, TEST_USER_ID)).thenReturn(false);
        when(designService.getLastOpenedLevelIdByPlanId(planId)).thenReturn(lastOpenedLevelId);
        when(designService.isValidLevelId(designId, lastOpenedLevelId)).thenReturn(true);

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param,
                TEST_USER_ID, TEST_OBS_DESIGN_ID, null, h5Model);

        assertEquals("成功路径应返回 null", null, result);
        verify(h5Model, times(1)).setVrc(any()); // 验证 setVrc 被调用一次
        verify(designService).getLastOpenedLevelIdByPlanId(planId);
        verify(designService).isValidLevelId(designId, lastOpenedLevelId);

        org.mockito.ArgumentCaptor<DesignInfo> designInfoCaptor =
                org.mockito.ArgumentCaptor.forClass(DesignInfo.class);
        verify(h5Model).setDesignInfo(designInfoCaptor.capture());
        DesignInfo capturedDesignInfo = designInfoCaptor.getValue();
        assertEquals("DesignInfo中的levelId应为上次打开的levelId", lastOpenedLevelId,
                capturedDesignInfo.getLevelId());
    }

    /**
     * 测试 handleNewDesign 方法 - isCoohomCreateDesignMatch 返回 true
     * 预期：返回 null (表示继续执行，不重定向)
     */
    @Test
    public void testHandleNewDesign_CoohomCreateMatch_ShouldReturnNull()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(true);

        ToolPageResult result = coohomBimPageHandler.handleNewDesign(request, param,
                TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        assertNull("当isCoohomCreateDesignMatch为true时，应返回null", result);

        verify(authCheckService, never()).checkCoohomUser(any(), anyLong());
        verify(authCheckService, never()).checkCoohomBimAuthPoint(anyLong());
    }

    /**
     * 测试 handleNewDesign 方法 - isCoohomCreateDesignMatch 返回 false, 用户非 Coohom 用户
     * 预期：返回 null
     */
    @Test
    public void testHandleNewDesign_NotCoohomCreateMatch_NotCoohomUser_ShouldReturnNull()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(false);
        when(authCheckService.checkCoohomUser(request, TEST_USER_ID)).thenReturn(false);
        ToolPageResult result = coohomBimPageHandler.handleNewDesign(request, param,
                TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        assertNull("非Coohom用户且开关关闭，应返回null", result);
        verify(authCheckService, never()).checkCoohomBimAuthPoint(anyLong());
    }

    /**
     * 测试 handleNewDesign 方法 - isCoohomCreateDesignMatch 返回 false, Coohom 用户但无 BIM 权限点
     * 预期：重定向到 Coohom 默认页面
     */
    @Test
    public void testHandleNewDesign_NotCoohomCreateMatch_CoohomUser_NoBimAuth_ShouldRedirect()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(false);
        when(authCheckService.checkCoohomUser(request, TEST_USER_ID)).thenReturn(true);
        when(authCheckService.checkCoohomBimAuthPoint(TEST_USER_ID)).thenReturn(false);
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("my.coohom.com");

        ToolPageResult result = coohomBimPageHandler.handleNewDesign(request, param,
                TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);

        assertEquals("状态码应为 FOUND", HttpStatus.FOUND.value(),
                result.getStatusCode());
        assertEquals("重定向URL应指向Coohom我的设计列表页",
                "//my.coohom.com" + COOHOM_MY_DESIGN_PATH,
                result.getRedirectUrl());
    }

    /**
     * 测试 handleNewDesign 方法 - isCoohomCreateDesignMatch 返回 false, Coohom 用户且有 BIM 权限点
     * 预期：返回 null
     */
    @Test
    public void testHandleNewDesign_NotCoohomCreateMatch_CoohomUser_WithBimAuth_ShouldReturnNull()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(false);
        when(authCheckService.checkCoohomUser(request, TEST_USER_ID)).thenReturn(true);
        when(authCheckService.checkCoohomBimAuthPoint(TEST_USER_ID)).thenReturn(true);

        ToolPageResult result = coohomBimPageHandler.handleNewDesign(request, param,
                TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);

        assertNull("Coohom用户有权限且开关关闭，应返回null", result);
    }



    /**
     * 测试私有方法 isCoohomCreateDesignMatch - 开关关闭
     */
    @Test
    public void testIsCoohomCreateDesignMatch_SwitchOff() throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(false);
        when(authCheckService.checkCoohomUser(request, TEST_USER_ID)).thenReturn(false);
        coohomBimPageHandler.handleNewDesign(request, param, TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        verify(authCheckService).checkCoohomUser(request, TEST_USER_ID);
    }

    /**
     * 测试私有方法 isCoohomCreateDesignMatch - 开关打开，用户在白名单
     */
    @Test
    public void testIsCoohomCreateDesignMatch_SwitchOn_UserInWhitelist()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(true);
        coohomBimPageHandler.handleNewDesign(request, param, TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        verify(authCheckService, never()).checkCoohomUser(any(), anyLong());
    }

    /**
     * 测试私有方法 isCoohomCreateDesignMatch - 开关打开，用户不在白名单，账户在白名单
     */
    @Test
    public void testIsCoohomCreateDesignMatch_SwitchOn_UserNotInWhitelist_AccountInWhitelist()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(true);
        coohomBimPageHandler.handleNewDesign(request, param, TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        verify(authCheckService, never()).checkCoohomUser(any(), anyLong());
    }

    /**
     * 测试私有方法 isCoohomCreateDesignMatch - 开关打开，白名单均不匹配
     */
    @Test
    public void testIsCoohomCreateDesignMatch_SwitchOn_NoWhitelistMatch()
            throws AccessAuthenticatorException {
        when(designService.isCoohomCreateDesignMatch(TEST_ROOT_ACCOUNT_ID)).thenReturn(false);
        coohomBimPageHandler.handleNewDesign(request, param, TEST_USER_ID, TEST_ROOT_ACCOUNT_ID);
        verify(authCheckService).checkCoohomUser(request, TEST_USER_ID);
    }
    

    /**
     * 测试 redirectCoohomDefaultPage 方法 - 包含 ORIGINAL_HOST_HEADER_NAME
     */
    @Test
    public void testRedirectCoohomDefaultPage_WithOriginalHost_Indirectly() {
        String originalHost = "original.host.com";
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(originalHost);
        // This setup makes handleExistingDesign call redirectCoohomDefaultPage
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), anyLong(), anyBoolean())).thenReturn(false);
        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID); // Needed by handleExistingDesign
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID); // Needed by handleExistingDesign

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param, TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertEquals("状态码应为 FOUND", HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("重定向 URI 应正确", "//" + originalHost + COOHOM_MY_DESIGN_PATH, result.getRedirectUrl());
    }

    /**
     * 测试 redirectCoohomDefaultPage 方法 - 不包含 ORIGINAL_HOST_HEADER_NAME
     */
    @Test
    public void testRedirectCoohomDefaultPage_NoOriginalHost_Indirectly() {
        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn(null);
        when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), anyLong(), anyBoolean())).thenReturn(false);
        when(param.getObsDesignId()).thenReturn(TEST_OBS_DESIGN_ID);
        when(param.getLevelId()).thenReturn(TEST_LEVEL_ID);

        ToolPageResult result = coohomBimPageHandler.handleExistingDesign(request, param, TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);

        assertEquals("状态码应为 FOUND", HttpStatus.FOUND.value(), result.getStatusCode());
        assertEquals("重定向 URI 应只包含路径", COOHOM_MY_DESIGN_PATH, result.getRedirectUrl());

        when(request.getHeader(ORIGINAL_HOST_HEADER_NAME)).thenReturn("");
        // Re-mock checkAuthFromDesignId for the second call if necessary, or ensure it's lenient
        lenient().when(authCheckService.checkAuthFromDesignId(eq(TEST_USER_ID), any(), anyLong(), anyBoolean())).thenReturn(false);
        result = coohomBimPageHandler.handleExistingDesign(request, param, TEST_USER_ID, TEST_OBS_DESIGN_ID, TEST_LEVEL_ID, h5Model);
        assertEquals("重定向 URI 应只包含路径 (空字符串host)", COOHOM_MY_DESIGN_PATH, result.getRedirectUrl());
    }
}