/*
 * ApiFactory.java
 * Copyright 2021 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.factory;

import com.qunhe.rpc.proxy.AdvancedDynamicProxy;
import com.qunhe.rpc.route.ApiRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component(value = "tpsApiFactory")
public class ApiFactory {

    private final ApiRegistry apiRegistry;

    @Autowired
    public ApiFactory(final ApiRegistry apiRegistry) {
        this.apiRegistry = apiRegistry;
    }

    public <T> T getApiInstance(final Class<T> clazz) {
        return AdvancedDynamicProxy.newInstance(clazz, apiRegistry);
    }

}

