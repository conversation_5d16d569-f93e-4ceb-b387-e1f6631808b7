/*
 * MqConfig.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.biz.config;

import com.qunhe.log.QHLogger;
import com.qunhe.middleware.rocketmq.client.QunheMQProducer;
import com.qunhe.middleware.rocketmq.client.QunheMQProducerFactory;
import org.apache.rocketmq.client.exception.MQClientException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Function: mq的配置
 *
 * <AUTHOR>
 */
@Configuration
public class MqConfig {

    private static final QHLogger LOG = QHLogger.getLogger(MqConfig.class);

    @Bean
    public QunheMQProducer userMQProducer(
            @Value("${app.feedback.group}") final String producerGroup,
            @Value("${app.feedback.rocketMqId}") final String rocketMqId)
            throws MQClientException, IOException {
        LOG.message("创建用户反馈生产者").with("group", producerGroup).with("rocket mq id", rocketMqId)
                .info();
        return QunheMQProducerFactory.createDefaultProducer(producerGroup, rocketMqId);
    }

    @Bean
    public QunheMQProducer designMQProducer(
            @Value("${app.design.group}") final String producerGroup,
            @Value("${app.design.rocketMqId}") final String rocketMqId)
            throws MQClientException, IOException {
        LOG.message("创建下游消息生产者").with("group", producerGroup).with("rocket mq id", rocketMqId)
                .info();
        return QunheMQProducerFactory.createDefaultProducer(producerGroup, rocketMqId);
    }

}