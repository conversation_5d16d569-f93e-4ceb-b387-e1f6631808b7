/*
 * FloorPlanMetaDataMapper.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.ddsdb;

import com.qunhe.diy.tool.project.service.common.data.FloorPlanMetaData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * Function:
 *
 * <AUTHOR>
 * 2024/5/31
 */
@Component
public interface FloorPlanMetaDataMapper {

    /**
     * 获取FloorPlanMetadata记录
     *
     * @param planId planId
     * @return FloorPlanMetadata
     */
    FloorPlanMetaData selectByPlanId(@Param("planId") final Long planId);

    /**
     * 保存门窗施工图新旧版本
     *
     * @param planId          planId
     * @param drawingVersion  门窗施工图新旧版本
     * @return 是否成功
     */
    int addOrUpdateDwDrawingVersion(@Param("planId") final Long planId,
                                    @Param("drawingVersion") final String drawingVersion);

}

