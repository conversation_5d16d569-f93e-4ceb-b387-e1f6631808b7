/*
 * CommunityMapper.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db.mapper.fenshua;

import com.qunhe.diy.tool.project.service.common.param.CommunityInfo;
import com.qunhe.instdeco.plan.data.Community;
import com.qunhe.instdeco.plan.data.SysDictArea;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@SuppressWarnings("PMD")
public interface CommunityMapper {
    /**
     * 获取全量社区相关信息
     * @param name 社区名称
     * @param areaId
     * @return
     */
    CommunityInfo getCommFullInfos(@Param("name") String name, @Param("areaId") int areaId);

    Community getDefaultCommunityByAreaId(Long var1);

    Community getDefaultCommunity(Long var1);

    SysDictArea getAreaByCommId(Long var1);

    Community[] getCommunitiesByNameAndAreaId(@Param("name") String var1,
            @Param("areaId") Long var2);

    int insertCommunity(Community var1);

    String getNameById(final long id);

}
