package com.qunhe.diy.tool.project.service.web.page;

import com.qunhe.diy.tool.project.service.biz.service.facade.TrialUserFacade;
import com.qunhe.diy.tool.project.service.common.data.ToolPageResult;
import com.qunhe.diy.tool.project.service.web.page.data.BimPageParam;
import com.qunhe.diy.tool.project.service.web.page.handler.CoohomBimPageHandler;
import com.qunhe.diy.tool.project.service.web.page.handler.KujialeBimPageHandler;
import com.qunhe.saas.commercialization.acl.sdk.exception.AccessAuthenticatorException;
import com.qunhe.utils.apiencrypt.cipher.LongCipher;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BimToolPageService 的单元测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class BimToolPageServiceTest {

    @InjectMocks
    private BimToolPageService bimToolPageService;

    @Mock
    private TrialUserFacade trialUserFacade;

    @Mock
    private KujialeBimPageHandler kujialePageHandler;

    @Mock
    private CoohomBimPageHandler coohomPageHandler;

    @Mock
    private HttpServletRequest mockRequest;


    // 预先构造 PageServiceResult 类型的对象
    private ToolPageResult kujialeServiceResult;
    private ToolPageResult coohomServiceResult;

    @Before
    public void setUp() throws AccessAuthenticatorException {
        // Initialize PageServiceResult objects
        kujialeServiceResult = ToolPageResult.success("kujiale");
        coohomServiceResult = ToolPageResult.success("coohom");

        // 使用 doReturn().when() 语法处理泛型
        doReturn(kujialeServiceResult).when(kujialePageHandler).handlePage(
                any(HttpServletRequest.class), any(BimPageParam.class));
        doReturn(coohomServiceResult).when(coohomPageHandler).handlePage(
                any(HttpServletRequest.class), any(BimPageParam.class));
    }


    /**
     * 测试 handlePageForBim 方法 - obsDesignId 为空, kujiale 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isEmpty_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 为空，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(null).trialUser(false).fromCoohom(
                false).build();

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的方法未被调用
        verify(trialUserFacade, never()).isTrialDesign(anyLong());
        verify(trialUserFacade, never()).markTrialDesignUpdated(anyLong());
        // 中文注释：验证 KujialeBimPageHandler 的 handlePage 被调用
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：验证 CoohomBimPageHandler 的 handlePage 未被调用
        verify(coohomPageHandler, never()).handlePage(mockRequest, param);
        // 中文注释：断言返回结果来自 Kujiale handler
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForBim 方法 - obsDesignId 为空, coohom 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isEmpty_coohom()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 为空，Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(null).trialUser(false).fromCoohom(
                true).build();

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的方法未被调用
        verify(trialUserFacade, never()).isTrialDesign(anyLong());
        verify(trialUserFacade, never()).markTrialDesignUpdated(anyLong());
        // 中文注释：验证 CoohomBimPageHandler 的 handlePage 被调用
        verify(coohomPageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：验证 KujialeBimPageHandler 的 handlePage 未被调用
        verify(kujialePageHandler, never()).handlePage(mockRequest, param);
        // 中文注释：断言返回结果来自 Coohom handler
        assertEquals("should equal", coohomServiceResult, response);
    }


    /**
     * 测试 handlePageForBim 方法 - obsDesignId 不为空，isTrialUser 为 true, kujiale 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isNotEmpty_isTrialUser_true_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，是试用用户，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId("obsId123").trialUser(true)
                .fromCoohom(false).build();

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 未被调用 (因为 !isTrialUser 为 false)
        verify(trialUserFacade, never()).isTrialDesign(anyLong());
        // 中文注释：验证 TrialUserFacade 的 markTrialDesignUpdated 未被调用
        verify(trialUserFacade, never()).markTrialDesignUpdated(anyLong());
        // 中文注释：验证 KujialeBimPageHandler 被调用
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForBim 方法 - obsDesignId 不为空，isTrialUser 为 false，isTrialDesign 返回 false,
     * kujiale 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isNotEmpty_isTrialUser_false_isTrialDesign_false_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，非试用用户，方案非试用方案，非 Coohom 来源
        BimPageParam param =
                BimPageParam.builder().obsDesignId(LongCipher.DEFAULT.encrypt(456L)).trialUser(
                        false).fromCoohom(false).build();
        when(trialUserFacade.isTrialDesign(456L)).thenReturn(false);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(456L);
        // 中文注释：验证 TrialUserFacade 的 markTrialDesignUpdated 未被调用
        verify(trialUserFacade, never()).markTrialDesignUpdated(anyLong());
        // 中文注释：验证 KujialeBimPageHandler 被调用
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForBim 方法 - obsDesignId 不为空，isTrialUser 为 false，isTrialDesign 返回 true,
     * kujiale 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isNotEmpty_isTrialUser_false_isTrialDesign_true_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，非试用用户，方案是试用方案，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(LongCipher.DEFAULT.encrypt(789L))
                .trialUser(false).fromCoohom(false).build();
        when(trialUserFacade.isTrialDesign(789L)).thenReturn(true);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(789L);
        // 中文注释：验证 TrialUserFacade 的 markTrialDesignUpdated 被调用
        verify(trialUserFacade, times(1)).markTrialDesignUpdated(789L);
        // 中文注释：验证 KujialeBimPageHandler 被调用
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForBim 方法 - obsDesignId 不为空，isTrialUser 为 false，isTrialDesign 返回 true, coohom 场景
     */
    @Test
    public void testHandlePageForBim_obsDesignId_isNotEmpty_isTrialUser_false_isTrialDesign_true_coohom()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，非试用用户，方案是试用方案，Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(LongCipher.DEFAULT.encrypt(789L))
                .trialUser(false).fromCoohom(true).build();
        when(trialUserFacade.isTrialDesign(789L)).thenReturn(true);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForBim(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(789L);
        // 中文注释：验证 TrialUserFacade 的 markTrialDesignUpdated 被调用
        verify(trialUserFacade, times(1)).markTrialDesignUpdated(789L);
        // 中文注释：验证 CoohomBimPageHandler 被调用
        verify(coohomPageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", coohomServiceResult, response);
    }


    /**
     * 测试 handlePageForTrial 方法 - obsDesignId 为空, kujiale 场景
     */
    @Test
    public void testHandlePageForTrial_obsDesignId_isEmpty_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 为空，是试用用户，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(null).trialUser(true).fromCoohom(
                false).userId(1L).build();

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForTrial(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 未被调用
        verify(trialUserFacade, never()).isTrialDesign(anyLong());
        // 中文注释：验证 KujialeBimPageHandler 的 handlePage 被调用，参数为原始 param
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForTrial 方法 - obsDesignId 不为空，isTrialUser 为 false, kujiale 场景
     */
    @Test
    public void testHandlePageForTrial_obsDesignId_isNotEmpty_isTrialUser_false_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，非试用用户，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId("trialObsId1").trialUser(false)
                .fromCoohom(false).userId(1L).build();

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForTrial(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 未被调用
        verify(trialUserFacade, never()).isTrialDesign(anyLong());
        // 中文注释：验证 KujialeBimPageHandler 被调用，参数为原始 param
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForTrial 方法 - obsDesignId 不为空，isTrialUser 为 true，isTrialDesign 返回 true,
     * kujiale 场景
     */
    @Test
    public void testHandlePageForTrial_obsDesignId_isNotEmpty_isTrialUser_true_isTrialDesign_true_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，是试用用户，方案是试用方案，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().obsDesignId(LongCipher.DEFAULT.encrypt(234L))
                .trialUser(true).fromCoohom(false).userId(1L).build();
        when(trialUserFacade.isTrialDesign(234L)).thenReturn(true);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForTrial(mockRequest, param);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(234L);
        // 中文注释：验证 KujialeBimPageHandler 被调用，参数为原始 param
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForTrial 方法 - obsDesignId 不为空，isTrialUser 为 true，isTrialDesign 返回 false,
     * kujiale 场景
     * 此场景会创建新的 BimPageParam
     */
    @Test
    public void testHandlePageForTrial_obsDesignId_isNotEmpty_isTrialUser_true_isTrialDesign_false_kujiale()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，是试用用户，方案非试用方案，非 Coohom 来源
        BimPageParam originalParam = BimPageParam.builder().obsDesignId(
                        LongCipher.DEFAULT.encrypt(345L)).trialUser(true).fromCoohom(false).userId(1L)
                .build();
        when(trialUserFacade.isTrialDesign(345L)).thenReturn(false);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForTrial(mockRequest,
                originalParam);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(345L);

        // 中文注释：验证 KujialeBimPageHandler 被调用，参数为新创建的 newParam (fromCoohom 默认为 false)
        verify(kujialePageHandler, times(1)).handlePage(eq(mockRequest), Mockito.argThat(
                newParam -> newParam.getUserId().equals(1L) &&
                        newParam.getObsDesignId() == null &&
                        !newParam.isFromCoohom() && // 验证新 param 的 fromCoohom 为 false
                        newParam.getRedirectUrl() == null // 验证其他字段在新param中是默认值
        ));
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }

    /**
     * 测试 handlePageForTrial 方法 - obsDesignId 不为空，isTrialUser 为 true，isTrialDesign 返回 false，且
     * fromCoohom 为 true
     * 此场景会创建新的 BimPageParam，其 fromCoohom 为 false，因此依然调用 kujialePageHandler
     */
    @Test
    public void testHandlePageForTrial_obsDesignId_isNotEmpty_isTrialUser_true_isTrialDesign_false_fromCoohom_true()
            throws AccessAuthenticatorException {
        // 中文注释：构造参数，obsDesignId 不为空，是试用用户，方案非试用方案，Coohom 来源
        BimPageParam originalParam = BimPageParam.builder().obsDesignId(
                        LongCipher.DEFAULT.encrypt(456L)).trialUser(true).fromCoohom(true).userId(1L)
                .build();
        when(trialUserFacade.isTrialDesign(456L)).thenReturn(false);

        // 中文注释：调用被测方法
        ToolPageResult response = bimToolPageService.handlePageForTrial(mockRequest,
                originalParam);

        // 中文注释：验证 TrialUserFacade 的 isTrialDesign 被调用
        verify(trialUserFacade, times(1)).isTrialDesign(456L);

        // 中文注释：验证 KujialeBimPageHandler 被调用，因为新创建的 param 的 fromCoohom 默认为 false
        verify(kujialePageHandler, times(1)).handlePage(eq(mockRequest), Mockito.argThat(
                newParam -> newParam.getUserId().equals(1L) &&
                        newParam.getObsDesignId() == null &&
                        !newParam.isFromCoohom()
        ));
        // 中文注释：验证 CoohomBimPageHandler 未被调用
        verify(coohomPageHandler, never()).handlePage(any(), any());
        // 中文注释：断言返回结果来自 Kujiale handler
        assertEquals("should equal", kujialeServiceResult, response);
    }


    /**
     * 测试 handlePage 方法 - fromCoohom 为 true
     */
    @Test
    public void testHandlePage_fromCoohom_true() throws AccessAuthenticatorException {
        // 中文注释：构造参数，Coohom 来源
        BimPageParam param = BimPageParam.builder().fromCoohom(true).build();
        // 中文注释：直接调用 handlePage 方法
        ToolPageResult response = bimToolPageService.handlePage(mockRequest, param);
        // 中文注释：验证 CoohomBimPageHandler 被调用
        verify(coohomPageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：验证 KujialeBimPageHandler 未被调用
        verify(kujialePageHandler, never()).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", coohomServiceResult, response);
    }

    /**
     * 测试 handlePage 方法 - fromCoohom 为 false
     */
    @Test
    public void testHandlePage_fromCoohom_false() throws AccessAuthenticatorException {
        // 中文注释：构造参数，非 Coohom 来源
        BimPageParam param = BimPageParam.builder().fromCoohom(false).build();
        // 中文注释：直接调用 handlePage 方法
        ToolPageResult response = bimToolPageService.handlePage(mockRequest, param);
        // 中文注释：验证 KujialeBimPageHandler 被调用
        verify(kujialePageHandler, times(1)).handlePage(mockRequest, param);
        // 中文注释：验证 CoohomBimPageHandler 未被调用
        verify(coohomPageHandler, never()).handlePage(mockRequest, param);
        // 中文注释：断言返回结果
        assertEquals("should equal", kujialeServiceResult, response);
    }
} 