/*
 * ToolProjectServiceApplication.java
 * Copyright 2018 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */
package com.qunhe.diy.tool.project.service.web;

import com.qunhe.diybe.module.cache.config.SummerCacheAutoConfiguration;
import com.qunhe.diybe.module.meter.thread.annotation.EnableMeterExecutor;
import com.qunhe.diybe.module.restapi.core.config.RestApiAutoConfig;
import com.qunhe.hunter.biz.annotation.EnableBizTrace;
import com.qunhe.instdeco.businessaccount.sdk.config.BusinessAccountSDKConfig;
import com.qunhe.middleware.hunter.starter.AutoHunter;
import com.qunhe.middleware.soa.starter.AutoSoa;
import com.qunhe.middleware.toad.spring.EnableToadAutoConfiguration;
import com.qunhe.project.platform.project.auth.security.AuthSecurityConfiguration;
import com.qunhe.rcsapi.config.RevisionAutoConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {
        "com.qunhe.diy.tool.project.service.web",
        "com.qunhe.diy.tool.project.service.biz",
        "com.qunhe.diy.tool.project.service.common",
        "com.qunhe.devops.controller",
        "com.qunhe.projectmanagement.client",
        "com.qunhe.instdeco.picservice.client",
        "com.qunhe.user.growth.floor.plan.cool.client",
        "com.qunhe.instdeco.plan.yuncoreapi.clients",
        "com.qunhe.i18n.locale.context",
        "com.qunhe.diybe.dms.client",
        "com.qunhe.saas.commercialization.acl.sdk.arc.client",
        "com.qunhe.project.platform.project.search.client",
        "com.qunhe.project.platform.project.auth.client",
        "com.qunhe.diy.project.service.client",
        "com.qunhe.house.property.floorplan.home.client",
        "com.qunhe.project.platform.project.search.client",
        "com.qunhe.diy.synergy.service.client",
        "com.qunhe.instdeco.plan.graylaunchmw.db",
        "com.qunhe.custom.dcs.order.client.client",
        "com.qunhe.instdeco.plan.graylaunchmw.infoparser",
}, exclude = { SolrAutoConfiguration.class,
        MongoAutoConfiguration.class, DataSourceAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class })
@AutoSoa
@AutoHunter
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@EnableConfigurationProperties
@EnableToadAutoConfiguration
@Import({ BusinessAccountSDKConfig.class,
        AuthSecurityConfiguration.class, com.qunhe.assembly.oplog.config.OpLogConfiguration.class,
        RevisionAutoConfig.class, RestApiAutoConfig.class, SummerCacheAutoConfiguration.class })
@EnableBizTrace
@EnableRetry
@EnableMeterExecutor
public class ToolProjectServiceApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ToolProjectServiceApplication.class);
    }

    public static void main(String[] args) {
        SpringApplication.run(ToolProjectServiceApplication.class, args);
    }
}
