/*
 * BatchOperatorInfo.java
 * Copyright 2024 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.common.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BatchOperatorVO {
    /**
     * 被操作人id
     */
    private Long userId;

    /**
     * 业务方标识/描述信息
     */
    private String description;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 请求来源的ip
     */
    private String ip;

    /**
     * 浏览器
     */
    private String userAgent;

    /**
     * 群核设备id
     */
    private String qhdi;

    @Override
    public String toString() {
        return "operatorId=" + operatorId + ", ip=" + ip + ", userAgent="
                + userAgent + ", qhdi=" + qhdi;
    }
}
