/*
 * BaseBizTest.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz;
import com.qunhe.diy.tool.project.service.biz.db.ProjectDesignFacadeDb;
import com.qunhe.diy.tool.project.service.biz.service.ProjectHandoverService;
import com.qunhe.diy.tool.project.service.biz.service.facade.AlbumService;
import com.qunhe.diy.tool.project.service.biz.service.facade.ProjectNotifyService;
import com.qunhe.diy.tool.project.service.common.constant.SoaConstant;
import com.qunhe.diybe.dms.client.project.DynamicProjectClient;
import com.qunhe.diybe.dms.client.project.ProjectClient;
import com.qunhe.instdeco.picservice.client.AlbumClient;
import com.qunhe.instdeco.plan.yun.core.client.apis.DecoProjectApi;
import com.qunhe.instdeco.plan.yuncoreapi.clients.DesignClient;
import com.qunhe.project.platform.project.search.client.DesignTagClient;
import com.qunhe.user.growth.floor.plan.cool.client.client.IncomingClient;
import com.qunhe.usergrowth.uic.rpc.client.UicUserInfoClient;
import com.qunhe.utils.crypt.LongCrypt;
import exacloud.AbstractRpcClientTest;

/**
 * <AUTHOR>
 */
public abstract class BaseBizTest extends AbstractRpcClientTest {
    protected  ProjectHandoverService projectHandoverService;
    protected  DesignTagClient designTagClient;
    protected  DynamicProjectClient dynamicProjectClient;
    protected  ProjectClient projectClient;
    protected  AlbumService albumService;
    protected  AlbumClient albumClient;
    protected DecoProjectApi decoProjectApi;


    protected  ProjectNotifyService projectNotifyService;
    protected  IncomingClient incomingClient;
    protected DesignClient designClient;
    protected  UicUserInfoClient uicUserInfoClient;
    protected  LongCrypt longCrypt;



    @Override
    protected String[] subscribedVips() {
        return new String[]{ SoaConstant.SERVICE_VIP };
    }

    @Override
    protected void initRpcClient() {
        //ProjectHandoverService
        ProjectDesignFacadeDb designFacadeDb = new ProjectDesignFacadeDb(dynamicProjectClient,projectClient);
        projectHandoverService = new ProjectHandoverService(designFacadeDb,designTagClient);

        //AlbumService
        albumClient = new AlbumClient(mApiRegistry);
        albumService = new AlbumService(albumClient);

    }
}
