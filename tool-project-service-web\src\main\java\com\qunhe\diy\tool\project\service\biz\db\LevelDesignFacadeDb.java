/*
 * LevelDesignFacadeDb.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.db;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.qunhe.diybe.dms.client.LevelDesignClient;
import com.qunhe.diybe.dms.data.LevelDesignData;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Function:
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
@Repository
@RequiredArgsConstructor
public class LevelDesignFacadeDb {

    private final LevelDesignClient levelDesignClient;

    public LevelDesignData getLevelDesign(final String levelId) {
        if (StringUtils.isBlank(levelId)) {
            return null;
        }
        List<LevelDesignData> levels = levelDesignClient.getLevels(
                Collections.singletonList(levelId));
        if (CollectionUtils.isNotEmpty(levels)) {
            return levels.get(0);
        }
        return null;
    }

    @SentinelResource(value = "batchGetLevelDesigns")
    public Map<String, LevelDesignData> batchGetLevelDesigns(final List<String> levelIds) {
        if (CollectionUtils.isEmpty(levelIds)) {
            return Collections.emptyMap();
        }
        return levelDesignClient.getLevels(levelIds).stream().collect(
                Collectors.toMap(l -> l.getLevelId(), l -> l, (a, b) -> a));
    }

    public boolean saveLevelDesign(final LevelDesignData levelDesignData) {
        return levelDesignClient.saveLevelDesign(levelDesignData);
    }

}
