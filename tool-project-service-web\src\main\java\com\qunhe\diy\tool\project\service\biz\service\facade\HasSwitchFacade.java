/*
 * HasSwitchFacade.java
 * Copyright 2025 Qunhe Tech, all rights reserved.
 * Qunhe PROPRIETARY/CONFIDENTIAL, any form of usage is subject to approval.
 */

package com.qunhe.diy.tool.project.service.biz.service.facade;

import com.qunhe.mdw.sth.core.bean.GenericSwitch;
import com.qunhe.mdw.sth.core.util.GenericSwitchUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * has-switch分桶
 * 接入文档：https://cf.qunhequnhe.com/pages/viewpage.action?pageId=***********
 * <AUTHOR>
 */
@Service
@SuppressWarnings({"PMD.CyclomaticComplexity", "PMD.AvoidComplexConditionRule"})
public class HasSwitchFacade {

    @Autowired
    private BusinessAccountFacade businessAccountFacade;

    /**
     * 规则设定：
     * 1、percentage 按userid最后一位尾号匹配,如percentage=20%表示尾号0，1的用户；
     * 2、kaPercentage 按accountId最后一位尾号匹配，同percentage
     * @param userId
     * @return
     */
    public boolean isLegalUserIdOrAccountId(final String switchName, final Long userId) {
        final GenericSwitch genericSwitch = GenericSwitchUtil.getSwitch(switchName);
        if (genericSwitch == null || userId == null) {
            return false;
        }
        //判断userid
        final Set<Long> whiteUserList = genericSwitch.getWhiteUserList();
        if (whiteUserList.contains(userId)) {
            return true;
        }
        final long lastUserId = userId % 10;
        final Integer percentage = genericSwitch.getPercentage();
        if (percentage != null && lastUserId < percentage) {
            return true;
        }

        //判断accountid
        final Set<Long> whiteAccountList = genericSwitch.getWhiteAccountList();
        final Long accountId = businessAccountFacade.getAccountId(userId);
        if (accountId != null) {
            final long lastAccountId = accountId % 10;
            final Integer kaPercentage = genericSwitch.getKaPercentage();
            if (whiteAccountList.contains(accountId) || (kaPercentage != null && lastAccountId < kaPercentage)) {
                return true;
            }
        }

        return false;
    }
}
